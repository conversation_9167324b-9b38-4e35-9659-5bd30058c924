<?php

namespace Botble\Hotel\Services;

use Botble\Base\Facades\BaseHelper;
use Botble\Hotel\Enums\BookingStatusEnum;
use Botble\Hotel\Events\BookingCreated;
use Botble\Hotel\Models\Booking;
use Botble\Hotel\Models\BookingAddress;
use Botble\Hotel\Models\BookingRoom;
use Botble\Hotel\Models\Room;
use Botble\Hotel\Models\RoomDate;
use Botble\Hotel\Models\RoomInventory;
use Botble\Payment\Enums\PaymentStatusEnum;
use Botble\Payment\Models\Payment;
use Botble\Payment\Repositories\Interfaces\PaymentInterface;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Throwable;

class CreateBookingService
{
    public function execute(array $data, array $bookingRooms = []): Booking|bool
    {
        $request = request();

        DB::beginTransaction();

        try {
            $booking = new Booking();
            $booking->fill($data);
            $booking->code = $this->generateBookingCode();
            $booking->status = $data['status'] ?? BookingStatusEnum::PENDING;
            $booking->token = md5(Str::random(40));
            $booking->transaction_id = Str::upper(Str::random(32));
            $booking->save();

            if (! empty($data['address']) && is_array($data['address'])) {
                $this->createBookingAddress($booking->id, $data['address']);
            }

            if (! empty($data['payment'])) {
                $this->createBookingPayment($booking, $data['payment']);
            }

            if ($bookingRooms) {
                $this->createBookingRooms($booking, $bookingRooms);
            } else {
                $this->storeBookingRooms($request, $booking);
            }

            // Important: We're not automatically assigning a room_id_code here
            // The room_id_code will be empty until an admin or staff manually assigns it

            event(new BookingCreated($booking));

            DB::commit();

            return $booking;
        } catch (Throwable $e) {
            DB::rollBack();

            BaseHelper::logError($e);

            return false;
        }
    }

    protected function generateBookingCode(): string
    {
        $prefix = 'MRR-';
        $nextInsertId = Booking::query()->max('id') + 1;

        do {
            $code = sprintf('%s%d', $prefix, $nextInsertId);
            $nextInsertId++;
        } while (Booking::query()->where('code', $code)->exists());

        return $code;
    }

    protected function createBookingAddress(int|string $bookingId, array $address): void
    {
        $address['booking_id'] = $bookingId;
        BookingAddress::query()->create($address);
    }

    protected function createBookingPayment(Booking $booking, array $paymentData): void
    {
        $paymentData['currency'] = $paymentData['currency'] ?? get_application_currency()->title;

        $payment = app(PaymentInterface::class)->createOrUpdate([
            'amount' => $booking->amount,
            'currency' => $paymentData['currency'],
            'charge_id' => $paymentData['charge_id'] ?? Str::upper(Str::random(10)),
            'payment_channel' => $paymentData['payment_channel'] ?? 'cash',
            'status' => $paymentData['status'] ?? PaymentStatusEnum::PENDING,
            'payment_type' => 'direct',
            'order_id' => $booking->id,
            'customer_id' => $booking->customer_id,
            'customer_type' => Customer::class,
        ]);

        $booking->payment_id = $payment->id;
        $booking->save();
    }

    protected function createBookingRooms(Booking $booking, array $bookingRooms): void
    {
        foreach ($bookingRooms as $bookingRoom) {
            $bookingRoomData = [
                'booking_id' => $booking->id,
                'room_id' => $bookingRoom['id'],
                'room_name' => $bookingRoom['name'],
                'room_image' => $bookingRoom['image'] ?? null,
                'price' => $bookingRoom['price'] ?? 0,
                'currency_id' => $bookingRoom['currency_id'] ?? get_application_currency_id(),
                'number_of_rooms' => $bookingRoom['number_of_rooms'] ?? 1,
                'start_date' => $bookingRoom['start_date'],
                'end_date' => $bookingRoom['end_date'],
                'tax_amount' => $bookingRoom['tax_amount'] ?? 0,
            ];

            $room = BookingRoom::query()->create($bookingRoomData);

            if (! $room) {
                continue;
            }

            $startDate = Carbon::parse($bookingRoom['start_date']);
            $endDate = Carbon::parse($bookingRoom['end_date']);

            $dates = [];
            for ($i = $startDate; $i->lte($endDate); $i->addDay()) {
                $dates[] = [
                    'booking_id' => $booking->id,
                    'booking_room_id' => $room->id,
                    'room_id' => $bookingRoom['id'],
                    'room_id_code' => null, // Important: We're not automatically assigning a room_id_code
                    'start_date' => $i->format('Y-m-d'),
                    'end_date' => $i->format('Y-m-d'),
                    'price' => $bookingRoom['price'] ?? 0,
                    'is_booked' => false, // Important: We're not marking the room as booked until a room_id_code is assigned
                    'active' => true,
                    'created_at' => Carbon::now(),
                    'updated_at' => Carbon::now(),
                ];
            }

            RoomDate::query()->insert($dates);
        }
    }

    protected function storeBookingRooms($request, Booking $booking): void
    {
        $bookingData = $request->input('booking');
        $startDate = Carbon::parse($bookingData['start_date']);
        $endDate = Carbon::parse($bookingData['end_date']);
        $nights = $startDate->diffInDays($endDate);

        $room = Room::query()->find($bookingData['room_id']);

        if (! $room) {
            throw new Exception(__('Room not found!'));
        }

        $roomBooking = BookingRoom::query()->create([
            'booking_id' => $booking->id,
            'room_id' => $room->id,
            'room_name' => $room->name,
            'room_image' => $room->image,
            'price' => $bookingData['amount'],
            'number_of_rooms' => 1,
            'start_date' => $startDate->format('Y-m-d'),
            'end_date' => $endDate->format('Y-m-d'),
            'tax_amount' => 0,
        ]);

        $dates = [];
        for ($i = $startDate; $i->lte($endDate); $i->addDay()) {
            $dates[] = [
                'booking_id' => $booking->id,
                'booking_room_id' => $roomBooking->id,
                'room_id' => $room->id,
                'room_id_code' => null, // Important: We're not automatically assigning a room_id_code
                'start_date' => $i->format('Y-m-d'),
                'end_date' => $i->format('Y-m-d'),
                'price' => $bookingData['amount'] / $nights,
                'is_booked' => false, // Important: We're not marking the room as booked until a room_id_code is assigned
                'active' => true,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ];
        }

        RoomDate::query()->insert($dates);
    }
}
