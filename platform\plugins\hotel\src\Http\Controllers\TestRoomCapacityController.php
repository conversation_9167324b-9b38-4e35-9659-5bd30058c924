<?php

namespace Botble\Hotel\Http\Controllers;

use Botble\Base\Http\Controllers\BaseController;
use Botble\Hotel\Services\RoomCapacityService;
use Illuminate\Http\Request;

class TestRoomCapacityController extends BaseController
{
    /**
     * Test the RoomCapacityService
     */
    public function testRoomCapacity(Request $request, RoomCapacityService $roomCapacityService)
    {
        $this->pageTitle('Test Room Capacity Service');

        // Test cases for different combinations of adults and children
        $testCases = [
            ['adults' => 1, 'children' => 2, 'expected' => ['Standard Room']],
            ['adults' => 1, 'children' => 3, 'expected' => ['Double Bed Room']],
            ['adults' => 3, 'children' => 0, 'expected' => ['Double Bed Room']],
            ['adults' => 3, 'children' => 1, 'expected' => ['Double Bed Room']],
            ['adults' => 2, 'children' => 0, 'expected' => ['Standard Room', 'Double Bed Room', 'Luxury Room']],
            ['adults' => 2, 'children' => 1, 'expected' => ['Standard Room', 'Double Bed Room']],
            ['adults' => 2, 'children' => 2, 'expected' => ['Double Bed Room']],
            ['adults' => 4, 'children' => 0, 'expected' => []],
            ['adults' => 5, 'children' => 0, 'expected' => []],
        ];

        $results = [];

        foreach ($testCases as $index => $testCase) {
            $adults = $testCase['adults'];
            $children = $testCase['children'];
            $expected = $testCase['expected'];
            
            $result = $roomCapacityService->getMatchingRoomTypes($adults, $children);
            
            $passed = count(array_diff($result, $expected)) === 0 && count(array_diff($expected, $result)) === 0;
            
            $results[] = [
                'case' => $index + 1,
                'adults' => $adults,
                'children' => $children,
                'expected' => $expected,
                'actual' => $result,
                'passed' => $passed,
            ];
        }

        return view('plugins/hotel::test-room-capacity', compact('results'));
    }
}
