<?php

namespace Bo<PERSON>ble\Theme\ThemeOption\Fields;

use Bo<PERSON>ble\Theme\Concerns\ThemeOption\Fields\HasOptions;
use Bo<PERSON>ble\Theme\ThemeOption\ThemeOptionField;

class SelectField extends ThemeOptionField
{
    use HasOptions;

    public function fieldType(): string
    {
        return 'customSelect';
    }

    public function toArray(): array
    {
        return [
            ...parent::toArray(),
            'attributes' => [
                ...parent::toArray()['attributes'],
                'choices' => $this->options,
            ],
        ];
    }
}
