@extends(BaseHelper::getAdminMasterLayoutTemplate())

@push('header-action')
    <div class="btn-group">
        <x-core::button
            type="button"
            color="primary"
            :outlined="true"
            class="date-range-picker"
            data-format-value="{{ trans('plugins/hotel::booking-reports.date_range_format_value', ['from' => '__from__', 'to' => '__to__']) }}"
            data-format="{{ Str::upper(config('core.base.general.date_format.js.date')) }}"
            data-href="{{ route('booking.reports.index') }}"
            data-start-date="{{ $startDate }}"
            data-end-date="{{ $endDate }}"
            icon="ti ti-calendar"
        >
            {{ trans('plugins/hotel::booking-reports.date_range_format_value', [
                'from' => BaseHelper::formatDate($startDate),
                'to' => BaseHelper::formatDate($endDate),
            ]) }}
        </x-core::button>

        <x-core::button
            type="button"
            tag="a"
            href="{{ route('booking.reports.export', ['start_date' => $startDate, 'end_date' => $endDate]) }}"
            icon="ti ti-download"
            color="success"
            :outlined="true"
        >
            {{ trans('plugins/hotel::booking.export_report') }}
        </x-core::button>

        <x-core::button
            type="button"
            tag="a"
            href="{{ route('booking.reports.advanced') }}"
            icon="ti ti-chart-bar"
            color="info"
            :outlined="true"
        >
            {{ trans('plugins/hotel::booking.advanced_reports') }}
        </x-core::button>
    </div>
@endpush

@section('content')
    <!-- Dashboard Header with Summary -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card bg-light border-0">
                <div class="card-body p-4">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <h4 class="mb-1">{{ trans('plugins/hotel::booking.reports') }}</h4>
                            <p class="text-muted mb-0">{{ BaseHelper::formatDate($startDate) }} - {{ BaseHelper::formatDate($endDate) }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Cards -->
    <div class="row mb-4">
        <!-- Bookings Card -->
        <div class="col-md-3 mb-3">
            <div class="card hover-translate-y shadow-sm border-0 h-100">
                <div class="card-body p-4">
                    <div class="row align-items-center">
                        <div class="col-auto">
                            <div class="bg-primary text-white p-3 rounded-circle">
                                <x-core::icon name="ti ti-calendar" size="md" />
                            </div>
                        </div>
                        <div class="col mt-0">
                            <p class="text-secondary mb-0 fs-4">
                                {{ trans('plugins/hotel::booking-reports.bookings') }}
                            </p>
                            <h3 class="mb-n1 fs-1 fw-bold">
                                {{ $totalBookings }}
                            </h3>
                        </div>
                    </div>
                    <div class="progress mt-3 mb-1" style="height: 6px;">
                        <div class="progress-bar bg-primary" role="progressbar" style="width: 100%;" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Revenue Card -->
        <div class="col-md-3 mb-3">
            <div class="card hover-translate-y shadow-sm border-0 h-100">
                <div class="card-body p-4">
                    <div class="row align-items-center">
                        <div class="col-auto">
                            <div class="bg-success text-white p-3 rounded-circle">
                                <x-core::icon name="ti ti-cash" size="md" />
                            </div>
                        </div>
                        <div class="col mt-0">
                            <p class="text-secondary mb-0 fs-4">
                                {{ trans('plugins/hotel::booking-reports.revenue') }}
                            </p>
                            <h3 class="mb-n1 fs-1 fw-bold">
                                {{ format_price($totalRevenue) }}
                            </h3>
                        </div>
                    </div>
                    <div class="progress mt-3 mb-1" style="height: 6px;">
                        <div class="progress-bar bg-success" role="progressbar" style="width: 100%;" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Customers Card -->
        <div class="col-md-3 mb-3">
            <div class="card hover-translate-y shadow-sm border-0 h-100">
                <div class="card-body p-4">
                    <div class="row align-items-center">
                        <div class="col-auto">
                            <div class="bg-info text-white p-3 rounded-circle">
                                <x-core::icon name="ti ti-users" size="md" />
                            </div>
                        </div>
                        <div class="col mt-0">
                            <p class="text-secondary mb-0 fs-4">
                                {{ trans('plugins/hotel::booking-reports.customers') }}
                            </p>
                            <h3 class="mb-n1 fs-1 fw-bold">
                                {{ $totalCustomers }}
                            </h3>
                        </div>
                    </div>
                    <div class="progress mt-3 mb-1" style="height: 6px;">
                        <div class="progress-bar bg-info" role="progressbar" style="width: 100%;" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Rooms Card -->
        <div class="col-md-3 mb-3">
            <div class="card hover-translate-y shadow-sm border-0 h-100">
                <div class="card-body p-4">
                    <div class="row align-items-center">
                        <div class="col-auto">
                            <div class="bg-warning text-white p-3 rounded-circle">
                                <x-core::icon name="ti ti-bed" size="md" />
                            </div>
                        </div>
                        <div class="col mt-0">
                            <p class="text-secondary mb-0 fs-4">
                                {{ trans('plugins/hotel::booking-reports.rooms') }}
                            </p>
                            <h3 class="mb-n1 fs-1 fw-bold">
                                {{ $totalRooms }}
                            </h3>
                        </div>
                    </div>
                    <div class="progress mt-3 mb-1" style="height: 6px;">
                        <div class="progress-bar bg-warning" role="progressbar" style="width: 100%;" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Booking Trend Chart -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white d-flex justify-content-between align-items-center py-3">
                    <h5 class="mb-0">{{ trans('plugins/hotel::booking.booking_trend') }}</h5>
                </div>
                <div class="card-body">
                    <div id="booking-trend-chart" style="height: 350px;"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Room Availability and Status Distribution -->
    <div class="row mb-4">
        <!-- Room Availability -->
        <div class="col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-white d-flex justify-content-between align-items-center py-3">
                    <h5 class="mb-0">{{ trans('plugins/hotel::booking.room_availability') }}</h5>
                </div>
                <div class="card-body">
                    <div id="room-availability-chart" style="height: 300px;"></div>
                </div>
            </div>
        </div>

        <!-- Booking Status Distribution -->
        <div class="col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-white d-flex justify-content-between align-items-center py-3">
                    <h5 class="mb-0">{{ trans('plugins/hotel::booking.booking_status_distribution') }}</h5>
                </div>
                <div class="card-body">
                    <div id="booking-status-chart" style="height: 300px;"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Bookings Table -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white d-flex justify-content-between align-items-center py-3">
                    <h5 class="mb-0">{{ trans('plugins/hotel::booking.recent_bookings') }}</h5>
                    <a href="{{ route('booking.index') }}" class="btn btn-sm btn-primary">
                        {{ trans('plugins/hotel::booking.view_all') }}
                    </a>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="bg-light">
                                <tr>
                                    <th class="py-3">{{ trans('core/base::tables.id') }}</th>
                                    <th class="py-3">{{ trans('plugins/hotel::booking.customer') }}</th>
                                    <th class="py-3">{{ trans('plugins/hotel::booking.room') }}</th>
                                    <th class="py-3">{{ trans('plugins/hotel::booking.room_id_code') }}</th>
                                    <th class="py-3">{{ trans('plugins/hotel::booking.amount') }}</th>
                                    <th class="py-3">{{ trans('plugins/hotel::booking.status') }}</th>
                                    <th class="py-3">{{ trans('core/base::tables.created_at') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($recentBookings as $booking)
                                    <tr>
                                        <td>{{ $booking->id }}</td>
                                        <td>
                                            @if($booking->customer)
                                                {{ $booking->customer->first_name }} {{ $booking->customer->last_name }}
                                            @else
                                                N/A
                                            @endif
                                        </td>
                                        <td>
                                            @if($booking->room)
                                                {{ $booking->room->room_name }}
                                            @else
                                                N/A
                                            @endif
                                        </td>
                                        <td>{{ $booking->room_id_code ?? 'N/A' }}</td>
                                        <td>{{ format_price($booking->amount) }}</td>
                                        <td>
                                            <span class="badge bg-{{ $booking->status->getColor() }}">
                                                {{ $booking->status->label() }}
                                            </span>
                                        </td>
                                        <td>{{ BaseHelper::formatDate($booking->created_at) }}</td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('footer')
    <script>
        var BotbleVariables = BotbleVariables || {};
        BotbleVariables.languages = BotbleVariables.languages || {};
        BotbleVariables.languages.reports = {!! json_encode(trans('plugins/hotel::booking-reports.ranges'), JSON_HEX_APOS) !!}

        document.addEventListener('DOMContentLoaded', function() {
            // Booking Trend Chart
            var bookingTrendOptions = {
                series: [{
                    name: 'Bookings',
                    data: [{{ implode(',', $chartData) }}]
                }],
                chart: {
                    height: 350,
                    type: 'area',
                    toolbar: {
                        show: false
                    },
                    fontFamily: 'inherit',
                    animations: {
                        enabled: true,
                        easing: 'easeinout',
                        speed: 800,
                        animateGradually: {
                            enabled: true,
                            delay: 150
                        },
                        dynamicAnimation: {
                            enabled: true,
                            speed: 350
                        }
                    }
                },
                colors: ['#3b82f6'],
                fill: {
                    type: 'gradient',
                    gradient: {
                        shadeIntensity: 1,
                        opacityFrom: 0.7,
                        opacityTo: 0.3,
                        stops: [0, 90, 100]
                    }
                },
                dataLabels: {
                    enabled: false
                },
                stroke: {
                    curve: 'smooth',
                    width: 3
                },
                xaxis: {
                    type: 'datetime',
                    categories: [
                        @foreach(array_keys($bookingTrends) as $date)
                            '{{ $date }}',
                        @endforeach
                    ]
                },
                tooltip: {
                    x: {
                        format: 'dd MMM yyyy'
                    }
                }
            };

            var bookingTrendChart = new ApexCharts(document.querySelector("#booking-trend-chart"), bookingTrendOptions);
            bookingTrendChart.render();

            // Room Availability Chart
            var roomAvailabilityOptions = {
                series: [{{ $roomAvailability['available'] }}, {{ $roomAvailability['booked'] }}],
                chart: {
                    width: '100%',
                    type: 'pie',
                    fontFamily: 'inherit'
                },
                labels: ['Available', 'Booked'],
                colors: ['#10b981', '#f43f5e'],
                legend: {
                    position: 'bottom'
                },
                responsive: [{
                    breakpoint: 480,
                    options: {
                        chart: {
                            width: 300
                        },
                        legend: {
                            position: 'bottom'
                        }
                    }
                }],
                tooltip: {
                    y: {
                        formatter: function(value) {
                            return value + ' rooms';
                        }
                    }
                }
            };

            var roomAvailabilityChart = new ApexCharts(document.querySelector("#room-availability-chart"), roomAvailabilityOptions);
            roomAvailabilityChart.render();

            // Booking Status Distribution Chart
            var bookingStatusOptions = {
                series: [
                    @foreach($bookingStatusStats as $status => $count)
                        {{ $count }},
                    @endforeach
                ],
                chart: {
                    width: '100%',
                    type: 'donut',
                    fontFamily: 'inherit'
                },
                labels: [
                    @foreach($bookingStatusStats as $status => $count)
                        '{{ BookingStatusEnum::getLabel($status) }}',
                    @endforeach
                ],
                colors: [
                    @foreach($bookingStatusStats as $status => $count)
                        '{{ BookingStatusEnum::getColor($status) }}',
                    @endforeach
                ],
                legend: {
                    position: 'bottom'
                },
                responsive: [{
                    breakpoint: 480,
                    options: {
                        chart: {
                            width: 300
                        },
                        legend: {
                            position: 'bottom'
                        }
                    }
                }],
                tooltip: {
                    y: {
                        formatter: function(value) {
                            return value + ' bookings';
                        }
                    }
                }
            };

            var bookingStatusChart = new ApexCharts(document.querySelector("#booking-status-chart"), bookingStatusOptions);
            bookingStatusChart.render();
        });
    </script>
@endpush
