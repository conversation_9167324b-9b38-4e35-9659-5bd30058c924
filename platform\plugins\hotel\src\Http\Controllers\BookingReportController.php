<?php

namespace Botble\Hotel\Http\Controllers;

use Bo<PERSON>ble\Base\Facades\Assets;
use Botble\Base\Http\Controllers\BaseController;
use Bo<PERSON>ble\Base\Widgets\AdminWidget;
use Botble\Hotel\Facades\HotelHelper;
use Botble\Hotel\Models\Booking;
use Botble\Hotel\Models\Customer;
use Botble\Hotel\Models\Room;
use Botble\Hotel\Models\RoomCategory;
use Botble\Hotel\Enums\BookingStatusEnum;
use Botble\Hotel\Tables\Reports\RecentBookingTable;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;
use Botble\Hotel\Exports\BookingExport;

class BookingReportController extends BaseController
{
    public function index(Request $request, AdminWidget $widget)
    {
        $this->pageTitle(trans('plugins/hotel::booking.reports'));

        Assets::addScriptsDirectly([
            'vendor/core/plugins/hotel/libraries/daterangepicker/daterangepicker.js',
            'vendor/core/plugins/hotel/js/report.js',
            'vendor/core/plugins/hotel/js/apexcharts.min.js',
            'vendor/core/plugins/hotel/js/booking-reports.js',
        ])
            ->addStylesDirectly([
                'vendor/core/plugins/hotel/libraries/daterangepicker/daterangepicker.css',
                'vendor/core/plugins/hotel/css/report.css',
                'vendor/core/plugins/hotel/css/booking-reports.css',
            ])
            ->addScripts(['moment', 'bootstrap']);

        [$startDate, $endDate] = HotelHelper::getDateRangeInReport($request);

        // Get booking statistics
        $totalBookings = Booking::query()
            ->whereDate('created_at', '>=', $startDate)
            ->whereDate('created_at', '<=', $endDate)
            ->count();

        $totalCustomers = Customer::query()
            ->whereDate('created_at', '>=', $startDate)
            ->whereDate('created_at', '<=', $endDate)
            ->count();

        $totalRooms = Room::query()->count();

        $totalRevenue = Booking::query()
            ->whereDate('created_at', '>=', $startDate)
            ->whereDate('created_at', '<=', $endDate)
            ->sum('amount');

        // Get booking status statistics
        $bookingStatusStats = Booking::query()
            ->select('status', DB::raw('count(*) as count'))
            ->whereDate('created_at', '>=', $startDate)
            ->whereDate('created_at', '<=', $endDate)
            ->groupBy('status')
            ->get()
            ->pluck('count', 'status')
            ->toArray();

        // Get booking trend data (daily bookings for the selected period)
        $period = Carbon::parse($startDate)->daysUntil($endDate);
        $bookingTrends = [];

        foreach ($period as $date) {
            $formattedDate = $date->format('Y-m-d');
            $bookingTrends[$formattedDate] = 0;
        }

        $dailyBookings = Booking::query()
            ->select(DB::raw('DATE(created_at) as date'), DB::raw('count(*) as count'))
            ->whereDate('created_at', '>=', $startDate)
            ->whereDate('created_at', '<=', $endDate)
            ->groupBy('date')
            ->get();

        foreach ($dailyBookings as $booking) {
            $bookingTrends[$booking->date] = $booking->count;
        }

        $chartData = array_values($bookingTrends);

        // Get revenue by room category
        $revenueByCategory = Booking::query()
            ->join('ht_booking_rooms', 'ht_bookings.id', '=', 'ht_booking_rooms.booking_id')
            ->join('ht_rooms', 'ht_booking_rooms.room_id', '=', 'ht_rooms.id')
            ->join('ht_room_categories', 'ht_rooms.room_category_id', '=', 'ht_room_categories.id')
            ->select('ht_room_categories.name', DB::raw('SUM(ht_bookings.amount) as revenue'))
            ->whereDate('ht_bookings.created_at', '>=', $startDate)
            ->whereDate('ht_bookings.created_at', '<=', $endDate)
            ->groupBy('ht_room_categories.name')
            ->get()
            ->pluck('revenue', 'name')
            ->toArray();

        // Get recent bookings
        $recentBookings = Booking::query()
            ->with(['customer', 'room', 'invoice.items'])
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        // Get room availability data
        $roomAvailability = $this->getRoomAvailabilityData();

        if ($request->ajax()) {
            return $this
                ->httpResponse()->setData(view('plugins/hotel::reports.ajax', compact('widget'))->render());
        }

        return view(
            'plugins/hotel::reports.index',
            compact(
                'startDate',
                'endDate',
                'widget',
                'totalBookings',
                'totalCustomers',
                'totalRooms',
                'totalRevenue',
                'bookingStatusStats',
                'chartData',
                'revenueByCategory',
                'recentBookings',
                'roomAvailability'
            )
        );
    }

    public function getRecentBookings(RecentBookingTable $table)
    {
        return $table->renderTable();
    }

    public function export(Request $request)
    {
        $this->pageTitle(trans('plugins/hotel::booking.export_booking_reports'));

        [$startDate, $endDate] = HotelHelper::getDateRangeInReport($request);

        return Excel::download(
            new BookingExport($startDate, $endDate),
            'booking_reports_' . Carbon::now()->format('Y-m-d') . '.xlsx'
        );
    }

    /**
     * Get room availability data
     *
     * @return array
     */
    protected function getRoomAvailabilityData()
    {
        $totalRooms = Room::query()->count();
        $bookedRooms = Booking::query()
            ->where('status', BookingStatusEnum::PROCESSING)
            ->count();

        return [
            'available' => $totalRooms - $bookedRooms,
            'booked' => $bookedRooms,
            'total' => $totalRooms,
        ];
    }

    public function advancedReports(Request $request)
    {
        $this->pageTitle(trans('plugins/hotel::booking.advance_reports'));

        // Set default date range if not provided
        $startDate = $request->input('start_date')
            ? Carbon::createFromFormat('Y-m-d', $request->input('start_date'))
            : Carbon::now()->startOfMonth();

        $endDate = $request->input('end_date')
            ? Carbon::createFromFormat('Y-m-d', $request->input('end_date'))
            : Carbon::now();

        // Get room categories for filter
        $roomCategories = app(RoomCategoryInterface::class)->all();

        // Calculate room availability
        $roomAvailability = $this->calculateRoomAvailability();

        // Other data preparation...

        return view('plugins/hotel::reports.advanced-report', compact(
            'startDate',
            'endDate',
            'roomCategories',
            'roomAvailability'
            // Other variables...
        ));
    }
}

