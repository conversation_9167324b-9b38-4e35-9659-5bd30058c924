.select2-container--default {
    &.select2-container--disabled,
    &.select2-container--disabled.select2-container--focus {
        .select2-selection {
            color: $s2bs5-disabled-color;
            cursor: not-allowed;
            background-color: $s2bs5-disabled-bg;
            border-color: $s2bs5-disabled-border-color;
            box-shadow: none;
        }

        .select2-selection--multiple {
            .select2-selection__clear {
                display: none;
            }

            .select2-selection__choice {
                cursor: not-allowed;
                .select2-selection__choice__remove {
                    display: none;
                }
            }

            .select2-selection__rendered:not(:empty) {
                padding-bottom: 0;

                + .select2-search {
                    display: none;
                }
            }
        }
    }
}
