<?php

namespace Botble\Hotel\Providers;

use Botble\Base\Facades\DashboardMenu;
use Botble\Base\Traits\LoadAndPublishDataTrait;
use Illuminate\Routing\Events\RouteMatched;
use Illuminate\Support\ServiceProvider;

class RemoveReportsMenuServiceProvider extends ServiceProvider
{
    use LoadAndPublishDataTrait;

    public function boot(): void
    {
        $this->app['events']->listen(RouteMatched::class, function () {
            DashboardMenu::removeItem('cms-plugins-hotel-booking-reports');
            DashboardMenu::removeItem('cms-plugins-hotel-booking-advance-reports');
        });
    }
}