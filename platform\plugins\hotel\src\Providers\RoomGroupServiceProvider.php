<?php

namespace Botble\Hotel\Providers;

use Botble\Base\Traits\LoadAndPublishDataTrait;
use Botble\Hotel\Services\RoomGroupService;
use Illuminate\Support\ServiceProvider;

class RoomGroupServiceProvider extends ServiceProvider
{
    use LoadAndPublishDataTrait;

    public function register(): void
    {
        $this->app->singleton(RoomGroupService::class, function () {
            return new RoomGroupService();
        });
    }

    public function boot(): void
    {
        $this->app->booted(function () {
            $this->app->register(EventServiceProvider::class);
        });
    }
}
