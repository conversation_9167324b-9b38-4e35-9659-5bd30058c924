@php
/**
 * This helper file enhances the booking information display
 * without modifying any existing functionality.
 */

// Get booking dates directly from the database if they're missing
if ($booking && (!isset($booking->start_date) || !isset($booking->end_date) || !$booking->start_date || !$booking->end_date)) {
    $bookingRoom = \Botble\Hotel\Models\BookingRoom::where('booking_id', $booking->id)->first();
    if ($bookingRoom) {
        // Set the dates in the current booking object (doesn't affect database)
        $booking->start_date = $bookingRoom->start_date;
        $booking->end_date = $bookingRoom->end_date;
    }
}

// Get customer information
$customerName = 'N/A';
$customerEmail = 'N/A';
$customerPhone = 'N/A';

// Try to get from booking address first
if ($booking->address && $booking->address->first_name) {
    $customerName = $booking->address->first_name . ' ' . $booking->address->last_name;
    $customerEmail = $booking->address->email ?: 'N/A';
    $customerPhone = $booking->address->phone ?: 'N/A';
} 
// Fall back to customer record if available
elseif ($booking->customer) {
    $customerName = $booking->customer->first_name . ' ' . $booking->customer->last_name;
    $customerEmail = $booking->customer->email ?: 'N/A';
    $customerPhone = $booking->customer->phone ?: 'N/A';
}

// Format dates for display
$checkInDate = $booking->start_date ?: 'N/A';
$checkOutDate = $booking->end_date ?: 'N/A';
@endphp

{{-- Customer Information Section --}}
<div class="customer-info-section mb-4">
    <x-core::datagrid>
        <x-core::datagrid.item :title="__('Customer Name')">
            {{ $customerName }}
        </x-core::datagrid.item>
        
        <x-core::datagrid.item :title="__('Customer Email')">
            {{ $customerEmail }}
        </x-core::datagrid.item>
        
        <x-core::datagrid.item :title="__('Customer Phone')">
            {{ $customerPhone }}
        </x-core::datagrid.item>
    </x-core::datagrid>
</div>

{{-- Booking Dates Section --}}
<div class="booking-dates-section mb-4">
    <x-core::datagrid>
        <x-core::datagrid.item :title="__('Check-in Date')">
            {{ $checkInDate }}
        </x-core::datagrid.item>
        
        <x-core::datagrid.item :title="__('Check-out Date')">
            {{ $checkOutDate }}
        </x-core::datagrid.item>
    </x-core::datagrid>
</div>
