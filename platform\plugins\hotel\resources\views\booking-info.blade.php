@php
    $route ??= 'invoices.generate';

    // Calculate service subtotal from invoice items (consistent with getTotalAmount method)
    $serviceSubtotal = 0;
    if ($booking->invoice && $booking->invoice->items) {
        $serviceSubtotal = $booking->invoice->items
            ->filter(function($item) {
                return strpos($item->name, 'additional service') !== false ||
                       strpos($item->name, 'extra service') !== false ||
                       strpos($item->name, '(extra service)') !== false;
            })
            ->sum('amount');
    }

    $roomPrice = $booking->room->price ?? 0;
    $recalculatedSubTotal = $roomPrice + $serviceSubtotal;
@endphp

@if ($booking)

    {{-- Booking Details --}}
    <x-core::datagrid class="mb-4">
        <x-core::datagrid.item :title="__('Booking Number')">
            {{ $booking->booking_number }}
        </x-core::datagrid.item>

        @if ($booking->room_id_code && $booking->room_id_code !== 'Select Room ID')
            <x-core::datagrid.item :title="__('Room ID Code')">
                {{ $booking->room_id_code }}
            </x-core::datagrid.item>
        @elseif (auth()->check() && (auth()->user()->hasPermission('booking.edit') || auth()->user()->isSuperUser()))
            <x-core::datagrid.item :title="__('Room ID Code')">
                <span class="text-warning">{{ __('Pending Room Assignment') }}</span>
            </x-core::datagrid.item>
        @endif
    </x-core::datagrid>
      <x-core::datagrid class="mb-4">
        <x-core::datagrid.item :title="__('Check-in Date')">
            {{ $booking->room && $booking->room->start_date ? $booking->room->start_date : __('N/A') }}
        </x-core::datagrid.item>

        <x-core::datagrid.item :title="__('Check-out Date')">
            {{ $booking->room && $booking->room->end_date ? $booking->room->end_date : __('N/A') }}
        </x-core::datagrid.item>

        <x-core::datagrid.item :title="__('Check-in Time')">
            {{ $booking->check_in_time ? \Carbon\Carbon::parse($booking->check_in_time)->format('Y-m-d H:i:s') : __('Not checked in yet') }}
        </x-core::datagrid.item>

        <x-core::datagrid.item :title="__('Check-out Time')">
            {{ $booking->check_out_time ? \Carbon\Carbon::parse($booking->check_out_time)->format('Y-m-d H:i:s') : __('Not checked out yet') }}
        </x-core::datagrid.item>

        @if ($booking->arrival_time)
            <x-core::datagrid.item :title="__('Arrival Time')">
                {{ $booking->arrival_time }}
            </x-core::datagrid.item>
        @endif

        @if ($booking->requests)
            <x-core::datagrid.item :title="__('Requests')">
                {{ $booking->requests }}
            </x-core::datagrid.item>
        @endif

        @if ($booking->number_of_guests)
            <x-core::datagrid.item :title="__('Number of adults')">
                {{ $booking->number_of_guests }}
            </x-core::datagrid.item>
        @endif

        @if ($booking->number_of_children)
            <x-core::datagrid.item :title="__('Number of children')">
                {{ $booking->number_of_children }}
            </x-core::datagrid.item>
        @endif
    </x-core::datagrid>

    {{-- Room Info --}}
    @if($booking->room)
        <div class="mb-4">
            <h4>{{ __('Room') }}</h4>
            <x-core::table>
                <x-core::table.header>
                    <x-core::table.header.cell class="text-center" style="width: 150px;">
                        {{ __('Image') }}
                    </x-core::table.header.cell>
                    <x-core::table.header.cell>{{ __('Name') }}</x-core::table.header.cell>
                    <x-core::table.header.cell class="text-center">{{ __('Check-in Date') }}</x-core::table.header.cell>
                    <x-core::table.header.cell class="text-center">{{ __('Check-out Date') }}</x-core::table.header.cell>
                    <x-core::table.header.cell class="text-center">{{ __('Number of rooms') }}</x-core::table.header.cell>
                    <x-core::table.header.cell class="text-center">{{ __('Price') }}</x-core::table.header.cell>
                    <x-core::table.header.cell class="text-center">{{ __('Tax') }}</x-core::table.header.cell>
                </x-core::table.header>
                <x-core::table.body>
                    <x-core::table.body.row>
                        @if ($booking->room->room && $booking->room->room->exists)
                            <x-core::table.body.cell class="text-center" style="width: 150px;">
                                <a href="{{ $booking->room->room->url }}" target="_blank">
                                    <img src="{{ RvMedia::getImageUrl($booking->room->room->image, 'thumb', false, RvMedia::getDefaultImage()) }}"
                                         alt="{{ $booking->room->room_name ?? 'Room' }}" width="140">
                                </a>
                            </x-core::table.body.cell>
                            <x-core::table.body.cell style="vertical-align: middle !important;">
                                <a class="booking-information-link" href="{{ $booking->room->room->url }}" target="_blank">
                                    {{ $booking->room->room->name }}
                                </a>
                            </x-core::table.body.cell>
                        @else
                            <x-core::table.body.cell>
                                <img src="{{ RvMedia::getImageUrl($booking->room->room_image ?? '', 'thumb', false, RvMedia::getDefaultImage()) }}"
                                     alt="{{ $booking->room->room_name ?? 'Room' }}" width="140">
                            </x-core::table.body.cell>
                            <x-core::table.body.cell style="vertical-align: middle !important;">
                                {{ $booking->room->room_name ?? __('N/A') }}
                            </x-core::table.body.cell>
                        @endif
                        <x-core::table.body.cell class="text-center">{{ $booking->room->start_date ?? __('N/A') }}</x-core::table.body.cell>
                        <x-core::table.body.cell class="text-center">{{ $booking->room->end_date ?? __('N/A') }}</x-core::table.body.cell>
                        <x-core::table.body.cell class="text-center">{{ $booking->room->number_of_rooms ?? 1 }}</x-core::table.body.cell>
                        <x-core::table.body.cell class="text-center"><strong>{{ format_price($roomPrice) }}</strong></x-core::table.body.cell>
                        <x-core::table.body.cell class="text-center"><strong>{{ format_price($booking->tax_amount ?? 0) }}</strong></x-core::table.body.cell>
                    </x-core::table.body.row>
                </x-core::table.body>
            </x-core::table>
        </div>
    @endif

    {{-- Services --}}
    @if (isset($booking->invoice) && $booking->invoice->items->isNotEmpty())
        <h4>{{ __('Services') }}</h4>
        <x-core::table>
            <x-core::table.header>
                <x-core::table.header.cell>{{ __('Name') }}</x-core::table.header.cell>
                <x-core::table.header.cell class="text-center">{{ __('Price') }}</x-core::table.header.cell>
                <x-core::table.header.cell class="text-center">{{ __('Quantity') }}</x-core::table.header.cell>
                <x-core::table.header.cell class="text-center">{{ __('Total') }}</x-core::table.header.cell>
            </x-core::table.header>
            <x-core::table.body>
                @foreach ($booking->invoice->items as $item)
                    @if (strpos($item->name, 'additional service') !== false ||
                         strpos($item->name, 'extra service') !== false ||
                         strpos($item->name, '(extra service)') !== false)
                        <x-core::table.body.row>
                            <x-core::table.body.cell>{{ $item->name }}</x-core::table.body.cell>
                            <x-core::table.body.cell class="text-center">{{ format_price($item->sub_total) }}</x-core::table.body.cell>
                            <x-core::table.body.cell class="text-center">{{ $item->qty }}</x-core::table.body.cell>
                            <x-core::table.body.cell class="text-center">{{ format_price($item->amount) }}</x-core::table.body.cell>
                        </x-core::table.body.row>
                    @endif
                @endforeach
            </x-core::table.body>
        </x-core::table>
    @endif

    {{-- Totals --}}
    <x-core::datagrid>
        <x-core::datagrid.item :title="__('Sub Total')">
            {{ format_price($recalculatedSubTotal) }}
        </x-core::datagrid.item>

        <x-core::datagrid.item :title="__('Discount Amount')">
            {{ format_price($booking->coupon_amount) }}
        </x-core::datagrid.item>

        <x-core::datagrid.item :title="__('Tax Amount')">
            {{ format_price($booking->tax_amount) }}
        </x-core::datagrid.item>

        <x-core::datagrid.item :title="__('Total Amount')">
            {{ format_price($booking->getTotalAmount()) }}
        </x-core::datagrid.item>

        <x-core::datagrid.item :title="__('Status')">
            {!! $booking->status->toHtml() !!}
        </x-core::datagrid.item>
    </x-core::datagrid>

    {{-- Invoice Buttons --}}
    @if ((auth()->check() || $booking->customer_id) && isset($booking->invoice->id) && $route)
        <div class="btn-list mt-5">
            @if (auth()->check() && (auth()->user()->hasPermission('booking.view-invoice') || auth()->user()->isSuperUser()))
                <x-core::button
                    tag="a"
                    :href="route('invoices.view', ['invoice' => $booking->invoice->id])"
                    target="_blank"
                    icon="ti ti-eye"
                >
                    {{ __('View Invoice') }}
                </x-core::button>
            @endif
            @if (auth()->check() && (auth()->user()->hasPermission('booking.download-invoice') || auth()->user()->isSuperUser()))
                <x-core::button
                    tag="a"
                    :href="route($route, ['id' => $booking->invoice->id, 'type' => 'download'])"
                    target="_blank"
                    icon="ti ti-download"
                >
                    {{ __('Download Invoice') }}
                </x-core::button>
            @endif
        </div>
    @endif
@endif

