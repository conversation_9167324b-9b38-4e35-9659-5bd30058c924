<?php

namespace Botble\Hotel\Services;

use Botble\Hotel\Models\Room;
use Botble\Hotel\Models\RoomCategory;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;

class RoomSearchService
{
    /**
     * @var RoomCapacityService
     */
    protected $roomCapacityService;

    /**
     * RoomSearchService constructor.
     */
    public function __construct(RoomCapacityService $roomCapacityService = null)
    {
        $this->roomCapacityService = $roomCapacityService ?? new RoomCapacityService();
    }

    /**
     * Get available rooms based on adults and children count
     * 
     * @param int $adults
     * @param int $children
     * @param array $additionalFilters Additional filters like dates, etc.
     * @return Collection
     */
    public function getAvailableRooms(int $adults, int $children, array $additionalFilters = []): Collection
    {
        try {
            // Log the search parameters
            \Illuminate\Support\Facades\Log::info('Room search started', [
                'adults' => $adults,
                'children' => $children,
                'additionalFilters' => $additionalFilters
            ]);
            
            // First filter rooms based on occupancy requirements
            $matchingRooms = collect();
            
            // Get all published rooms
            $allRooms = Room::with(['category', 'currency'])
                ->where('status', 'published')
                ->get();
                
            // Filter rooms based on occupancy
            foreach ($allRooms as $room) {
                $categoryName = $room->category ? strtolower($room->category->name) : '';
                
                // Log room details for debugging
                \Illuminate\Support\Facades\Log::info('Checking room', [
                    'room_id' => $room->id,
                    'room_name' => $room->name,
                    'category' => $categoryName,
                    'adults' => $adults,
                    'children' => $children
                ]);
                
                // SPECIAL CASE: Always include Luxury rooms for 1 adult
                if ($adults === 1 && stripos($categoryName, 'luxury') !== false) {
                    \Illuminate\Support\Facades\Log::info('Luxury room added for 1 adult', [
                        'room_id' => $room->id,
                        'room_name' => $room->name
                    ]);
                    $matchingRooms->push($room);
                    continue;
                }
                
                // Skip rooms that don't match occupancy requirements
                if (!$this->roomMatchesOccupancyRules($room, $adults, $children)) {
                    \Illuminate\Support\Facades\Log::info('Room skipped - does not match occupancy rules', [
                        'room_id' => $room->id,
                        'room_name' => $room->name
                    ]);
                    continue;
                }
                
                $matchingRooms->push($room);
            }
            
            if ($matchingRooms->isEmpty()) {
                return collect();
            }
            
            // Apply additional filters if provided
            if (!empty($additionalFilters)) {
                $filteredRooms = collect();
                
                foreach ($matchingRooms as $room) {
                    // Check date availability if dates are provided
                    if (isset($additionalFilters['start_date']) && isset($additionalFilters['end_date'])) {
                        $startDate = $additionalFilters['start_date'];
                        $endDate = $additionalFilters['end_date'];
                        
                        $condition = [
                            'start_date' => $startDate,
                            'end_date' => $endDate,
                            'adults' => $adults,
                            'children' => $children,
                            'rooms' => $additionalFilters['rooms'] ?? 1,
                        ];
                        
                        if ($room->isAvailableAt($condition)) {
                            $filteredRooms->push($room);
                        }
                    } else {
                        $filteredRooms->push($room);
                    }
                }
                
                $matchingRooms = $filteredRooms;
            }
            
            // Log the search results for debugging
            Log::info('Room search results', [
                'adults' => $adults,
                'children' => $children,
                'room_count' => $matchingRooms->count(),
            ]);
            
            return $matchingRooms;
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Error in room search: ' . $e->getMessage(), [
                'adults' => $adults,
                'children' => $children,
                'trace' => $e->getTraceAsString(),
            ]);
            
            // Return empty collection on error
            return collect();
        }
    }

    /**
     * Check if a room matches our flexible occupancy rules
     * 
     * @param Room $room
     * @param int $adults
     * @param int $children
     * @return bool
     */
    public function roomMatchesOccupancyRules(Room $room, int $adults, int $children): bool
    {
        // Total occupancy check
        $totalOccupants = $adults + $children;
        $maxOccupants = $room->max_adults + $room->max_children;
        
        if ($totalOccupants > $maxOccupants) {
            return false;
        }
        
        // Adult capacity check
        if ($adults > $room->max_adults) {
            return false;
        }
        
        // Children capacity check
        if ($children > $room->max_children) {
            return false;
        }
        
        // Get category name for rule checking
        $categoryName = $room->category ? strtolower($room->category->name) : '';
        
        // ALLOW ALL ROOM TYPES FOR 1 ADULT
        // Special case: Luxury Room - explicitly allow 1 adult
        if ($adults === 1 && stripos($categoryName, 'luxury') !== false) {
            \Illuminate\Support\Facades\Log::info('Luxury room allowed for 1 adult', [
                'room_id' => $room->id,
                'room_name' => $room->name
            ]);
            return true;
        }
        
        // Special case: Standard Room with 2 adults and 2 children
        if ($adults === 2 && $children === 2 && $room->category && 
            stripos($categoryName, 'standard') !== false) {
            return false;
        }
        
        return true;
    }

    /**
     * Get room types that match the given adults and children count
     * 
     * @param int $adults
     * @param int $children
     * @return array
     */
    public function getMatchingRoomTypes(int $adults, int $children): array
    {
        // Delegate to the RoomCapacityService
        return $this->roomCapacityService->getMatchingRoomTypes($adults, $children);
    }
}





