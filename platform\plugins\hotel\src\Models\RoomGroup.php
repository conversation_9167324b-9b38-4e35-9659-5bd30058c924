<?php

namespace Botble\Hotel\Models;

use Botble\Base\Models\BaseModel;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Botble\Hotel\Models\RoomInventory;

class RoomGroup extends BaseModel
{
    protected $table = 'ht_room_groups';

    protected $fillable = [
        'name',
        'code',
        'description',
    ];

    public function items(): HasMany
    {
        return $this->hasMany(RoomGroupItem::class, 'room_group_id');
    }

    /**
     * Check if any room in the group is booked
     */
    public function hasAnyRoomBooked(): bool
    {
        $roomIdCodes = $this->items()->pluck('room_id_code')->toArray();

        if (empty($roomIdCodes)) {
            return false;
        }

        return RoomInventory::whereIn('room_id_code', $roomIdCodes)
            ->where('is_available', false)
            ->exists();
    }

    /**
     * Check if all rooms in the group are available
     */
    public function areAllRoomsAvailable(): bool
    {
        $roomIdCodes = $this->items()->pluck('room_id_code')->toArray();

        if (empty($roomIdCodes)) {
            return false;
        }

        $availableCount = RoomInventory::whereIn('room_id_code', $roomIdCodes)
            ->where('is_available', true)
            ->count();

        return $availableCount === count($roomIdCodes);
    }
}
