@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">{{ trans('plugins/hotel::booking.create_offline_booking') }}</h4>
                </div>

                <div class="card-body">
                    <form id="offline-booking-form" action="{{ route('booking.offline.store') }}" method="POST">
                        @csrf

                        <!-- Customer Information Section -->
                        <div class="row">
                            <div class="col-md-12">
                                <h5 class="mb-3">{{ trans('plugins/hotel::booking.customer_information') }}</h5>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="first_name" class="form-label required">{{ trans('plugins/hotel::booking.first_name') }}</label>
                                    <input type="text" class="form-control" id="first_name" name="first_name"
                                           value="{{ old('first_name') }}" required>
                                    @error('first_name')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="last_name" class="form-label required">{{ trans('plugins/hotel::booking.last_name') }}</label>
                                    <input type="text" class="form-control" id="last_name" name="last_name"
                                           value="{{ old('last_name') }}" required>
                                    @error('last_name')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="email" class="form-label required">{{ trans('plugins/hotel::booking.email') }}</label>
                                    <input type="email" class="form-control" id="email" name="email"
                                           value="{{ old('email') }}" required>
                                    @error('email')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="phone" class="form-label required">{{ trans('plugins/hotel::booking.phone') }}</label>
                                    <input type="tel" class="form-control" id="phone" name="phone"
                                           value="{{ old('phone') }}" required>
                                    @error('phone')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label for="address" class="form-label">{{ trans('plugins/hotel::booking.address') }}</label>
                                    <textarea class="form-control" id="address" name="address" rows="2">{{ old('address') }}</textarea>
                                    @error('address')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="city" class="form-label">{{ trans('plugins/hotel::booking.city') }}</label>
                                    <input type="text" class="form-control" id="city" name="city" value="{{ old('city') }}">
                                    @error('city')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="state" class="form-label">{{ trans('plugins/hotel::booking.state') }}</label>
                                    <input type="text" class="form-control" id="state" name="state" value="{{ old('state') }}">
                                    @error('state')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="country" class="form-label">{{ trans('plugins/hotel::booking.country') }}</label>
                                    <input type="text" class="form-control" id="country" name="country" value="{{ old('country') }}">
                                    @error('country')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="zip" class="form-label">{{ trans('plugins/hotel::booking.zip') }}</label>
                                    <input type="text" class="form-control" id="zip" name="zip" value="{{ old('zip') }}">
                                    @error('zip')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <hr class="my-4">

                        <!-- Booking Information Section -->
                        <div class="row">
                            <div class="col-md-12">
                                <h5 class="mb-3">{{ trans('plugins/hotel::booking.booking_information') }}</h5>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="start_date" class="form-label required">{{ trans('plugins/hotel::booking.start_date') }}</label>
                                    <input type="date" class="form-control" id="start_date" name="start_date"
                                           value="{{ old('start_date') }}" required min="{{ date('Y-m-d') }}">
                                    @error('start_date')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="end_date" class="form-label required">{{ trans('plugins/hotel::booking.end_date') }}</label>
                                    <input type="date" class="form-control" id="end_date" name="end_date"
                                           value="{{ old('end_date') }}" required min="{{ date('Y-m-d', strtotime('+1 day')) }}">
                                    @error('end_date')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-2">
                                <div class="form-group">
                                    <label for="adults" class="form-label required">{{ trans('plugins/hotel::booking.adults') }}</label>
                                    <select class="form-control" id="adults" name="adults" required>
                                        @for($i = 1; $i <= 10; $i++)
                                            <option value="{{ $i }}" {{ old('adults', 1) == $i ? 'selected' : '' }}>{{ $i }}</option>
                                        @endfor
                                    </select>
                                    @error('adults')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-2">
                                <div class="form-group">
                                    <label for="children" class="form-label">{{ trans('plugins/hotel::booking.children') }}</label>
                                    <select class="form-control" id="children" name="children">
                                        @for($i = 0; $i <= 10; $i++)
                                            <option value="{{ $i }}" {{ old('children', 0) == $i ? 'selected' : '' }}>{{ $i }}</option>
                                        @endfor
                                    </select>
                                    @error('children')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-2">
                                <div class="form-group">
                                    <label for="number_of_rooms" class="form-label required">{{ trans('plugins/hotel::booking.number_of_rooms') }}</label>
                                    <select class="form-control" id="number_of_rooms" name="number_of_rooms" required>
                                        @for($i = 1; $i <= 10; $i++)
                                            <option value="{{ $i }}" {{ old('number_of_rooms', 1) == $i ? 'selected' : '' }}>{{ $i }}</option>
                                        @endfor
                                    </select>
                                    @error('number_of_rooms')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <button type="button" id="search-rooms" class="btn btn-primary">
                                    <i class="ti ti-search"></i> {{ trans('plugins/hotel::booking.search_available_rooms') }}
                                </button>
                            </div>
                        </div>

                        <!-- Available Rooms Section -->
                        <div id="available-rooms-section" class="mt-4" style="display: none;">
                            <h6>{{ trans('plugins/hotel::booking.available_rooms') }}</h6>
                            <div id="available-rooms-list" class="row">
                                <!-- Rooms will be loaded here via AJAX -->
                            </div>
                        </div>

                        <!-- Selected Room Section -->
                        <div id="selected-room-section" class="mt-4" style="display: none;">
                            <div class="alert alert-info">
                                <h6>{{ trans('plugins/hotel::booking.selected_room') }}</h6>
                                <div id="selected-room-details"></div>
                            </div>
                            <input type="hidden" id="room_id" name="room_id" value="{{ old('room_id') }}">
                            @error('room_id')
                                <div class="text-danger">{{ $message }}</div>
                            @enderror
                        </div>

                        <hr class="my-4">

                        <!-- Additional Information Section -->
                        <div class="row">
                            <div class="col-md-12">
                                <h5 class="mb-3">{{ trans('plugins/hotel::booking.additional_information') }}</h5>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="arrival_time" class="form-label">{{ trans('plugins/hotel::booking.arrival_time') }}</label>
                                    <input type="time" class="form-control" id="arrival_time" name="arrival_time"
                                           value="{{ old('arrival_time') }}">
                                    @error('arrival_time')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="status" class="form-label required">{{ trans('core/base::tables.status') }}</label>
                                    <select class="form-control" id="status" name="status" required>
                                        @foreach($bookingStatuses as $value => $label)
                                            <option value="{{ $value }}" {{ old('status', 'pending') == $value ? 'selected' : '' }}>
                                                {{ $label }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('status')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label for="requests" class="form-label">{{ trans('plugins/hotel::booking.requests') }}</label>
                                    <textarea class="form-control" id="requests" name="requests" rows="3"
                                              placeholder="{{ trans('plugins/hotel::booking.special_requests_placeholder') }}">{{ old('requests') }}</textarea>
                                    @error('requests')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Services Section -->
                        @if($services->count() > 0)
                        <div class="row">
                            <div class="col-md-12">
                                <h6>{{ trans('plugins/hotel::booking.additional_services') }}</h6>
                                <div class="row">
                                    @foreach($services as $service)
                                        <div class="col-md-4">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox"
                                                       id="service_{{ $service->id }}"
                                                       name="services[]"
                                                       value="{{ $service->id }}"
                                                       {{ in_array($service->id, old('services', [])) ? 'checked' : '' }}>
                                                <label class="form-check-label" for="service_{{ $service->id }}">
                                                    {{ $service->name }}
                                                    @if($service->price > 0)
                                                        <small class="text-muted">({{ format_price($service->price) }})</small>
                                                    @endif
                                                </label>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        </div>
                        @endif

                        <hr class="my-4">

                        <!-- Payment Information Section -->
                        <div class="row">
                            <div class="col-md-12">
                                <h5 class="mb-3">{{ trans('plugins/hotel::booking.payment_information') }}</h5>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="payment_method" class="form-label">{{ trans('plugins/hotel::booking.payment_method') }}</label>
                                    <select class="form-control" id="payment_method" name="payment_method">
                                        <option value="">{{ trans('plugins/hotel::booking.select_payment_method') }}</option>
                                        <option value="cash" {{ old('payment_method') == 'cash' ? 'selected' : '' }}>{{ trans('plugins/hotel::booking.cash') }}</option>
                                        <option value="card" {{ old('payment_method') == 'card' ? 'selected' : '' }}>{{ trans('plugins/hotel::booking.card') }}</option>
                                        <option value="bank_transfer" {{ old('payment_method') == 'bank_transfer' ? 'selected' : '' }}>{{ trans('plugins/hotel::booking.bank_transfer') }}</option>
                                        <option value="other" {{ old('payment_method') == 'other' ? 'selected' : '' }}>{{ trans('plugins/hotel::booking.other') }}</option>
                                    </select>
                                    @error('payment_method')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="payment_notes" class="form-label">{{ trans('plugins/hotel::booking.payment_notes') }}</label>
                                    <textarea class="form-control" id="payment_notes" name="payment_notes" rows="2"
                                              placeholder="{{ trans('plugins/hotel::booking.payment_notes_placeholder') }}">{{ old('payment_notes') }}</textarea>
                                    @error('payment_notes')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Form Actions -->
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-actions">
                                    <button type="submit" class="btn btn-success" id="submit-booking">
                                        <i class="ti ti-check"></i> {{ trans('plugins/hotel::booking.create_booking') }}
                                    </button>
                                    <a href="{{ route('booking.index') }}" class="btn btn-secondary">
                                        <i class="ti ti-arrow-left"></i> {{ trans('core/base::forms.cancel') }}
                                    </a>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('footer')
<script>
$(document).ready(function() {
    // Date validation
    $('#start_date').on('change', function() {
        const startDate = $(this).val();
        if (startDate) {
            const nextDay = new Date(startDate);
            nextDay.setDate(nextDay.getDate() + 1);
            $('#end_date').attr('min', nextDay.toISOString().split('T')[0]);

            if ($('#end_date').val() && $('#end_date').val() <= startDate) {
                $('#end_date').val('');
            }
        }
    });

    // Search available rooms
    $('#search-rooms').on('click', function() {
        const startDate = $('#start_date').val();
        const endDate = $('#end_date').val();
        const adults = $('#adults').val();
        const children = $('#children').val();
        const numberOfRooms = $('#number_of_rooms').val();

        if (!startDate || !endDate) {
            alert('{{ trans("plugins/hotel::booking.please_select_dates") }}');
            return;
        }

        if (startDate >= endDate) {
            alert('{{ trans("plugins/hotel::booking.end_date_must_be_after_start_date") }}');
            return;
        }

        const button = $(this);
        const originalText = button.html();
        button.html('<i class="ti ti-loader"></i> {{ trans("plugins/hotel::booking.searching") }}...').prop('disabled', true);

        $.ajax({
            url: '{{ route("booking.offline.get-available-rooms") }}',
            method: 'GET',
            data: {
                start_date: startDate,
                end_date: endDate,
                adults: adults,
                children: children,
                number_of_rooms: numberOfRooms
            },
            success: function(response) {
                if (response.success) {
                    displayAvailableRooms(response.rooms);
                    $('#available-rooms-section').show();
                } else {
                    alert(response.message);
                }
            },
            error: function() {
                alert('{{ trans("plugins/hotel::booking.error_searching_rooms") }}');
            },
            complete: function() {
                button.html(originalText).prop('disabled', false);
            }
        });
    });

    // Display available rooms
    function displayAvailableRooms(rooms) {
        const roomsList = $('#available-rooms-list');
        roomsList.empty();

        if (rooms.length === 0) {
            roomsList.html('<div class="col-12"><div class="alert alert-warning">{{ trans("plugins/hotel::booking.no_rooms_available") }}</div></div>');
            return;
        }

        rooms.forEach(function(room) {
            const roomCard = `
                <div class="col-md-6 col-lg-4 mb-3">
                    <div class="card room-card" data-room-id="${room.id}">
                        <div class="card-body">
                            <h6 class="card-title">${room.name}</h6>
                            <p class="card-text">
                                <small class="text-muted">${room.category}</small><br>
                                <strong>{{ trans("plugins/hotel::booking.price_per_night") }}: ${room.formatted_price}</strong><br>
                                <strong>{{ trans("plugins/hotel::booking.total_price") }}: ${room.formatted_total_price}</strong>
                            </p>
                            <p class="card-text">
                                <small>{{ trans("plugins/hotel::booking.max_occupancy") }}: ${room.max_adults} {{ trans("plugins/hotel::booking.adults") }}, ${room.max_children} {{ trans("plugins/hotel::booking.children") }}</small>
                            </p>
                            <button type="button" class="btn btn-primary btn-sm select-room" data-room='${JSON.stringify(room)}'>
                                {{ trans("plugins/hotel::booking.select_room") }}
                            </button>
                        </div>
                    </div>
                </div>
            `;
            roomsList.append(roomCard);
        });
    }

    // Select room
    $(document).on('click', '.select-room', function() {
        const room = JSON.parse($(this).attr('data-room'));

        $('#room_id').val(room.id);

        const roomDetails = `
            <strong>${room.name}</strong> (${room.category})<br>
            {{ trans("plugins/hotel::booking.price_per_night") }}: ${room.formatted_price}<br>
            {{ trans("plugins/hotel::booking.total_price") }}: ${room.formatted_total_price}<br>
            {{ trans("plugins/hotel::booking.max_occupancy") }}: ${room.max_adults} {{ trans("plugins/hotel::booking.adults") }}, ${room.max_children} {{ trans("plugins/hotel::booking.children") }}
        `;

        $('#selected-room-details').html(roomDetails);
        $('#selected-room-section').show();

        // Highlight selected room
        $('.room-card').removeClass('border-primary');
        $(this).closest('.room-card').addClass('border-primary');

        // Update button text
        $('.select-room').text('{{ trans("plugins/hotel::booking.select_room") }}');
        $(this).text('{{ trans("plugins/hotel::booking.selected") }}');
    });

    // Form validation before submit
    $('#offline-booking-form').on('submit', function(e) {
        const roomId = $('#room_id').val();

        if (!roomId) {
            e.preventDefault();
            alert('{{ trans("plugins/hotel::booking.please_select_room") }}');
            return false;
        }

        // Show loading state
        $('#submit-booking').html('<i class="ti ti-loader"></i> {{ trans("plugins/hotel::booking.creating_booking") }}...').prop('disabled', true);
    });

    // Auto-search rooms if dates are already selected (for validation errors)
    if ($('#start_date').val() && $('#end_date').val()) {
        $('#search-rooms').click();
    }
});
</script>
@endpush
