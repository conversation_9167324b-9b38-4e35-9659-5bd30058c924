/**
 * Room Occupancy Validation
 * Handles client-side validation for room occupancy based on room category
 */
$(document).ready(function() {
    // Define valid occupancy combinations for Standard Room
    const standardRoomCombinations = [
        { adults: 1, children: 0 }, // 1 adult only
        { adults: 1, children: 1 }, // 1 adult and 1 child
        { adults: 1, children: 2 }, // 1 adult and 2 children
        { adults: 2, children: 1 }  // 2 adults and 1 child
    ];

    // Define valid occupancy combinations for Double Bed Room
    const doubleBedRoomCombinations = [
        { adults: 3, children: 1 }, // 3 adults, 1 child
        { adults: 3, children: 0 }, // 3 adults, no children
        { adults: 2, children: 2 }, // 2 adults, 2 children
        { adults: 2, children: 1 }, // 2 adults, 1 child
        { adults: 2, children: 0 }, // 2 adults, no children
        { adults: 1, children: 3 }, // 1 adult, 3 children
        { adults: 1, children: 2 }, // 1 adult, 2 children
        { adults: 1, children: 1 }, // 1 adult, 1 child
        { adults: 1, children: 0 }  // 1 adult, no children
        // Note: 4 adults is explicitly excluded
    ];

    // Get form elements
    const $roomCategorySelect = $('select[name="room_category_id"]');
    const $maxAdultsInput = $('input[id="max-adults-number"]');
    const $maxChildrenInput = $('input[id="max-children-number"]');

    // Function to validate room occupancy based on room category
    function validateRoomOccupancy() {
        const maxAdults = parseInt($maxAdultsInput.val()) || 0;
        const maxChildren = parseInt($maxChildrenInput.val()) || 0;

        // Get the selected room category
        const selectedCategoryText = $roomCategorySelect.find('option:selected').text();

        // Remove any existing warnings
        $('.occupancy-warning').remove();
        $maxAdultsInput.removeClass('is-invalid');
        $maxChildrenInput.removeClass('is-invalid');

        // Validate based on room category
        if (selectedCategoryText === 'Standard Room') {
            validateSpecificRoomType(
                maxAdults,
                maxChildren,
                standardRoomCombinations,
                'Standard Room',
                [
                    '2 adults and 1 child',
                    '1 adult and 2 children',
                    '1 adult only',
                    '1 adult and 1 child'
                ]
            );
        } else if (selectedCategoryText === 'Double Bed Room') {
            validateSpecificRoomType(
                maxAdults,
                maxChildren,
                doubleBedRoomCombinations,
                'Double Bed Room',
                [
                    '3 adults and 1 child',
                    '3 adults only',
                    '2 adults and 2 children',
                    '2 adults and 1 child',
                    '2 adults only',
                    '1 adult and 3 children',
                    '1 adult and 2 children',
                    '1 adult and 1 child',
                    '1 adult only'
                ]
            );
        }
    }

    // Helper function to validate a specific room type
    function validateSpecificRoomType(maxAdults, maxChildren, allowedCombinations, roomTypeName, allowedDescriptions) {
        // Check if the current combination is valid
        let isValid = false;

        for (const combo of allowedCombinations) {
            if (combo.adults === maxAdults && combo.children === maxChildren) {
                isValid = true;
                break;
            }
        }

        // Show validation message if invalid
        if (!isValid) {
            showWarning(roomTypeName, allowedDescriptions);
            return false;
        }

        return true;
    }

    // Helper function to show warning message
    function showWarning(roomTypeName, allowedDescriptions) {
        // Create list items for allowed combinations
        let listItems = '';
        allowedDescriptions.forEach(desc => {
            listItems += `<li>${desc}</li>`;
        });

        // Add warning message
        const warningHtml = `
            <div class="occupancy-warning alert alert-warning mt-2">
                <i class="fas fa-exclamation-triangle"></i>
                <strong>Invalid occupancy combination for ${roomTypeName}.</strong><br>
                Only these combinations are allowed:
                <ul>
                    ${listItems}
                </ul>
            </div>
        `;

        $maxChildrenInput.closest('.row').after(warningHtml);

        // Highlight the fields
        $maxAdultsInput.addClass('is-invalid');
        $maxChildrenInput.addClass('is-invalid');
    }

    // Validate on room category change
    $roomCategorySelect.on('change', function() {
        validateRoomOccupancy();
    });

    // Validate on max adults change
    $maxAdultsInput.on('change keyup', function() {
        validateRoomOccupancy();
    });

    // Validate on max children change
    $maxChildrenInput.on('change keyup', function() {
        validateRoomOccupancy();
    });

    // Initial validation on page load
    validateRoomOccupancy();

    // Add form submission validation
    $('form').on('submit', function(e) {
        const selectedCategoryText = $roomCategorySelect.find('option:selected').text();
        const maxAdults = parseInt($maxAdultsInput.val()) || 0;
        const maxChildren = parseInt($maxChildrenInput.val()) || 0;

        if (selectedCategoryText === 'Standard Room') {
            // Check if the current combination is valid for Standard Room
            let isValid = false;

            for (const combo of standardRoomCombinations) {
                if (combo.adults === maxAdults && combo.children === maxChildren) {
                    isValid = true;
                    break;
                }
            }

            if (!isValid) {
                e.preventDefault();
                handleInvalidSubmission('Standard Room');
                return false;
            }
        } else if (selectedCategoryText === 'Double Bed Room') {
            // Check if the current combination is valid for Double Bed Room
            let isValid = false;

            for (const combo of doubleBedRoomCombinations) {
                if (combo.adults === maxAdults && combo.children === maxChildren) {
                    isValid = true;
                    break;
                }
            }

            if (!isValid) {
                e.preventDefault();
                handleInvalidSubmission('Double Bed Room', 'For Double Bed Room, the maximum occupancy is 4 people total, but the combination of 4 adults is not allowed.');
                return false;
            }
        }

        return true;
    });

    // Helper function to handle invalid form submission
    function handleInvalidSubmission(roomTypeName, customMessage = null) {
        // Scroll to the warning if it exists
        if ($('.occupancy-warning').length) {
            $('html, body').animate({
                scrollTop: $('.occupancy-warning').offset().top - 100
            }, 500);
        }

        // Use custom message if provided, otherwise use default message
        const message = customMessage || `Invalid occupancy combination for ${roomTypeName}. Please correct the values before saving.`;

        // Show error message using Botble's notification system if available
        if (typeof Botble !== 'undefined' && Botble.showError) {
            Botble.showError(message);
        } else {
            alert(message);
        }
    }
});
