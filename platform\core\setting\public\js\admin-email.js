$((function(){var n=$('[data-bb-toggle="admin-email"]');if(n.length){var e=n.find("#add"),a=parseInt(n.data("max"),10),t=n.data("emails");0===t.length&&(t=[""]);var i=function(){n.find("input[type=email]").length>=a?e.addClass("disabled"):e.removeClass("disabled")},l=function(){var a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return n.find("label").after('<div class="d-flex mt-2 more-email align-items-center">\n                <input type="email" class="form-control" placeholder="'.concat(e.data("placeholder"),'" name="admin_email[]" value="').concat(a||"",'" />\n                <a class="btn btn-link btn-sm text-danger bg-transparent border-0"><svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-minus" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">\n  <path stroke="none" d="M0 0h24v24H0z" fill="none"/>\n  <path d="M5 12l14 0" />\n</svg>\n</a>\n            </div>'))};n.on("click",".more-email > a",(function(){$(this).hasClass("disabled")||($(this).parent(".more-email").remove(),i())})),e.on("click",(function(n){n.preventDefault(),l(),i()})),t.forEach((function(n){l(n)})),i()}}));