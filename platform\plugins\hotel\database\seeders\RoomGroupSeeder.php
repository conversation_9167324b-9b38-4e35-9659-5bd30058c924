<?php

namespace Botble\Hotel\Database\Seeders;

use Bo<PERSON>ble\Base\Supports\BaseSeeder;
use Botble\Hotel\Models\RoomGroup;
use Botble\Hotel\Models\RoomGroupItem;
use Botble\Hotel\Models\RoomInventory;
use Illuminate\Support\Facades\DB;

class RoomGroupSeeder extends BaseSeeder
{
    public function run(): void
    {
        RoomGroup::truncate();
        RoomGroupItem::truncate();
        
        // Create the 2BHK package group
        $roomGroup = RoomGroup::create([
            'name' => '2BHK Package',
            'code' => '2BHK',
            'description' => 'A 2BHK house with two rooms (MRR-999 and MRR-777)',
        ]);
        
        // Add the two rooms to the group
        RoomGroupItem::create([
            'room_group_id' => $roomGroup->id,
            'room_id_code' => 'MRR-999',
        ]);
        
        RoomGroupItem::create([
            'room_group_id' => $roomGroup->id,
            'room_id_code' => 'MRR-777',
        ]);
        
        // Make sure the room inventory entries exist for these rooms
        $this->ensureRoomInventoryExists('MRR-999');
        $this->ensureRoomInventoryExists('MRR-777');
    }
    
    private function ensureRoomInventoryExists(string $roomIdCode): void
    {
        // Find a room to associate with this inventory item
        $roomId = DB::table('ht_rooms')->first()->id ?? null;
        
        if (!$roomId) {
            return;
        }
        
        RoomInventory::updateOrCreate(
            ['room_id_code' => $roomIdCode],
            [
                'room_id' => $roomId,
                'is_available' => true,
            ]
        );
    }
}
