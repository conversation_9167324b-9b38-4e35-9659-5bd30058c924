@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="booking-analytics-dashboard">
        <!-- Date Range Filter -->
        <div class="filter-section">
            <form id="date-range-form" method="GET" action="{{ route('booking.analytics.index') }}">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="date-range">Date Range</label>
                            <input type="text" class="form-control date-range-picker" id="date-range"
                                value="{{ $startDate->format('Y-m-d') }} - {{ $endDate->format('Y-m-d') }}">
                            <input type="hidden" name="start_date" id="start_date" value="{{ $startDate->format('Y-m-d') }}">
                            <input type="hidden" name="end_date" id="end_date" value="{{ $endDate->format('Y-m-d') }}">
                        </div>
                    </div>
                    <div class="col-md-6 d-flex align-items-end">
                        <div class="export-buttons">
                            <a href="{{ route('booking.analytics.export') }}" class="btn btn-excel export-btn" data-format="xlsx">
                                <i class="fas fa-file-excel"></i> Export Excel
                            </a>
                            <button type="button" class="btn btn-primary refresh-btn">
                                <i class="fas fa-sync-alt"></i> Refresh Data
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>

        <!-- Stats Cards -->
        <div class="row">
            <div class="col-xl-3 col-md-6">
                <div class="stats-card primary">
                    <div class="stats-title">Total Bookings</div>
                    <div class="stats-value">{{ number_format($totalBookings) }}</div>
                    <div class="stats-icon">
                        <i class="fas fa-calendar-check"></i>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6">
                <div class="stats-card success">
                    <div class="stats-title">Total Revenue</div>
                    <div class="stats-value">₹{{ number_format($totalRevenue) }}</div>
                    <div class="stats-icon">
                        <i class="fas fa-rupee-sign"></i>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6">
                <div class="stats-card info">
                    <div class="stats-title">Total Customers</div>
                    <div class="stats-value">{{ number_format($totalCustomers) }}</div>
                    <div class="stats-icon">
                        <i class="fas fa-users"></i>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6">
                <div class="stats-card warning">
                    <div class="stats-title">Room Availability</div>
                    <div class="stats-value">{{ $roomAvailability['available'] }}/{{ $roomAvailability['total'] }}</div>
                    <div class="stats-icon">
                        <i class="fas fa-bed"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Bookings Table -->
        <div class="analytics-card">
            <div class="card-header">
                <h5><i class="fas fa-list mr-2"></i> Recent Bookings</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table booking-table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Customer</th>
                                <th>Room</th>
                                <th>Check-in</th>
                                <th>Check-out</th>
                                <th>Amount</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @if(count($recentBookings) > 0)
                                @foreach($recentBookings as $booking)
                                    <tr>
                                        <td>{{ $booking->id }}</td>
                                        <td>
                                            @if($booking->customer)
                                                {{ $booking->customer->first_name }} {{ $booking->customer->last_name }}
                                            @else
                                                N/A
                                            @endif
                                        </td>
                                        <td>
                                            @if($booking->room && $booking->room->room)
                                                {{ $booking->room->room->name }}
                                                @if($booking->room_id_code)
                                                    ({{ $booking->room_id_code }})
                                                @endif
                                            @else
                                                N/A
                                            @endif
                                        </td>
                                        <td>{{ $booking->room && $booking->room->start_date ? Carbon\Carbon::parse($booking->room->start_date)->format('M d, Y') : 'N/A' }}</td>
                                        <td>{{ $booking->room && $booking->room->end_date ? Carbon\Carbon::parse($booking->room->end_date)->format('M d, Y') : 'N/A' }}</td>
                                        <td>₹{{ number_format($booking->getTotalAmount()) }}</td>
                                        <td>
                                            <span class="status-badge {{ strtolower($booking->status->getValue()) }}">
                                                {{ $booking->status->label() }}
                                            </span>
                                        </td>
                                        <td>
                                            <a href="{{ route('booking.edit', $booking->id) }}" class="btn btn-sm btn-info">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                @endforeach
                            @else
                                <tr>
                                    <td colspan="8" class="text-center">No bookings found</td>
                                </tr>
                            @endif
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="card-footer">
                <a href="{{ route('booking.index') }}" class="btn btn-primary">View All Bookings</a>
            </div>
        </div>
    </div>
@endsection
