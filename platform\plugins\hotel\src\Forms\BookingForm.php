<?php

namespace Botble\Hotel\Forms;

use Bo<PERSON>ble\Base\Facades\Assets;
use Bo<PERSON>ble\Base\Facades\Html;
use Botble\Base\Forms\FieldOptions\SelectFieldOption;
use Bo<PERSON>ble\Base\Forms\FieldOptions\StatusFieldOption;
use Botble\Base\Forms\Fields\MediaFileField;
use Botble\Base\Forms\Fields\MediaImageField;
use Botble\Base\Forms\Fields\SelectField;
use Botble\Base\Forms\FormAbstract;
use Botble\Hotel\Enums\BookingStatusEnum;
use Botble\Hotel\Http\Requests\UpdateBookingRequest;
use Botble\Hotel\Models\Booking;

class BookingForm extends FormAbstract
{
    public function setup(): void
    {
        try {
            Assets::addStyles(['jquery-ui'])
                ->addScripts(['jquery-ui', 'form-validation'])
                ->addScriptsDirectly([
                    'vendor/core/plugins/hotel/js/booking.js',
                    'vendor/core/plugins/hotel/js/room-groups.js',
                    'js/booking-occupancy-validation.js',
                ]);

            // Room ID Code options
            $roomIdCodeOptions = [];

            // Add MRR-101 to MRR-110
            for ($i = 101; $i <= 110; $i++) {
                $roomIdCodeOptions['MRR-' . $i] = 'MRR-' . $i;
            }

            // Add MRR-201
            $roomIdCodeOptions['MRR-201'] = 'MRR-201';

            // Add 2BHK package rooms
            $roomIdCodeOptions['MRR-777'] = 'MRR-777 (Standard Room - 2BHK)';
            $roomIdCodeOptions['MRR-999'] = 'MRR-999 (Double Bedroom - 2BHK)';

            // Add 2BHK package (for website bookings)
            $roomIdCodeOptions['2BHK'] = '2BHK Package (Both Rooms)';

            // Setup the model first
            $this->setupModel(new Booking());

            // If we're editing an existing booking and it has a room_id_code, make sure it's in the options
            $model = $this->getModel();
            if ($model && $model->exists && $model->room_id_code && !isset($roomIdCodeOptions[$model->room_id_code])) {
                $roomIdCodeOptions[$model->room_id_code] = $model->room_id_code;
            }
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Error setting up booking form: ' . $e->getMessage());
            // Continue with default options if there's an error
            $roomIdCodeOptions = [
                'MRR-101' => 'MRR-101',
                'MRR-102' => 'MRR-102',
                'MRR-103' => 'MRR-103',
                'MRR-104' => 'MRR-104',
                'MRR-105' => 'MRR-105',
                'MRR-106' => 'MRR-106',
                'MRR-107' => 'MRR-107',
                'MRR-108' => 'MRR-108',
                'MRR-109' => 'MRR-109',
                'MRR-110' => 'MRR-110',
                'MRR-201' => 'MRR-201',
                'MRR-777' => 'MRR-777 (Standard Room - 2BHK)',
                'MRR-999' => 'MRR-999 (Double Bedroom - 2BHK)',
                '2BHK' => '2BHK Package (Both Rooms)',
            ];
        }

        try {
            $this
                ->setValidatorClass(UpdateBookingRequest::class)
                ->withCustomFields()
                ->setFormOptions([
                    'enctype' => 'multipart/form-data',
                ])
                ->add('room_id_code', 'select', [
                    'label' => trans('Room ID Code') . ' *',
                    'required' => true,
                    'choices' => $roomIdCodeOptions,
                    'help_block' => [
                        'text' => trans('Select a room ID to assign to this booking. The room will only be marked as booked after you select a valid room ID.'),
                    ],
                    'attr' => [
                        'class' => 'form-control select-search-full',
                        'data-placeholder' => 'Select Room ID',
                    ],
                    'wrapper' => [
                        'class' => $this->getModel()->room_id_code ? 'form-group has-success' : 'form-group has-warning',
                    ],
                    'empty_value' => 'Select Room ID',
                    'default_value' => null,
                ])
                ->add('photo', MediaImageField::class, [
                    'label' => trans('Photo'),
                    'help_block' => [
                        'text' => trans('Optional. Upload a photo of the guest or room.'),
                    ],
                ])
                ->add('proof', MediaImageField::class, [
                    'label' => trans('Proof Document'),
                    'help_block' => [
                        'text' => trans('Optional. Upload an ID proof or any document.'),
                    ],
                ])
                ->add('signature', MediaImageField::class, [
                    'label' => trans('Signature'),
                    'help_block' => [
                        'text' => trans('Optional. Upload a signature image.'),
                    ],
                ])
                ->add('status', 'select', [
                    'label' => trans('core/base::tables.status'),
                    'choices' => BookingStatusEnum::labels(),
                ])
                ->setBreakFieldPoint('status');

            // Add metaboxes
            try {
                $this->addMetaBoxes([
                    'information' => [
                        'title' => trans('plugins/hotel::booking.booking_information'),
                        'content' => $this->renderBookingInfo(),
                        'attributes' => [
                            'style' => 'margin-top: 0',
                        ],
                    ],
                    'additional_services' => [
                        'title' => trans('plugins/hotel::booking.additional_services'),
                        'content' => view('plugins/hotel::booking-selected-services', ['booking' => $this->getModel()])->render() .
                            Html::link(
                                route('booking.additional-services.create', $this->getModel()->id),
                                trans('plugins/hotel::booking.add_additional_service'),
                                ['class' => 'btn btn-info mt-2']
                            ),
                    ],
                ]);
            } catch (\Exception $e) {
                \Illuminate\Support\Facades\Log::error('Error adding metaboxes to booking form: ' . $e->getMessage());
                // Continue without metaboxes if there's an error
            }
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Error setting up booking form fields: ' . $e->getMessage());
            // Add minimal fields if there's an error
            $this
                ->setValidatorClass(UpdateBookingRequest::class)
                ->withCustomFields()
                ->add('status', 'select', [
                    'label' => trans('core/base::tables.status'),
                    'choices' => BookingStatusEnum::labels(),
                ]);
        }
    }

    /**
     * Safely render the booking info view
     */
    protected function renderBookingInfo(): string
    {
        try {
            return view('plugins/hotel::booking-info', ['booking' => $this->getModel()])->render();
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Error rendering booking info: ' . $e->getMessage());
            return '<div class="alert alert-danger">Error loading booking information. Please check the logs for details.</div>';
        }
    }
}
