<?php

namespace Botble\Hotel\Http\Controllers;

use Bo<PERSON>ble\Base\Http\Controllers\BaseController;
use Botble\Hotel\Models\Room;
use Botble\Hotel\Models\RoomPrice;
use Botble\Hotel\Services\DynamicPricingService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class RoomPriceController extends BaseController
{
    protected $dynamicPricingService;

    public function __construct(DynamicPricingService $dynamicPricingService)
    {
        $this->dynamicPricingService = $dynamicPricingService;
    }

    /**
     * Display room pricing management page with future pricing support
     */
    public function index(Request $request)
    {
        $rooms = Room::with('category')->get();
        $roomCategories = RoomCategory::all();

        $query = RoomPrice::with(['room', 'roomCategory']);

        // Filter by room
        if ($request->filled('room_id')) {
            $query->where('room_id', $request->room_id);
        }

        // Filter by category
        if ($request->filled('room_category_id')) {
            $query->where('room_category_id', $request->room_category_id);
        }

        // Filter by pricing category
        if ($request->filled('pricing_category')) {
            $query->where('pricing_category', $request->pricing_category);
        }

        // Filter by date range
        if ($request->filled('date_from') && $request->filled('date_to')) {
            $query->forDateRange($request->date_from, $request->date_to);
        }

        // Filter by future pricing
        if ($request->filled('show_future_only')) {
            $query->future();
        }

        // Filter by approval status
        if ($request->filled('approval_status')) {
            $query->where('approval_status', $request->approval_status);
        }

        $roomPrices = $query->orderBy('start_date', 'desc')->paginate(15);

        // Get future pricing statistics
        $futurePricingStats = [
            'total_future_rules' => RoomPrice::future()->count(),
            'pending_approval' => RoomPrice::where('approval_status', RoomPrice::APPROVAL_PENDING)->count(),
            'promotional_active' => RoomPrice::promotional()->count(),
            'conflicts' => $this->getConflictCount(),
        ];

        return view('plugins/hotel::room-prices.index', compact(
            'rooms',
            'roomCategories',
            'roomPrices',
            'futurePricingStats'
        ));
    }

    /**
     * Store a new room price with future pricing support
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'pricing_category' => 'required|in:individual_room,room_category,all_rooms',
            'room_id' => 'required_if:pricing_category,individual_room|exists:ht_rooms,id',
            'room_category_id' => 'required_if:pricing_category,room_category|exists:ht_room_categories,id',
            'name' => 'required|string|max:255',
            'price' => 'required|numeric|min:0',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after:start_date',
            'priority' => 'required|integer|min:1|max:100',
            'override_priority' => 'nullable|integer|min:1|max:100',
            'pricing_type' => 'required|in:fixed,percentage,amount_adjust',
            'adjustment_value' => 'nullable|numeric',
            'days_of_week' => 'nullable|array',
            'days_of_week.*' => 'integer|between:0,6',
            'min_nights' => 'nullable|integer|min:1',
            'max_nights' => 'nullable|integer|min:1',
            'recurrence_pattern' => 'nullable|in:none,daily,weekly,monthly,yearly',
            'recurrence_end_date' => 'nullable|date|after:end_date',
            'is_promotional' => 'nullable|boolean',
            'promotional_expires_at' => 'nullable|date',
            'description' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        // Validate future pricing rules
        $validation = $this->dynamicPricingService->validateFuturePricing($request->all());
        if (!$validation['valid']) {
            return response()->json([
                'success' => false,
                'message' => 'Future pricing validation failed',
                'errors' => $validation['errors'],
                'warnings' => $validation['warnings']
            ], 422);
        }

        $data = $request->all();

        // Set approval status based on requirements
        $roomPrice = new RoomPrice($data);
        $data['approval_status'] = $roomPrice->requiresApproval() ?
            RoomPrice::APPROVAL_PENDING : RoomPrice::APPROVAL_APPROVED;

        $data['created_by_type'] = 'admin';
        $data['created_by_id'] = auth()->id();

        $roomPrice = RoomPrice::create($data);

        return response()->json([
            'success' => true,
            'message' => 'Room price created successfully',
            'data' => $roomPrice,
            'warnings' => $validation['warnings'] ?? []
        ]);
    }

    /**
     * Update an existing room price
     */
    public function update(Request $request, RoomPrice $roomPrice)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'price' => 'required|numeric|min:0',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after:start_date',
            'priority' => 'required|integer|min:1|max:100',
            'pricing_type' => 'required|in:fixed,percentage,amount_adjust',
            'adjustment_value' => 'nullable|numeric',
            'days_of_week' => 'nullable|array',
            'days_of_week.*' => 'integer|between:0,6',
            'min_nights' => 'nullable|integer|min:1',
            'max_nights' => 'nullable|integer|min:1',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $roomPrice->update($request->all());

        return response()->json([
            'success' => true,
            'message' => 'Room price updated successfully',
            'data' => $roomPrice
        ]);
    }

    /**
     * Delete a room price
     */
    public function destroy(RoomPrice $roomPrice)
    {
        $roomPrice->delete();

        return response()->json([
            'success' => true,
            'message' => 'Room price deleted successfully'
        ]);
    }

    /**
     * Toggle active status
     */
    public function toggleActive(RoomPrice $roomPrice)
    {
        $roomPrice->update(['is_active' => !$roomPrice->is_active]);

        return response()->json([
            'success' => true,
            'message' => 'Room price status updated successfully',
            'is_active' => $roomPrice->is_active
        ]);
    }

    /**
     * Get pricing preview for a date range
     */
    public function preview(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'room_id' => 'required|exists:ht_rooms,id',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after:start_date',
            'rooms' => 'nullable|integer|min:1',
            'guests' => 'nullable|integer|min:1',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        $room = Room::findOrFail($request->room_id);

        $pricing = $this->dynamicPricingService->calculateRoomPrice(
            $room,
            $request->start_date,
            $request->end_date,
            $request->rooms ?? 1,
            $request->guests ?? 1
        );

        return response()->json([
            'success' => true,
            'pricing' => $pricing
        ]);
    }

    /**
     * Bulk create seasonal pricing
     */
    public function bulkCreateSeasonal(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'room_ids' => 'required|array',
            'room_ids.*' => 'exists:ht_rooms,id',
            'seasons' => 'required|array',
            'seasons.*.name' => 'required|string',
            'seasons.*.start_date' => 'required|date',
            'seasons.*.end_date' => 'required|date|after:seasons.*.start_date',
            'seasons.*.price_adjustment' => 'required|numeric',
            'seasons.*.adjustment_type' => 'required|in:fixed,percentage,amount_adjust',
            'seasons.*.priority' => 'required|integer|min:1',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        $created = [];

        foreach ($request->room_ids as $roomId) {
            foreach ($request->seasons as $season) {
                $roomPrice = RoomPrice::create([
                    'room_id' => $roomId,
                    'name' => $season['name'],
                    'price' => $season['adjustment_type'] === 'fixed' ? $season['price_adjustment'] : 0,
                    'start_date' => $season['start_date'],
                    'end_date' => $season['end_date'],
                    'priority' => $season['priority'],
                    'pricing_type' => $season['adjustment_type'],
                    'adjustment_value' => $season['adjustment_type'] !== 'fixed' ? $season['price_adjustment'] : null,
                    'is_active' => true,
                ]);

                $created[] = $roomPrice;
            }
        }

        return response()->json([
            'success' => true,
            'message' => 'Seasonal pricing created successfully',
            'created_count' => count($created)
        ]);
    }

    /**
     * Get future pricing calendar view
     */
    public function calendar(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'room_id' => 'required|exists:ht_rooms,id',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after:start_date',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        $room = Room::findOrFail($request->room_id);
        $startDate = $request->start_date ?? now()->format('Y-m-d');
        $endDate = $request->end_date ?? now()->addMonths(3)->format('Y-m-d');

        $calendar = $this->dynamicPricingService->getFuturePricingCalendar($room, $startDate, $endDate);

        return response()->json([
            'success' => true,
            'calendar' => $calendar,
            'room' => $room
        ]);
    }

    /**
     * Approve or reject pricing rule
     */
    public function approve(Request $request, RoomPrice $roomPrice)
    {
        $validator = Validator::make($request->all(), [
            'action' => 'required|in:approve,reject',
            'notes' => 'nullable|string|max:500',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        $roomPrice->update([
            'approval_status' => $request->action === 'approve' ?
                RoomPrice::APPROVAL_APPROVED : RoomPrice::APPROVAL_REJECTED,
            'approved_by' => auth()->id(),
            'approved_at' => now(),
            'approval_notes' => $request->notes,
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Pricing rule ' . $request->action . 'd successfully'
        ]);
    }

    /**
     * Generate bulk future pricing
     */
    public function bulkFuturePricing(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'seasons' => 'required|array',
            'targets' => 'required|array',
            'seasons.*.name' => 'required|string',
            'seasons.*.start_date' => 'required|date',
            'seasons.*.end_date' => 'required|date|after:seasons.*.start_date',
            'seasons.*.price' => 'required|numeric|min:0',
            'seasons.*.pricing_type' => 'required|in:fixed,percentage,amount_adjust',
            'targets.*.type' => 'required|in:individual_room,room_category,all_rooms',
            'targets.*.id' => 'required|integer',
            'targets.*.name' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        $result = $this->dynamicPricingService->generateBulkSeasonalPricing($request->all());

        // Create the pricing rules
        $created = [];
        foreach ($result['generated'] as $pricingData) {
            $created[] = RoomPrice::create($pricingData);
        }

        return response()->json([
            'success' => true,
            'message' => 'Bulk future pricing generated successfully',
            'created_count' => count($created),
            'errors' => $result['errors']
        ]);
    }

    /**
     * Get pricing conflicts
     */
    public function conflicts(Request $request)
    {
        $conflicts = [];

        $query = RoomPrice::with(['room', 'roomCategory'])
            ->active();

        // Only add approval status filter if the column exists
        if (\Schema::hasColumn('ht_room_prices', 'approval_status')) {
            $query->approved();
        }

        $roomPrices = $query->get();

        foreach ($roomPrices as $price) {
            $priceConflicts = $price->hasConflicts();
            if (!empty($priceConflicts)) {
                $conflicts[] = [
                    'price' => $price,
                    'conflicts' => $priceConflicts
                ];
            }
        }

        return response()->json([
            'success' => true,
            'conflicts' => $conflicts,
            'count' => count($conflicts)
        ]);
    }

    /**
     * Export pricing data to CSV
     */
    public function export(Request $request)
    {
        $query = RoomPrice::with(['room', 'roomCategory']);

        // Apply filters
        if ($request->filled('room_id')) {
            $query->where('room_id', $request->room_id);
        }

        if ($request->filled('date_from') && $request->filled('date_to')) {
            $query->forDateRange($request->date_from, $request->date_to);
        }

        $prices = $query->get();

        $csvData = [];
        $csvData[] = [
            'ID', 'Room', 'Category', 'Name', 'Price', 'Start Date', 'End Date',
            'Priority', 'Pricing Type', 'Status', 'Approval Status', 'Created At'
        ];

        foreach ($prices as $price) {
            $csvData[] = [
                $price->id,
                $price->room ? $price->room->name : 'All Rooms',
                $price->roomCategory ? $price->roomCategory->name : 'N/A',
                $price->name,
                $price->price,
                $price->start_date->format('Y-m-d'),
                $price->end_date->format('Y-m-d'),
                $price->getEffectivePriority(),
                $price->pricing_type,
                $price->is_active ? 'Active' : 'Inactive',
                \Schema::hasColumn('ht_room_prices', 'approval_status') ?
                    ucfirst($price->approval_status) : 'N/A',
                $price->created_at->format('Y-m-d H:i:s')
            ];
        }

        $filename = 'room_pricing_' . now()->format('Y_m_d_H_i_s') . '.csv';

        return response()->streamDownload(function () use ($csvData) {
            $file = fopen('php://output', 'w');
            foreach ($csvData as $row) {
                fputcsv($file, $row);
            }
            fclose($file);
        }, $filename, [
            'Content-Type' => 'text/csv',
        ]);
    }

    /**
     * Get conflict count for statistics
     */
    protected function getConflictCount(): int
    {
        $count = 0;
        $query = RoomPrice::active();

        // Only add approval status filter if the column exists
        if (\Schema::hasColumn('ht_room_prices', 'approval_status')) {
            $query->approved();
        }

        $roomPrices = $query->get();

        foreach ($roomPrices as $price) {
            if (!empty($price->hasConflicts())) {
                $count++;
            }
        }

        return $count;
    }
}
