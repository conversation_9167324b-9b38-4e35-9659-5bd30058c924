.auth-card {
    background-color: rgba(248, 249, 250, 1) !important;

    .card-header {
        background-color: rgba(248, 249, 250, 1) !important;
    }

    form {
        svg {
            stroke-width: 1.25;
            width: 24px;
            height: 24px;
        }

        .auth-input-icon {
            position: absolute;
            top: 3px;
            left: 1px;
            z-index: 10;
            background: transparent;
            border: 0;
        }

        .form-control {
            background-color: #fff;
        }
    }

    .text-end {
        text-align: right !important;
    }

    .text-decoration-underline {
        text-decoration: underline !important;
    }

    .gap-3 {
        gap: 1rem !important;
    }

    .pb-0 {
        padding-bottom: 0!important;
    }

    .ps-5 {
        padding-left: 3rem!important;
    }

    .form-check {
        display: block;
        min-height: 1.5rem;
        padding-left: 1.5em;
        margin-bottom: 0.125rem;

        &.form-check-inline {
            display: inline-block;
            margin-right: 1rem;
        }

        .form-check-input {
            margin-left: -1.5em;

            &:checked {
                background-color: var(--primary-color);
                border-color: var(--primary-color);
            }
        }
    }

    .shop-url-status {
        top: 0;
        right: 0;
    }

    .text-primary {
        color: var(--primary-color) !important;
    }
}
