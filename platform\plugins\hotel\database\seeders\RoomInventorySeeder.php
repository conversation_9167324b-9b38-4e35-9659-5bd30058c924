<?php

namespace Botble\Hotel\Database\Seeders;

use Botble\Base\Supports\BaseSeeder;
use Botble\Hotel\Models\Room;
use Botble\Hotel\Models\RoomInventory;

class RoomInventorySeeder extends BaseSeeder
{
    public function run(): void
    {
        RoomInventory::query()->truncate();
        
        // Get all rooms
        $rooms = Room::query()->get();
        
        foreach ($rooms as $room) {
            // Get room category name to determine which room IDs to create
            $categoryName = $room->category->name ?? '';
            $roomIdCodes = [];
            
            // Assign room IDs based on category
            if (str_contains(strtolower($categoryName), 'standard')) {
                // Standard rooms: MRR-101 to MRR-104, MRR-107 to MRR-110
                $roomIdCodes = ['MRR-101', 'MRR-102', 'MRR-103', 'MRR-104', 'MRR-107', 'MRR-108', 'MRR-109', 'MRR-110'];
            } elseif (str_contains(strtolower($categoryName), 'double')) {
                // Double bed rooms: MRR-105, MRR-106
                $roomIdCodes = ['MRR-105', 'MRR-106'];
            } elseif (str_contains(strtolower($categoryName), 'luxury')) {
                // Luxury rooms: MRR-201
                $roomIdCodes = ['MRR-201'];
            } elseif (str_contains(strtolower($categoryName), '2bhk')) {
                // 2BHK rooms
                $roomIdCodes = ['2BHK'];
            } else {
                // For any other room types, create generic room IDs
                for ($i = 1; $i <= $room->number_of_rooms; $i++) {
                    $roomIdCodes[] = 'ROOM-' . $room->id . '-' . $i;
                }
            }
            
            // Create room inventory records
            foreach ($roomIdCodes as $roomIdCode) {
                RoomInventory::query()->create([
                    'room_id' => $room->id,
                    'room_id_code' => $roomIdCode,
                    'is_available' => true,
                ]);
            }
        }
    }
}
