<?php

namespace Botble\Hotel\Database\Seeders;

use Botble\Hotel\Models\Room;
use Botble\Hotel\Models\RoomPrice;
use Carbon\Carbon;
use Illuminate\Database\Seeder;

class SeasonalPricingSampleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get all rooms
        $rooms = Room::where('status', 'published')->get();

        if ($rooms->isEmpty()) {
            $this->command->info('No rooms found. Please create rooms first.');
            return;
        }

        $this->command->info('Creating sample seasonal pricing data...');

        // Create Function Season pricing (your specific example)
        $this->createFunctionSeasonPricing($rooms);

        // Create other seasonal pricing examples
        $this->createPeakSeasonPricing($rooms);
        $this->createFestivalSeasonPricing($rooms);
        $this->createWeddingSeasonPricing($rooms);

        $this->command->info('Sample seasonal pricing data created successfully!');
    }

    /**
     * Create Function Season pricing (Dec 15 - Jan 15, +20%)
     */
    protected function createFunctionSeasonPricing($rooms): void
    {
        $startDate = Carbon::now()->addMonths(1)->day(15); // Next month 15th
        $endDate = $startDate->copy()->addMonth()->day(15); // Following month 15th

        foreach ($rooms as $room) {
            $basePrice = $room->price;
            $seasonalPrice = $basePrice * 1.20; // 20% increase

            RoomPrice::create([
                'room_id' => $room->id,
                'pricing_category' => 'individual_room',
                'name' => 'Function Season - ' . $room->name,
                'description' => 'Special pricing for function season with enhanced services',
                'price' => $seasonalPrice,
                'start_date' => $startDate,
                'end_date' => $endDate,
                'priority' => 15,
                'is_active' => true,
                'pricing_type' => 'fixed',
                'season_type' => RoomPrice::SEASON_FUNCTION,
                'is_future_pricing' => true,
                'price_reason' => 'Increased demand during function season',
                'customer_display_message' => 'Function Season Premium - Enhanced services and amenities for special events',
                'approval_status' => RoomPrice::APPROVAL_APPROVED,
                'approved_by' => 1,
                'approved_at' => now(),
            ]);
        }

        $this->command->info("Created Function Season pricing for {$rooms->count()} rooms");
    }

    /**
     * Create Peak Season pricing (Nov 1 - Feb 28, +30%)
     */
    protected function createPeakSeasonPricing($rooms): void
    {
        $currentYear = Carbon::now()->year;
        $startDate = Carbon::create($currentYear, 11, 1);
        $endDate = Carbon::create($currentYear + 1, 2, 28);

        // Only create if the dates are in the future
        if ($startDate->isFuture()) {
            foreach ($rooms as $room) {
                $basePrice = $room->price;
                $seasonalPrice = $basePrice * 1.30; // 30% increase

                RoomPrice::create([
                    'room_id' => $room->id,
                    'pricing_category' => 'individual_room',
                    'name' => 'Peak Season - ' . $room->name,
                    'description' => 'Peak tourist season with premium amenities',
                    'price' => $seasonalPrice,
                    'start_date' => $startDate,
                    'end_date' => $endDate,
                    'priority' => 20,
                    'is_active' => true,
                    'pricing_type' => 'fixed',
                    'season_type' => RoomPrice::SEASON_PEAK,
                    'is_future_pricing' => true,
                    'price_reason' => 'Peak tourist season pricing',
                    'customer_display_message' => 'Peak Season - Premium experience with full amenities and services',
                    'approval_status' => RoomPrice::APPROVAL_APPROVED,
                    'approved_by' => 1,
                    'approved_at' => now(),
                ]);
            }

            $this->command->info("Created Peak Season pricing for {$rooms->count()} rooms");
        }
    }

    /**
     * Create Festival Season pricing (Oct 1 - Nov 30, +25%)
     */
    protected function createFestivalSeasonPricing($rooms): void
    {
        $currentYear = Carbon::now()->year;
        $startDate = Carbon::create($currentYear, 10, 1);
        $endDate = Carbon::create($currentYear, 11, 30);

        // If current date is past October, use next year
        if ($startDate->isPast()) {
            $startDate = Carbon::create($currentYear + 1, 10, 1);
            $endDate = Carbon::create($currentYear + 1, 11, 30);
        }

        foreach ($rooms as $room) {
            $basePrice = $room->price;
            $seasonalPrice = $basePrice * 1.25; // 25% increase

            RoomPrice::create([
                'room_id' => $room->id,
                'pricing_category' => 'individual_room',
                'name' => 'Festival Season - ' . $room->name,
                'description' => 'Special pricing during festival celebrations',
                'price' => $seasonalPrice,
                'start_date' => $startDate,
                'end_date' => $endDate,
                'priority' => 18,
                'is_active' => true,
                'pricing_type' => 'fixed',
                'season_type' => RoomPrice::SEASON_FESTIVAL,
                'is_future_pricing' => true,
                'price_reason' => 'Festival season special pricing',
                'customer_display_message' => 'Festival Season - Special cultural experiences and celebrations included',
                'approval_status' => RoomPrice::APPROVAL_APPROVED,
                'approved_by' => 1,
                'approved_at' => now(),
            ]);
        }

        $this->command->info("Created Festival Season pricing for {$rooms->count()} rooms");
    }

    /**
     * Create Wedding Season pricing (Nov 15 - Mar 15, +35%)
     */
    protected function createWeddingSeasonPricing($rooms): void
    {
        $currentYear = Carbon::now()->year;
        $startDate = Carbon::create($currentYear, 11, 15);
        $endDate = Carbon::create($currentYear + 1, 3, 15);

        // If current date is past November 15, use next year
        if ($startDate->isPast()) {
            $startDate = Carbon::create($currentYear + 1, 11, 15);
            $endDate = Carbon::create($currentYear + 2, 3, 15);
        }

        foreach ($rooms as $room) {
            $basePrice = $room->price;
            $seasonalPrice = $basePrice * 1.35; // 35% increase

            RoomPrice::create([
                'room_id' => $room->id,
                'pricing_category' => 'individual_room',
                'name' => 'Wedding Season - ' . $room->name,
                'description' => 'Premium wedding season with exclusive services',
                'price' => $seasonalPrice,
                'start_date' => $startDate,
                'end_date' => $endDate,
                'priority' => 25,
                'is_active' => true,
                'pricing_type' => 'fixed',
                'season_type' => RoomPrice::SEASON_WEDDING,
                'is_future_pricing' => true,
                'price_reason' => 'Wedding season premium pricing',
                'customer_display_message' => 'Wedding Season - Exclusive wedding services, decorations, and premium amenities',
                'approval_status' => RoomPrice::APPROVAL_APPROVED,
                'approved_by' => 1,
                'approved_at' => now(),
            ]);
        }

        $this->command->info("Created Wedding Season pricing for {$rooms->count()} rooms");
    }

    /**
     * Create sample weekend pricing (every weekend, +15%)
     */
    protected function createWeekendPricing($rooms): void
    {
        $startDate = Carbon::now()->next(Carbon::FRIDAY);
        $endDate = $startDate->copy()->addMonths(3);

        foreach ($rooms as $room) {
            $basePrice = $room->price;
            $weekendPrice = $basePrice * 1.15; // 15% increase

            RoomPrice::create([
                'room_id' => $room->id,
                'pricing_category' => 'individual_room',
                'name' => 'Weekend Premium - ' . $room->name,
                'description' => 'Weekend premium pricing',
                'price' => $weekendPrice,
                'start_date' => $startDate,
                'end_date' => $endDate,
                'priority' => 10,
                'is_active' => true,
                'pricing_type' => 'fixed',
                'days_of_week' => [5, 6], // Friday and Saturday
                'price_reason' => 'Weekend premium pricing',
                'customer_display_message' => 'Weekend Premium - Enhanced weekend experience',
                'approval_status' => RoomPrice::APPROVAL_APPROVED,
                'approved_by' => 1,
                'approved_at' => now(),
            ]);
        }

        $this->command->info("Created Weekend pricing for {$rooms->count()} rooms");
    }
}
