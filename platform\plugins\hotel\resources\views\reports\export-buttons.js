/**
 * Export Button Functions
 * This file contains the functions for the export buttons in the booking analytics dashboard
 */

// Function to handle Excel export
function handleExcelExport(e) {
    e.preventDefault();
    
    console.log('Export to Excel button clicked');
    
    // Show loading indicator
    $(this).addClass('disabled').html('<i class="fas fa-spinner fa-spin"></i> Exporting...');
    
    // Get the current filter values
    const startDate = $('input[name="start_date"]').val();
    const endDate = $('input[name="end_date"]').val();
    const roomCategoryId = $('#room-category').val();
    
    // Build the export URL
    let exportUrl = $(this).attr('href');
    
    // Add filter parameters if they exist
    if (startDate && endDate) {
        exportUrl = updateQueryStringParameter(exportUrl, 'start_date', startDate);
        exportUrl = updateQueryStringParameter(exportUrl, 'end_date', endDate);
    }
    
    if (roomCategoryId) {
        exportUrl = updateQueryStringParameter(exportUrl, 'room_category_id', roomCategoryId);
    }
    
    // Redirect to the export URL
    window.location.href = exportUrl;
    
    // Reset button after a delay
    setTimeout(() => {
        $(this).removeClass('disabled').html('<i class="fas fa-file-excel"></i> Export to Excel');
    }, 3000);
    
    return false;
}

// Function to handle CSV export
function handleCsvExport(e) {
    e.preventDefault();
    
    console.log('Export to CSV button clicked');
    
    // Show loading indicator
    $(this).addClass('disabled').html('<i class="fas fa-spinner fa-spin"></i> Exporting...');
    
    // Get the current filter values
    const startDate = $('input[name="start_date"]').val();
    const endDate = $('input[name="end_date"]').val();
    const roomCategoryId = $('#room-category').val();
    
    // Build the export URL
    let exportUrl = $(this).attr('href');
    
    // Add filter parameters if they exist
    if (startDate && endDate) {
        exportUrl = updateQueryStringParameter(exportUrl, 'start_date', startDate);
        exportUrl = updateQueryStringParameter(exportUrl, 'end_date', endDate);
    }
    
    if (roomCategoryId) {
        exportUrl = updateQueryStringParameter(exportUrl, 'room_category_id', roomCategoryId);
    }
    
    // Redirect to the export URL
    window.location.href = exportUrl;
    
    // Reset button after a delay
    setTimeout(() => {
        $(this).removeClass('disabled').html('<i class="fas fa-file-csv"></i> Export to CSV');
    }, 3000);
    
    return false;
}

// Helper function to update query string parameters
function updateQueryStringParameter(uri, key, value) {
    var re = new RegExp("([?&])" + key + "=.*?(&|$)", "i");
    var separator = uri.indexOf('?') !== -1 ? "&" : "?";
    
    if (uri.match(re)) {
        return uri.replace(re, '$1' + key + "=" + value + '$2');
    } else {
        return uri + separator + key + "=" + value;
    }
}

// Initialize export button event handlers
function initializeExportButtons() {
    console.log('Initializing export buttons');
    
    // Excel export button
    $('#export-excel').off('click').on('click', handleExcelExport);
    
    // CSV export button
    $('#export-csv').off('click').on('click', handleCsvExport);
    
    // Add direct onclick handlers as a fallback
    document.getElementById('export-excel').onclick = function(e) {
        return handleExcelExport.call(this, e);
    };
    
    document.getElementById('export-csv').onclick = function(e) {
        return handleCsvExport.call(this, e);
    };
    
    console.log('Export buttons initialized');
}

// Export functions
window.handleExcelExport = handleExcelExport;
window.handleCsvExport = handleCsvExport;
window.initializeExportButtons = initializeExportButtons;
