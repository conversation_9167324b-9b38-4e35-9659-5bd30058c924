<?php

namespace Botble\Hotel\Models;

use Botble\Base\Models\BaseModel;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Botble\Hotel\Models\RoomInventory;

class RoomGroupItem extends BaseModel
{
    protected $table = 'ht_room_group_items';

    protected $fillable = [
        'room_group_id',
        'room_id_code',
    ];

    public function group(): BelongsTo
    {
        return $this->belongsTo(RoomGroup::class, 'room_group_id');
    }

    public function roomInventory()
    {
        return RoomInventory::where('room_id_code', $this->room_id_code)->first();
    }
}
