<?php

namespace Botble\Hotel\Models;

use Botble\Base\Models\BaseModel;
use Carbon\Carbon;

class PricingRule extends BaseModel
{
    protected $table = 'ht_pricing_rules';

    protected $fillable = [
        'name',
        'type',
        'is_active',
        'priority',
        'conditions',
        'adjustment_type',
        'adjustment_value',
        'valid_from',
        'valid_to',
        'applicable_rooms',
        'applicable_categories',
        'description',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'priority' => 'integer',
        'conditions' => 'array',
        'adjustment_value' => 'decimal:2',
        'valid_from' => 'date',
        'valid_to' => 'date',
        'applicable_rooms' => 'array',
        'applicable_categories' => 'array',
    ];

    // Pricing rule types
    const TYPE_SEASONAL = 'seasonal';
    const TYPE_DEMAND_BASED = 'demand_based';
    const TYPE_EARLY_BIRD = 'early_bird';
    const TYPE_LAST_MINUTE = 'last_minute';
    const TYPE_GROUP_DISCOUNT = 'group_discount';
    const TYPE_WEEKDAY_WEEKEND = 'weekday_weekend';
    const TYPE_LENGTH_OF_STAY = 'length_of_stay';

    // Adjustment types
    const ADJUSTMENT_PERCENTAGE = 'percentage';
    const ADJUSTMENT_FIXED_AMOUNT = 'fixed_amount';
    const ADJUSTMENT_FIXED_PRICE = 'fixed_price';

    /**
     * Scope to get active rules
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get rules by priority (highest first)
     */
    public function scopeByPriority($query)
    {
        return $query->orderBy('priority', 'desc');
    }

    /**
     * Scope to get rules valid for a specific date
     */
    public function scopeValidForDate($query, $date)
    {
        $date = Carbon::parse($date);
        
        return $query->where(function ($q) use ($date) {
            $q->whereNull('valid_from')
              ->orWhere('valid_from', '<=', $date);
        })->where(function ($q) use ($date) {
            $q->whereNull('valid_to')
              ->orWhere('valid_to', '>=', $date);
        });
    }

    /**
     * Check if this rule applies to a specific room
     */
    public function appliesToRoom($roomId, $categoryId = null): bool
    {
        // If no specific rooms/categories defined, applies to all
        if (!$this->applicable_rooms && !$this->applicable_categories) {
            return true;
        }

        // Check specific rooms
        if ($this->applicable_rooms && in_array($roomId, $this->applicable_rooms)) {
            return true;
        }

        // Check room categories
        if ($this->applicable_categories && $categoryId && in_array($categoryId, $this->applicable_categories)) {
            return true;
        }

        return false;
    }

    /**
     * Check if conditions are met for this booking
     */
    public function conditionsMet($bookingData): bool
    {
        if (!$this->conditions) {
            return true;
        }

        foreach ($this->conditions as $condition => $value) {
            switch ($condition) {
                case 'min_advance_days':
                    $bookingDate = Carbon::parse($bookingData['booking_date'] ?? now());
                    $checkInDate = Carbon::parse($bookingData['check_in']);
                    if ($bookingDate->diffInDays($checkInDate) < $value) {
                        return false;
                    }
                    break;

                case 'max_advance_days':
                    $bookingDate = Carbon::parse($bookingData['booking_date'] ?? now());
                    $checkInDate = Carbon::parse($bookingData['check_in']);
                    if ($bookingDate->diffInDays($checkInDate) > $value) {
                        return false;
                    }
                    break;

                case 'min_nights':
                    if (($bookingData['nights'] ?? 1) < $value) {
                        return false;
                    }
                    break;

                case 'max_nights':
                    if (($bookingData['nights'] ?? 1) > $value) {
                        return false;
                    }
                    break;

                case 'min_rooms':
                    if (($bookingData['rooms'] ?? 1) < $value) {
                        return false;
                    }
                    break;

                case 'min_guests':
                    if (($bookingData['guests'] ?? 1) < $value) {
                        return false;
                    }
                    break;

                case 'days_of_week':
                    $checkInDate = Carbon::parse($bookingData['check_in']);
                    if (!in_array($checkInDate->dayOfWeek, $value)) {
                        return false;
                    }
                    break;

                case 'occupancy_threshold':
                    // This would require checking current occupancy
                    // Implementation depends on your occupancy tracking system
                    break;
            }
        }

        return true;
    }

    /**
     * Calculate the adjusted price
     */
    public function calculateAdjustedPrice($basePrice): float
    {
        switch ($this->adjustment_type) {
            case self::ADJUSTMENT_PERCENTAGE:
                return $basePrice + ($basePrice * $this->adjustment_value / 100);

            case self::ADJUSTMENT_FIXED_AMOUNT:
                return $basePrice + $this->adjustment_value;

            case self::ADJUSTMENT_FIXED_PRICE:
                return $this->adjustment_value;

            default:
                return $basePrice;
        }
    }

    /**
     * Get human-readable rule type
     */
    public function getTypeNameAttribute(): string
    {
        return match($this->type) {
            self::TYPE_SEASONAL => 'Seasonal Pricing',
            self::TYPE_DEMAND_BASED => 'Demand-Based Pricing',
            self::TYPE_EARLY_BIRD => 'Early Bird Discount',
            self::TYPE_LAST_MINUTE => 'Last Minute Pricing',
            self::TYPE_GROUP_DISCOUNT => 'Group Discount',
            self::TYPE_WEEKDAY_WEEKEND => 'Weekday/Weekend Pricing',
            self::TYPE_LENGTH_OF_STAY => 'Length of Stay Discount',
            default => ucfirst(str_replace('_', ' ', $this->type)),
        };
    }
}
