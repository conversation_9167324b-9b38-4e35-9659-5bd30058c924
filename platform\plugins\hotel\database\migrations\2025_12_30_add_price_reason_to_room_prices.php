<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add columns individually with try-catch to handle existing columns
        try {
            if (!Schema::hasColumn('ht_room_prices', 'price_reason')) {
                Schema::table('ht_room_prices', function (Blueprint $table) {
                    $table->string('price_reason')->nullable()->after('description');
                });
            }
        } catch (\Exception $e) {
            // Column might already exist, continue
        }

        try {
            if (!Schema::hasColumn('ht_room_prices', 'season_type')) {
                Schema::table('ht_room_prices', function (Blueprint $table) {
                    $table->string('season_type')->nullable();
                });
            }
        } catch (\Exception $e) {
            // Column might already exist, continue
        }

        try {
            if (!Schema::hasColumn('ht_room_prices', 'is_future_pricing')) {
                Schema::table('ht_room_prices', function (Blueprint $table) {
                    $table->boolean('is_future_pricing')->default(false);
                });
            }
        } catch (\Exception $e) {
            // Column might already exist, continue
        }

        try {
            if (!Schema::hasColumn('ht_room_prices', 'customer_display_message')) {
                Schema::table('ht_room_prices', function (Blueprint $table) {
                    $table->text('customer_display_message')->nullable();
                });
            }
        } catch (\Exception $e) {
            // Column might already exist, continue
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('ht_room_prices', function (Blueprint $table) {
            $table->dropColumn(['price_reason', 'season_type', 'is_future_pricing', 'customer_display_message']);
        });
    }
};