<?php

namespace Botble\Hotel\Providers;

use Botble\Base\Events\RenderingAdminWidgetEvent;
use Botble\Hotel\Events\BookingCreated;
use Botble\Hotel\Events\BookingStatusChanged;
use Botble\Hotel\Events\BookingUpdated;
use Bo<PERSON>ble\Hotel\Listeners\AddSitemapListener;
use Botble\Hotel\Listeners\GenerateInvoiceListener;
use Botble\Hotel\Listeners\RegisterBookingReportsWidget;
use Botble\Hotel\Listeners\SendConfirmationEmail;
use Botble\Hotel\Listeners\SendStatusChangedNotificationListener;
use Botble\Hotel\Listeners\UpdateRoomAvailabilityListener;
use Botble\Hotel\Listeners\UpdateRoomGroupAvailabilityListener;
use Botble\Hotel\Listeners\FreeRoomOnStatusChangeListener;
use Botble\Hotel\Listeners\SendSmsNotificationListener;
use Botble\Hotel\Listeners\UpdateCheckInOutTimeListener;
use Bo<PERSON>ble\Theme\Events\RenderingSiteMapEvent;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;

class EventServiceProvider extends ServiceProvider
{
    protected $listen = [];

    public function __construct($app)
    {
        $this->listen = [
            RenderingSiteMapEvent::class => [
                AddSitemapListener::class,
            ],
            BookingCreated::class => [
                GenerateInvoiceListener::class,
                SendConfirmationEmail::class,
                UpdateRoomAvailabilityListener::class,
                UpdateRoomGroupAvailabilityListener::class,
            ],
        ];

        // Only register SMS notification listener if the class exists and Twilio SDK is available
        if (class_exists(SendSmsNotificationListener::class)) {
            // We'll still register the listener even if Twilio SDK is missing
            // The listener itself will handle the case when Twilio is not available
            $this->listen[BookingCreated::class][] = SendSmsNotificationListener::class;
        }

        parent::__construct($app);
    }

    public function boot()
    {
        parent::boot();

        // Add BookingUpdated and BookingStatusChanged listeners
        $this->listen[BookingUpdated::class] = [
            UpdateRoomAvailabilityListener::class,
            UpdateRoomGroupAvailabilityListener::class,
        ];

        $this->listen[BookingStatusChanged::class] = [
            SendStatusChangedNotificationListener::class,
            UpdateCheckInOutTimeListener::class,
            UpdateRoomGroupAvailabilityListener::class,
        ];

        // Only register FreeRoomOnStatusChangeListener if the class exists
        if (class_exists(FreeRoomOnStatusChangeListener::class)) {
            $this->listen[BookingStatusChanged::class][] = FreeRoomOnStatusChangeListener::class;
        }

        $this->listen[RenderingAdminWidgetEvent::class] = [
            RegisterBookingReportsWidget::class,
        ];
    }
}
