<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Enhance existing ht_room_prices table with future pricing capabilities
        Schema::table('ht_room_prices', function (Blueprint $table) {
            // Pricing scope and targeting
            $table->enum('pricing_category', ['individual_room', 'room_category', 'all_rooms'])
                  ->default('individual_room')
                  ->after('room_id');
            
            $table->unsignedBigInteger('room_category_id')->nullable()->after('pricing_category');
            $table->foreign('room_category_id')->references('id')->on('ht_room_categories')->onDelete('cascade');
            
            // Recurrence patterns for future pricing
            $table->enum('recurrence_pattern', ['none', 'daily', 'weekly', 'monthly', 'yearly'])
                  ->default('none')
                  ->after('max_nights');
            
            $table->date('recurrence_end_date')->nullable()->after('recurrence_pattern');
            $table->json('recurrence_config')->nullable()->after('recurrence_end_date'); // Store specific days, weeks, etc.
            
            // Enhanced priority and conflict resolution
            $table->integer('override_priority')->default(1)->after('priority');
            $table->boolean('is_promotional')->default(false)->after('is_active');
            $table->datetime('promotional_expires_at')->nullable()->after('is_promotional');
            
            // Approval workflow
            $table->enum('approval_status', ['pending', 'approved', 'rejected'])
                  ->default('approved')
                  ->after('promotional_expires_at');
            
            $table->unsignedBigInteger('approved_by')->nullable()->after('approval_status');
            $table->datetime('approved_at')->nullable()->after('approved_by');
            $table->text('approval_notes')->nullable()->after('approved_at');
            
            // Soft deletes for history preservation
            $table->softDeletes();
            
            // Enhanced metadata
            $table->json('metadata')->nullable()->after('description'); // Store additional configuration
            $table->string('created_by_type')->nullable()->after('metadata'); // 'admin', 'system', 'import'
            $table->unsignedBigInteger('created_by_id')->nullable()->after('created_by_type');
        });

        // Add indexes for efficient querying
        Schema::table('ht_room_prices', function (Blueprint $table) {
            $table->index(['pricing_category', 'room_category_id'], 'idx_pricing_category');
            $table->index(['recurrence_pattern', 'recurrence_end_date'], 'idx_recurrence');
            $table->index(['is_promotional', 'promotional_expires_at'], 'idx_promotional');
            $table->index(['approval_status', 'approved_at'], 'idx_approval');
            $table->index(['start_date', 'end_date', 'is_active', 'deleted_at'], 'idx_date_range_active');
            $table->index(['override_priority', 'priority'], 'idx_priority_override');
        });

        // Create future pricing calendar cache table for performance
        if (!Schema::hasTable('ht_pricing_calendar_cache')) {
            Schema::create('ht_pricing_calendar_cache', function (Blueprint $table) {
                $table->id();
                $table->foreignId('room_id')->constrained('ht_rooms')->onDelete('cascade');
                $table->date('date');
                $table->decimal('calculated_price', 15, 2);
                $table->decimal('base_price', 15, 2);
                $table->json('applied_rules'); // Store which pricing rules were applied
                $table->json('price_breakdown'); // Detailed breakdown for display
                $table->timestamp('cached_at');
                $table->timestamps();
                
                $table->unique(['room_id', 'date']);
                $table->index(['date', 'room_id']);
                $table->index('cached_at');
            });
        }

        // Create pricing conflicts log table
        if (!Schema::hasTable('ht_pricing_conflicts')) {
            Schema::create('ht_pricing_conflicts', function (Blueprint $table) {
                $table->id();
                $table->foreignId('room_price_id')->constrained('ht_room_prices')->onDelete('cascade');
                $table->foreignId('conflicting_price_id')->constrained('ht_room_prices')->onDelete('cascade');
                $table->date('conflict_start_date');
                $table->date('conflict_end_date');
                $table->enum('conflict_type', ['date_overlap', 'priority_conflict', 'category_conflict']);
                $table->enum('resolution_status', ['unresolved', 'resolved', 'ignored']);
                $table->text('resolution_notes')->nullable();
                $table->unsignedBigInteger('resolved_by')->nullable();
                $table->timestamp('resolved_at')->nullable();
                $table->timestamps();
                
                $table->index(['conflict_type', 'resolution_status']);
                $table->index(['conflict_start_date', 'conflict_end_date']);
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ht_pricing_conflicts');
        Schema::dropIfExists('ht_pricing_calendar_cache');
        
        Schema::table('ht_room_prices', function (Blueprint $table) {
            $table->dropSoftDeletes();
            $table->dropForeign(['room_category_id']);
            $table->dropColumn([
                'pricing_category',
                'room_category_id',
                'recurrence_pattern',
                'recurrence_end_date',
                'recurrence_config',
                'override_priority',
                'is_promotional',
                'promotional_expires_at',
                'approval_status',
                'approved_by',
                'approved_at',
                'approval_notes',
                'metadata',
                'created_by_type',
                'created_by_id'
            ]);
        });
    }
};
