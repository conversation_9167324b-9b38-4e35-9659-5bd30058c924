{"interfaces": {"google.analytics.data.v1beta.BetaAnalyticsData": {"retry_codes": {"no_retry_codes": [], "retry_policy_1_codes": ["UNKNOWN"], "no_retry_1_codes": []}, "retry_params": {"no_retry_params": {"initial_retry_delay_millis": 0, "retry_delay_multiplier": 0.0, "max_retry_delay_millis": 0, "initial_rpc_timeout_millis": 0, "rpc_timeout_multiplier": 1.0, "max_rpc_timeout_millis": 0, "total_timeout_millis": 0}, "retry_policy_1_params": {"initial_retry_delay_millis": 1000, "retry_delay_multiplier": 1.3, "max_retry_delay_millis": 60000, "initial_rpc_timeout_millis": 60000, "rpc_timeout_multiplier": 1.0, "max_rpc_timeout_millis": 60000, "total_timeout_millis": 60000}, "no_retry_1_params": {"initial_retry_delay_millis": 0, "retry_delay_multiplier": 0.0, "max_retry_delay_millis": 0, "initial_rpc_timeout_millis": 60000, "rpc_timeout_multiplier": 1.0, "max_rpc_timeout_millis": 60000, "total_timeout_millis": 60000}}, "methods": {"BatchRunPivotReports": {"timeout_millis": 60000, "retry_codes_name": "no_retry_1_codes", "retry_params_name": "no_retry_1_params"}, "BatchRunReports": {"timeout_millis": 60000, "retry_codes_name": "no_retry_1_codes", "retry_params_name": "no_retry_1_params"}, "CheckCompatibility": {"timeout_millis": 60000, "retry_codes_name": "retry_policy_1_codes", "retry_params_name": "retry_policy_1_params"}, "GetMetadata": {"timeout_millis": 60000, "retry_codes_name": "retry_policy_1_codes", "retry_params_name": "retry_policy_1_params"}, "RunPivotReport": {"timeout_millis": 60000, "retry_codes_name": "no_retry_1_codes", "retry_params_name": "no_retry_1_params"}, "RunRealtimeReport": {"timeout_millis": 60000, "retry_codes_name": "no_retry_1_codes", "retry_params_name": "no_retry_1_params"}, "RunReport": {"timeout_millis": 60000, "retry_codes_name": "no_retry_1_codes", "retry_params_name": "no_retry_1_params"}}}}}