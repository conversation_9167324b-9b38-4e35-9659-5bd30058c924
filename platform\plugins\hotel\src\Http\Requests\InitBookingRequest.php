<?php

namespace Botble\Hotel\Http\Requests;

use Botble\Hotel\Facades\HotelHelper;
use Botble\Hotel\Models\Room;
use Botble\Hotel\Services\RoomCapacityService;
use Botble\Support\Http\Requests\Request;
use Illuminate\Validation\Validator;

class InitBookingRequest extends Request
{
    public function rules(): array
    {
        $dateFormat = HotelHelper::getDateFormat();

        $rules = [
            'room_id' => ['required', 'exists:ht_rooms,id'],
            'start_date' => ['required', 'string', 'date', 'date_format:' . $dateFormat, 'after_or_equal:today'],
            'end_date' => ['required', 'string', 'date', 'date_format:' . $dateFormat, 'after_or_equal:start_date'],
            'adults' => [
                'required',
                'integer',
                'min:' . HotelHelper::getMinimumNumberOfGuests(),
                'max:' . HotelHelper::getMaximumNumberOfGuests(),
            ],
            'children' => ['nullable', 'integer', 'min:0'],
            'rooms' => ['nullable', 'integer', 'min:1'],
        ];

        $roomId = $this->input('room_id');

        if ($roomId) {
            $room = Room::query()
                ->select('number_of_rooms')
                ->find($roomId);

            if ($room) {
                $rules['rooms'][] = 'max:' . $room->number_of_rooms;
            }
        }

        return $rules;
    }

    /**
     * Configure the validator instance with custom validation rules for room occupancy.
     */
    public function withValidator(Validator $validator): void
    {
        $validator->after(function (Validator $validator) {
            $this->validateRoomOccupancy($validator);
        });
    }

    /**
     * Validate room occupancy based on room category
     */
    protected function validateRoomOccupancy(Validator $validator): void
    {
        $roomId = $this->input('room_id');
        $adults = (int) $this->input('adults');
        $children = (int) $this->input('children');

        if (!$roomId) {
            return;
        }

        // Get the room
        $room = Room::with('category')->find($roomId);
        if (!$room || !$room->category) {
            return;
        }

        // Check if this is a Double Bed Room
        if ($room->category->name === 'Double Bed Room') {
            // Check if it's 4 adults (not allowed)
            if ($adults === 4 && $children === 0) {
                $validator->errors()->add(
                    'adults',
                    'For Double Bed Room, the maximum occupancy is 4 people total, but the combination of 4 adults is not allowed.'
                );
                return;
            }

            // Check if total exceeds 4
            if ($adults + $children > 4) {
                $validator->errors()->add(
                    'adults',
                    'For Double Bed Room, the maximum total occupancy is 4 people (adults + children).'
                );
                return;
            }

            // Check if the combination is one of the allowed combinations
            $validCombinations = [
                ['adults' => 3, 'children' => 1],
                ['adults' => 3, 'children' => 0],
                ['adults' => 2, 'children' => 2],
                ['adults' => 2, 'children' => 1],
                ['adults' => 2, 'children' => 0],
                ['adults' => 1, 'children' => 3],
                ['adults' => 1, 'children' => 2],
                ['adults' => 1, 'children' => 1],
                ['adults' => 1, 'children' => 0],
            ];

            $isValid = false;
            foreach ($validCombinations as $combination) {
                if ($combination['adults'] === $adults && $combination['children'] === $children) {
                    $isValid = true;
                    break;
                }
            }

            if (!$isValid) {
                $validator->errors()->add(
                    'adults',
                    'This combination of adults and children is not allowed for Double Bed Room.'
                );
                return;
            }
        } else {
            // For other room types, use the RoomCapacityService
            $roomCapacityService = app(RoomCapacityService::class);
            if (!$roomCapacityService->roomMatchesOccupancyRules($room, $adults, $children)) {
                $validator->errors()->add(
                    'adults',
                    'The selected room does not support this combination of adults and children.'
                );
            }
        }
    }
}
