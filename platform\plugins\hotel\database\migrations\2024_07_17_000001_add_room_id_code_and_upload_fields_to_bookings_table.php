<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('ht_bookings', function (Blueprint $table) {
            if (!Schema::hasColumn('ht_bookings', 'room_id_code')) {
                $table->string('room_id_code')->nullable()->after('booking_number');
            }
            if (!Schema::hasColumn('ht_bookings', 'photo')) {
                $table->string('photo')->nullable()->after('status');
            }
            if (!Schema::hasColumn('ht_bookings', 'proof')) {
                $table->string('proof')->nullable()->after('photo');
            }
            if (!Schema::hasColumn('ht_bookings', 'signature')) {
                $table->string('signature')->nullable()->after('proof');
            }
        });
    }

    public function down(): void
    {
        Schema::table('ht_bookings', function (Blueprint $table) {
            $columnsToRemove = [];
            if (Schema::hasColumn('ht_bookings', 'room_id_code')) {
                $columnsToRemove[] = 'room_id_code';
            }
            if (Schema::hasColumn('ht_bookings', 'photo')) {
                $columnsToRemove[] = 'photo';
            }
            if (Schema::hasColumn('ht_bookings', 'proof')) {
                $columnsToRemove[] = 'proof';
            }
            if (Schema::hasColumn('ht_bookings', 'signature')) {
                $columnsToRemove[] = 'signature';
            }

            if (!empty($columnsToRemove)) {
                $table->dropColumn($columnsToRemove);
            }
        });
    }
};
