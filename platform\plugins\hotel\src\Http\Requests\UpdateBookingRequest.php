<?php

namespace Botble\Hotel\Http\Requests;

use Botble\Hotel\Enums\BookingStatusEnum;
use Botble\Hotel\Models\Room;
use Botble\Hotel\Models\RoomCategory;
use Botble\Hotel\Services\RoomCapacityService;
use Botble\Support\Http\Requests\Request;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Validator;

class UpdateBookingRequest extends Request
{
    public function rules(): array
    {
        $status = $this->input('status', BookingStatusEnum::PENDING); // Default to PENDING if not set
        $bypassRoomValidation = $this->input('bypass_room_validation', false);

        $rules = [
            'status' => ['required', Rule::in(BookingStatusEnum::values())],
            'room_id_code' => [
                'required',
                'string',
                function ($attribute, $value, $fail) {
                    if ($value === 'Select Room ID') {
                        $fail('Please select a valid room ID.');
                    }
                }
            ],
            'photo' => ['nullable', 'string'],
            'proof' => ['nullable', 'string'],
            'signature' => ['nullable', 'string'],
            'arrival_time' => ['nullable', 'string'],
            'requests' => ['nullable', 'string', 'max:500'],
            'number_of_guests' => ['nullable', 'integer', 'min:1', 'max:10'],
            'number_of_children' => ['nullable', 'integer', 'min:0', 'max:10'],
        ];

        // Room ID validation - more flexible for certain statuses
        if ($bypassRoomValidation || in_array($status, ['completed', 'cancelled'])) {
            // For completed/cancelled bookings, room_id_code is optional
            $rules['room_id_code'] = ['nullable', 'string'];
        } else {
            // For other statuses, require a valid room ID
            $rules['room_id_code'] = [
                'required',
                'string',
                function ($attribute, $value, $fail) {
                    if (empty($value) || $value === 'Select Room ID' || $value === '') {
                        $fail('Please select a valid room ID.');
                    }
                }
            ];
        }

        return $rules;
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'status.required' => 'The booking status is required.',
            'status.in' => 'The selected booking status is invalid.',
            'room_id_code.in' => 'The selected room ID is invalid.',
            'number_of_guests.min' => 'The number of adults must be at least 1.',
            'number_of_guests.max' => 'The number of adults cannot exceed 10.',
            'number_of_children.min' => 'The number of children cannot be negative.',
            'number_of_children.max' => 'The number of children cannot exceed 10.',
            'requests.max' => 'The special requests cannot exceed 500 characters.',
        ];
    }

    /**
     * Configure the validator instance with custom validation rules for room occupancy.
     */
    public function withValidator(Validator $validator): void
    {
        $validator->after(function (Validator $validator) {
            $this->validateRoomOccupancy($validator);
        });
    }

    /**
     * Validate room occupancy based on room category
     */
    protected function validateRoomOccupancy(Validator $validator): void
    {
        $roomIdCode = $this->input('room_id_code');
        $adults = (int) $this->input('number_of_guests');
        $children = (int) $this->input('number_of_children');

        if (!$roomIdCode || $roomIdCode === 'Select Room ID') {
            return;
        }

        // Find a room with this room_id_code
        // This is a simplified approach - in a real system, you would query the database
        // to find the room with this code and get its category

        // For demonstration, let's check if the room code starts with specific prefixes
        // that might indicate room types
        $isDoubleBedRoom = false;

        // Check if this is a Double Bed Room (e.g., if codes for Double Bed Rooms start with "DBR-")
        if (strpos($roomIdCode, 'DBR-') === 0) {
            $isDoubleBedRoom = true;
        } else {
            // If we can't determine from the code, try to find the room in the database
            // and check its category
            try {
                // Find the room in the database based on room_id_code
                $room = Room::where('room_id_code', $roomIdCode)->first();

                if ($room && $room->category) {
                    $isDoubleBedRoom = $room->category->name === 'Double Bed Room';
                }
            } catch (\Exception $e) {
                // If there's an error, log it but continue
                \Log::error('Error finding room by room_id_code: ' . $e->getMessage());
            }
        }

        // If this is a Double Bed Room, apply the specific validation rules
        if ($isDoubleBedRoom) {
            // Check if it's 4 adults (not allowed)
            if ($adults === 4 && $children === 0) {
                $validator->errors()->add(
                    'number_of_guests',
                    'For Double Bed Room, the maximum occupancy is 4 people total, but the combination of 4 adults is not allowed.'
                );
                return;
            }

            // Check if total exceeds 4
            if ($adults + $children > 4) {
                $validator->errors()->add(
                    'number_of_guests',
                    'For Double Bed Room, the maximum total occupancy is 4 people (adults + children).'
                );
                return;
            }

            // Check if the combination is one of the allowed combinations
            $validCombinations = [
                ['adults' => 3, 'children' => 1],
                ['adults' => 3, 'children' => 0],
                ['adults' => 2, 'children' => 2],
                ['adults' => 2, 'children' => 1],
                ['adults' => 2, 'children' => 0],
                ['adults' => 1, 'children' => 3],
                ['adults' => 1, 'children' => 2],
                ['adults' => 1, 'children' => 1],
                ['adults' => 1, 'children' => 0],
            ];

            $isValid = false;
            foreach ($validCombinations as $combination) {
                if ($combination['adults'] === $adults && $combination['children'] === $children) {
                    $isValid = true;
                    break;
                }
            }

            if (!$isValid) {
                $validator->errors()->add(
                    'number_of_guests',
                    'This combination of adults and children is not allowed for Double Bed Room.'
                );
            }
        }
    }
}

