(()=>{"use strict";var t={6262:(t,e)=>{e.A=(t,e)=>{const a=t.__vccOpts||t;for(const[t,n]of e)a[t]=n;return a}}},e={};const a=Vue;const n={props:{getRoomAvailabilityUrl:{type:String,default:""}},data:function(){return{form:{id:"",value:"",value_type:"fixed",start_date:"",end_date:"",enable_person:0,min_guests:0,max_guests:0,active:0,number_of_rooms:1},formDefault:{id:"",value:"",start_date:"",end_date:"",enable_person:0,min_guests:0,max_guests:0,active:0,number_of_rooms:1},onSubmit:!1,calendar:null}},methods:{show:function(t){$("#modal-calendar").modal("show"),this.onSubmit=!1,void 0!==t&&(this.form=Object.assign({},t),t.start_date&&$(".modal-title").text(moment(t.start_date).format("MM/DD/YYYY")))},hide:function(){$("#modal-calendar").modal("hide"),this.form=Object.assign({},this.formDefault)},saveForm:function(){var t=this,e=this;this.onSubmit||this.validateForm()&&($("#modal-calendar").find(".btn-primary").addClass("button-loading"),this.onSubmit=!0,$.ajax({url:this.getRoomAvailabilityUrl,data:this.form,dataType:"json",method:"POST",success:function(a){a.error?Botble.showError(a.message):(t.calendar&&t.calendar.refetchEvents(),e.hide(),Botble.showSuccess(a.message)),e.onSubmit=!1,$("#modal-calendar").find(".btn-primary").removeClass("button-loading")},error:function(){e.onSubmit=!1,$("#modal-calendar").find(".btn-primary").removeClass("button-loading")}}))},validateForm:function(){return!!this.form.start_date&&this.form.end_date}},created:function(){var t=this;this.$nextTick((function(){$(t.$el).on("hide.bs.modal",(function(){this.form=Object.assign({},this.formDefault)}))}))},mounted:function(){var t,e=this;t=document.getElementById("dates-calendar"),this.calendar&&this.calendar.destroy(),this.calendar=new FullCalendar.Calendar(t,{headerToolbar:{left:"title"},navLinks:!0,editable:!1,dayMaxEvents:!1,events:{url:this.getRoomAvailabilityUrl},loading:function(e){e?$(t).addClass("loading"):$(t).removeClass("loading")},select:function(t){e.show({start_date:moment(t.start).format("YYYY-MM-DD"),end_date:moment(t.end).format("YYYY-MM-DD")})},eventClick:function(t){var a=Object.assign({},t.event.extendedProps);a.start_date=moment(t.event.start).format("YYYY-MM-DD"),a.end_date=moment(t.event.start).format("YYYY-MM-DD"),e.show(a)},eventRender:function(t){$(t.el).find(".fc-title").html(t.event.title)}}),this.calendar.render()}};var o=function a(n){var o=e[n];if(void 0!==o)return o.exports;var r=e[n]={exports:{}};return t[n](r,r.exports,a),r.exports}(6262);const r=(0,o.A)(n,[["render",function(t,e,n,o,r,s){return(0,a.renderSlot)(t.$slots,"default",(0,a.normalizeProps)((0,a.guardReactiveProps)({form:r.form,saveForm:s.saveForm})))}]]);"undefined"!=typeof vueApp&&vueApp.booting((function(t){t.component("calendar-modal-component",r)}))})();