// Update any client-side filtering for 2BHK rooms
function filterRoomsByOccupancy(rooms, adults, children) {
    return rooms.filter(function(room) {
        // Special case for 2BHK rooms
        if (room.category && (room.category.toLowerCase().includes('2bhk') || room.category.toLowerCase().includes('family'))) {
            const totalOccupants = parseInt(adults) + parseInt(children);
            // Update to allow 7 people
            return totalOccupants <= 7 && adults >= 1;
        }
        
        // Regular room filtering logic
        return (adults <= room.max_adults && children <= room.max_children);
    });
}

// Debug the room filtering
console.log('Room search parameters:', {
    adults: $('#adults').val(),
    children: $('#children').val(),
    total: parseInt($('#adults').val()) + parseInt($('#children').val())
});