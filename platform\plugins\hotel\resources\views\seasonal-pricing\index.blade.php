@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
<meta name="csrf-token" content="{{ csrf_token() }}">
<style>
/* Modern Seasonal Pricing Styles */
.seasonal-pricing-container {
    background: #ffffff;
    min-height: 100vh;
    padding: 20px 0;
}

.seasonal-header {
    background: #ffffff;
    border-radius: 20px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #e2e8f0;
}

.seasonal-header h1 {
    color: #2d3748;
    font-weight: 700;
    font-size: 2.5rem;
    margin-bottom: 10px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.seasonal-header p {
    color: #718096;
    font-size: 1.1rem;
    margin-bottom: 0;
}

.action-buttons {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.btn-modern {
    padding: 12px 24px;
    border-radius: 12px;
    font-weight: 600;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    border: none;
    position: relative;
    overflow: hidden;
}

.btn-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn-modern:hover::before {
    left: 100%;
}

.btn-primary-modern {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.btn-primary-modern:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
    color: white;
}

.btn-success-modern {
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
    color: white;
    box-shadow: 0 8px 25px rgba(72, 187, 120, 0.3);
}

.btn-success-modern:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 35px rgba(72, 187, 120, 0.4);
    color: white;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 25px;
    margin-bottom: 40px;
}

.stat-card {
    background: #ffffff;
    border-radius: 20px;
    padding: 30px;
    text-align: center;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #e2e8f0;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
}

.stat-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    font-size: 2rem;
    color: white;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
}

.stat-number {
    font-size: 3rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 10px;
    line-height: 1;
}

.stat-label {
    font-size: 1.1rem;
    font-weight: 600;
    color: #4a5568;
    margin-bottom: 10px;
}

.stat-detail {
    font-size: 0.9rem;
    color: #718096;
    padding: 8px 16px;
    background: rgba(102, 126, 234, 0.1);
    border-radius: 20px;
    display: inline-block;
}

.pricing-table-container {
    background: #ffffff;
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #e2e8f0;
}

.table-modern {
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    background: white;
}

.table-modern thead {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.table-modern thead th {
    border: none;
    padding: 20px 15px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.85rem;
}

.table-modern tbody tr {
    transition: all 0.3s ease;
}

.table-modern tbody tr:hover {
    background: rgba(102, 126, 234, 0.05);
    transform: scale(1.01);
}

.table-modern tbody td {
    padding: 20px 15px;
    border: none;
    border-bottom: 1px solid #e2e8f0;
    vertical-align: middle;
}

.badge-modern {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.badge-success-modern {
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
    color: white;
}

.badge-warning-modern {
    background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
    color: white;
}

.badge-info-modern {
    background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
    color: white;
}

.action-buttons-table {
    display: flex;
    gap: 8px;
}

.btn-action {
    width: 35px;
    height: 35px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
    transition: all 0.3s ease;
    font-size: 0.85rem;
}

.btn-edit {
    background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
    color: white;
}

.btn-edit:hover {
    transform: scale(1.1);
    box-shadow: 0 5px 15px rgba(66, 153, 225, 0.4);
}

.btn-delete {
    background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
    color: white;
}

.btn-delete:hover {
    transform: scale(1.1);
    box-shadow: 0 5px 15px rgba(245, 101, 101, 0.4);
}

.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #718096;
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 20px;
    color: #cbd5e0;
}

.empty-state h3 {
    font-size: 1.5rem;
    margin-bottom: 10px;
    color: #4a5568;
}

.empty-state p {
    font-size: 1rem;
    margin-bottom: 30px;
}

@media (max-width: 768px) {
    .seasonal-header {
        padding: 20px;
        text-align: center;
    }

    .seasonal-header h1 {
        font-size: 2rem;
    }

    .action-buttons {
        justify-content: center;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .pricing-table-container {
        padding: 20px;
    }

    .table-responsive {
        border-radius: 15px;
    }
}
</style>

<div class="seasonal-pricing-container">
    <div class="container-fluid">
        <!-- Header Section -->
        <div class="seasonal-header">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1><i class="fas fa-calendar-alt me-3"></i>Seasonal Pricing Management</h1>
                    <p>Manage dynamic pricing strategies for different seasons and events</p>
                </div>
                <div class="col-lg-4 text-lg-end">
                    <div class="action-buttons">
                        <button type="button" class="btn btn-modern btn-primary-modern" data-toggle="modal" data-target="#bulkSeasonalModal">
                            <i class="fas fa-plus me-2"></i>Create Pricing
                        </button>
                        <button type="button" class="btn btn-modern btn-success-modern" data-toggle="modal" data-target="#quickSetupModal">
                            <i class="fas fa-magic me-2"></i>Quick Setup
                        </button>
                        <button type="button" class="btn btn-outline-info btn-sm" onclick="debugSeasonalPricing()">
                            <i class="fas fa-bug me-1"></i>Debug
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="stats-grid">
            @foreach($stats as $seasonType => $stat)
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-calendar-check"></i>
                </div>
                <div class="stat-number">{{ $stat['total_count'] }}</div>
                <div class="stat-label">{{ $stat['name'] }}</div>
                <div class="stat-detail">{{ $stat['future_count'] }} future rules</div>
            </div>
            @endforeach
        </div>

        <!-- Pricing Table -->
        <div class="pricing-table-container">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h3 class="mb-0" style="color: #2d3748; font-weight: 600;">
                    <i class="fas fa-table me-2"></i>Active Pricing Rules
                </h3>
                <div class="d-flex gap-2">
                    <button class="btn btn-sm btn-outline-primary" onclick="refreshTable()">
                        <i class="fas fa-sync-alt me-1"></i>Refresh
                    </button>
                    <button class="btn btn-sm btn-outline-success" onclick="exportPricing()">
                        <i class="fas fa-download me-1"></i>Export
                    </button>
                </div>
            </div>

            <div class="table-responsive">
                <table class="table table-modern">
                    <thead>
                        <tr>
                            <th>Season Details</th>
                            <th>Room/Category</th>
                            <th>Date Range</th>
                            <th>Price Adjustment</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($futurePricing as $pricing)
                        <tr>
                            <td>
                                <div class="d-flex flex-column">
                                    <strong style="color: #2d3748; font-size: 1rem;">{{ $pricing->name }}</strong>
                                    @if($pricing->season_type)
                                        <span class="badge badge-modern badge-info-modern mt-1 align-self-start">
                                            {{ $pricing->getSeasonDisplayName() }}
                                        </span>
                                    @endif
                                    @if($pricing->price_reason)
                                        <small class="text-muted mt-1">{{ $pricing->price_reason }}</small>
                                    @endif
                                </div>
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    @if($pricing->room)
                                        <i class="fas fa-bed me-2" style="color: #667eea;"></i>
                                        <span>{{ $pricing->room->name }}</span>
                                    @elseif($pricing->roomCategory)
                                        <i class="fas fa-layer-group me-2" style="color: #667eea;"></i>
                                        <span>{{ $pricing->roomCategory->name }}</span>
                                    @else
                                        <i class="fas fa-building me-2" style="color: #667eea;"></i>
                                        <span>All Rooms</span>
                                    @endif
                                </div>
                            </td>
                            <td>
                                <div class="d-flex flex-column">
                                    <span style="font-weight: 600; color: #2d3748;">
                                        {{ $pricing->start_date->format('M d, Y') }}
                                    </span>
                                    <span style="color: #718096;">to</span>
                                    <span style="font-weight: 600; color: #2d3748;">
                                        {{ $pricing->end_date->format('M d, Y') }}
                                    </span>
                                    <small class="text-muted mt-1">
                                        <i class="fas fa-clock me-1"></i>
                                        {{ $pricing->start_date->diffInDays($pricing->end_date) }} days
                                    </small>
                                </div>
                            </td>
                            <td>
                                <div class="d-flex flex-column">
                                    @if($pricing->pricing_type === 'fixed')
                                        <span style="font-weight: 700; color: #2d3748; font-size: 1.1rem;">
                                            ₹{{ number_format($pricing->price, 2) }}
                                        </span>
                                        <small class="text-muted">Fixed Price</small>
                                    @elseif($pricing->pricing_type === 'percentage')
                                        <span style="font-weight: 700; color: #48bb78; font-size: 1.1rem;">
                                            +{{ $pricing->adjustment_value }}%
                                        </span>
                                        <small class="text-muted">Percentage Increase</small>
                                    @else
                                        <span style="font-weight: 700; color: #48bb78; font-size: 1.1rem;">
                                            +₹{{ number_format($pricing->adjustment_value, 2) }}
                                        </span>
                                        <small class="text-muted">Fixed Increase</small>
                                    @endif
                                </div>
                            </td>
                            <td>
                                <div class="d-flex flex-column gap-1">
                                    @if($pricing->is_future_pricing || $pricing->start_date->isFuture())
                                        <span class="badge badge-modern badge-warning-modern">Future</span>
                                    @else
                                        <span class="badge badge-modern badge-success-modern">Active</span>
                                    @endif
                                    @if($pricing->is_active)
                                        <span class="badge badge-modern badge-success-modern">Enabled</span>
                                    @else
                                        <span class="badge badge-modern" style="background: #e2e8f0; color: #4a5568;">Disabled</span>
                                    @endif
                                </div>
                            </td>
                            <td>
                                <div class="action-buttons-table">
                                    <button type="button" class="btn-action btn-edit"
                                            onclick="editPricing({{ $pricing->id }})"
                                            title="Edit Pricing">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button type="button" class="btn-action btn-delete"
                                            onclick="deletePricing({{ $pricing->id }})"
                                            title="Delete Pricing">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="6">
                                <div class="empty-state">
                                    <i class="fas fa-calendar-times"></i>
                                    <h3>No Pricing Rules Found</h3>
                                    <p>Start by creating your first seasonal pricing rule using the buttons above.</p>
                                    <button type="button" class="btn btn-modern btn-primary-modern" data-toggle="modal" data-target="#quickSetupModal">
                                        <i class="fas fa-magic me-2"></i>Quick Setup
                                    </button>
                                </div>
                            </td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            @if($futurePricing->hasPages())
            <div class="d-flex justify-content-center mt-4">
                <div class="pagination-wrapper">
                    {{ $futurePricing->links() }}
                </div>
            </div>
            @endif
        </div>
    </div>
</div>

<!-- Bulk Seasonal Pricing Modal -->
<div class="modal fade" id="bulkSeasonalModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content" style="border-radius: 20px; border: none; box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);">
            <div class="modal-header" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 20px 20px 0 0; border: none;">
                <h5 class="modal-title" style="font-weight: 600; font-size: 1.25rem;">
                    <i class="fas fa-plus-circle me-2"></i>Create Seasonal Pricing
                </h5>
                <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close" style="color: white; opacity: 1;">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="bulkSeasonalForm">
                @csrf
                <div class="modal-body" style="padding: 30px;">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="season_name" class="form-label" style="font-weight: 600; color: #2d3748;">Season Name *</label>
                                <input type="text" class="form-control" id="season_name" name="season_name" required
                                       style="border-radius: 10px; border: 2px solid #e2e8f0; padding: 12px 15px;"
                                       placeholder="e.g., Christmas Season 2025">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="season_type" class="form-label" style="font-weight: 600; color: #2d3748;">Season Type *</label>
                                <select class="form-control" id="season_type" name="season_type" required
                                        style="border-radius: 10px; border: 2px solid #e2e8f0; padding: 12px 15px;">
                                    <option value="">Select Season Type</option>
                                    @foreach($seasonTypes as $key => $name)
                                        <option value="{{ $key }}">{{ $name }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="start_date" class="form-label" style="font-weight: 600; color: #2d3748;">Start Date *</label>
                                <input type="date" class="form-control" id="start_date" name="start_date" required
                                       style="border-radius: 10px; border: 2px solid #e2e8f0; padding: 12px 15px;">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="end_date" class="form-label" style="font-weight: 600; color: #2d3748;">End Date *</label>
                                <input type="date" class="form-control" id="end_date" name="end_date" required
                                       style="border-radius: 10px; border: 2px solid #e2e8f0; padding: 12px 15px;">
                            </div>
                        </div>
                    </div>

                    <div class="form-group mb-3">
                        <label for="pricing_scope" class="form-label" style="font-weight: 600; color: #2d3748;">Apply To *</label>
                        <select class="form-control" id="pricing_scope" name="pricing_scope" required
                                style="border-radius: 10px; border: 2px solid #e2e8f0; padding: 12px 15px;">
                            <option value="all_rooms">All Rooms</option>
                            <option value="room_category">Specific Room Category</option>
                            <option value="specific_room">Specific Room</option>
                        </select>
                    </div>

                    <div class="form-group mb-3" id="room_category_group" style="display: none;">
                        <label for="room_category_id" class="form-label" style="font-weight: 600; color: #2d3748;">Room Category</label>
                        <select class="form-control" id="room_category_id" name="room_category_id"
                                style="border-radius: 10px; border: 2px solid #e2e8f0; padding: 12px 15px;">
                            <option value="">Select Room Category</option>
                            @foreach($roomCategories as $category)
                                <option value="{{ $category->id }}">{{ $category->name }}</option>
                            @endforeach
                        </select>
                    </div>

                    <div class="form-group mb-3" id="room_group" style="display: none;">
                        <label for="room_id" class="form-label" style="font-weight: 600; color: #2d3748;">Room</label>
                        <select class="form-control" id="room_id" name="room_id"
                                style="border-radius: 10px; border: 2px solid #e2e8f0; padding: 12px 15px;">
                            <option value="">Select Room</option>
                            @foreach($rooms as $room)
                                <option value="{{ $room->id }}">{{ $room->name }} (₹{{ number_format($room->price, 2) }})</option>
                            @endforeach
                        </select>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="adjustment_type" class="form-label" style="font-weight: 600; color: #2d3748;">Pricing Type *</label>
                                <select class="form-control" id="adjustment_type" name="adjustment_type" required
                                        style="border-radius: 10px; border: 2px solid #e2e8f0; padding: 12px 15px;">
                                    <option value="percentage">Percentage Increase</option>
                                    <option value="fixed_amount">Fixed Amount Increase</option>
                                    <option value="fixed_price">Fixed Price</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="adjustment_value" class="form-label" style="font-weight: 600; color: #2d3748;">Value *</label>
                                <input type="number" class="form-control" id="adjustment_value" name="adjustment_value" step="0.01" required
                                       style="border-radius: 10px; border: 2px solid #e2e8f0; padding: 12px 15px;"
                                       placeholder="e.g., 20">
                                <small class="form-text text-muted" id="adjustment_help">Enter percentage (e.g., 20 for 20%)</small>
                            </div>
                        </div>
                    </div>

                    <div class="form-group mb-3">
                        <label for="price_reason" class="form-label" style="font-weight: 600; color: #2d3748;">Price Reason</label>
                        <input type="text" class="form-control" id="price_reason" name="price_reason"
                               style="border-radius: 10px; border: 2px solid #e2e8f0; padding: 12px 15px;"
                               placeholder="e.g., Increased demand during function season">
                    </div>

                    <div class="form-group mb-3">
                        <label for="customer_message" class="form-label" style="font-weight: 600; color: #2d3748;">Customer Message</label>
                        <textarea class="form-control" id="customer_message" name="customer_message" rows="3"
                                  style="border-radius: 10px; border: 2px solid #e2e8f0; padding: 12px 15px;"
                                  placeholder="Message to display to customers explaining the price increase"></textarea>
                    </div>

                    <div class="form-group mb-3">
                        <label for="priority" class="form-label" style="font-weight: 600; color: #2d3748;">Priority (1-100)</label>
                        <input type="number" class="form-control" id="priority" name="priority" min="1" max="100" value="10"
                               style="border-radius: 10px; border: 2px solid #e2e8f0; padding: 12px 15px;">
                        <small class="form-text text-muted">Higher numbers have higher priority</small>
                    </div>


                </div>
                <div class="modal-footer" style="border: none; padding: 20px 30px; display: flex; justify-content: space-between; align-items: center;">
                    <!-- Left side - Cancel button -->
                    <button type="button" class="btn btn-danger" data-bs-dismiss="modal" style="background: #dc3545; color: white; border: none; padding: 10px 20px; border-radius: 8px;">
                        <i class="fas fa-times me-1"></i>Cancel
                    </button>

                    <!-- Right side - Action button -->
                    <button type="submit" class="btn btn-modern btn-primary-modern" id="createSeasonalBtn">
                        <i class="fas fa-save me-1"></i>Create Seasonal Pricing
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Quick Setup Modal -->
<div class="modal fade" id="quickSetupModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content" style="border-radius: 20px; border: none; box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);">
            <div class="modal-header" style="background: linear-gradient(135deg, #48bb78 0%, #38a169 100%); color: white; border-radius: 20px 20px 0 0; border: none;">
                <h5 class="modal-title" style="font-weight: 600; font-size: 1.25rem;">
                    <i class="fas fa-magic me-2"></i>Quick Seasonal Setup
                </h5>
                <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close" style="color: white; opacity: 1;">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="quickSetupForm">
                @csrf
                <div class="modal-body" style="padding: 30px;">
                    <div class="form-group mb-4">
                        <label for="pattern" class="form-label" style="font-weight: 600; color: #2d3748; font-size: 1.1rem;">Seasonal Pattern *</label>
                        <select class="form-control" id="pattern" name="pattern" required
                                style="border-radius: 10px; border: 2px solid #e2e8f0; padding: 15px; font-size: 1rem;">
                            <option value="">Select Pattern</option>
                            <option value="function_season">🎉 Function Season (Dec 15 - Jan 15, +20%)</option>
                            <option value="peak_season">⭐ Peak Season (Nov 1 - Feb 28, +30%)</option>
                            <option value="festival_season">🎊 Festival Season (Oct 1 - Nov 30, +25%)</option>
                            <option value="wedding_season">💒 Wedding Season (Nov 15 - Mar 15, +35%)</option>
                        </select>
                    </div>

                    <div class="form-group mb-4">
                        <label for="year" class="form-label" style="font-weight: 600; color: #2d3748; font-size: 1.1rem;">Year *</label>
                        <select class="form-control" id="year" name="year" required
                                style="border-radius: 10px; border: 2px solid #e2e8f0; padding: 15px; font-size: 1rem;">
                            @for($i = date('Y'); $i <= date('Y') + 2; $i++)
                                <option value="{{ $i }}" {{ $i == date('Y') ? 'selected' : '' }}>{{ $i }}</option>
                            @endfor
                        </select>
                    </div>

                    <div class="alert" style="background: linear-gradient(135deg, #e6fffa 0%, #b2f5ea 100%); border: 1px solid #81e6d9; border-radius: 15px; padding: 20px;">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-info-circle me-3" style="color: #38a169; font-size: 1.5rem;"></i>
                            <div>
                                <h6 style="color: #2d3748; font-weight: 600; margin-bottom: 5px;">Quick Setup Benefits</h6>
                                <p style="color: #4a5568; margin-bottom: 0;">This will automatically create seasonal pricing for all rooms based on predefined patterns, saving you time and ensuring consistent pricing strategies.</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer" style="border: none; padding: 20px 30px; display: flex; justify-content: space-between; align-items: center;">
                    <!-- Left side - Cancel button -->
                    <button type="button" class="btn btn-danger" data-bs-dismiss="modal" style="background: #dc3545; color: white; border: none; padding: 10px 20px; border-radius: 8px;">
                        <i class="fas fa-times me-1"></i>Cancel
                    </button>

                    <!-- Right side - Action button -->
                    <button type="submit" class="btn btn-modern btn-success-modern" id="quickSetupBtn">
                        <i class="fas fa-rocket me-1"></i>Setup Seasonal Pricing
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Ensure jQuery is loaded
if (typeof jQuery === 'undefined') {
    console.error('jQuery is not loaded! Seasonal pricing functionality may not work.');
}

// Debug function to check if everything is loaded
function debugSeasonalPricing() {
    console.log('=== Seasonal Pricing Debug ===');
    console.log('jQuery loaded:', typeof jQuery !== 'undefined');
    console.log('Bootstrap loaded:', typeof $.fn.modal !== 'undefined');
    console.log('CSRF token:', $('meta[name="csrf-token"]').attr('content'));
    console.log('Modal elements:', $('#bulkSeasonalModal').length, $('#quickSetupModal').length);
    console.log('Button elements:', $('[data-toggle="modal"]').length);
}

// Additional JavaScript for enhanced functionality
function refreshTable() {
    location.reload();
}

function exportPricing() {
    window.open('/admin/hotel/seasonal-pricing/export', '_blank');
}

// Enhanced notification functions
function showSuccess(message) {
    if (typeof toastr !== 'undefined') {
        toastr.success(message, 'Success!', {
            timeOut: 5000,
            progressBar: true,
            positionClass: 'toast-top-right'
        });
    } else {
        alert('Success: ' + message);
    }
}

function showError(message) {
    if (typeof toastr !== 'undefined') {
        toastr.error(message, 'Error!', {
            timeOut: 7000,
            progressBar: true,
            positionClass: 'toast-top-right'
        });
    } else {
        alert('Error: ' + message);
    }
}

function showValidationErrors(errors) {
    let errorMessage = 'Please fix the following errors:\n';
    for (let field in errors) {
        errorMessage += '- ' + errors[field].join('\n- ') + '\n';
    }
    showError(errorMessage);
}

// Enhanced form handling
$(document).ready(function() {
    // Debug on page load
    debugSeasonalPricing();

    // Manual modal initialization for better compatibility
    if (typeof $.fn.modal !== 'undefined') {
        $('#bulkSeasonalModal, #quickSetupModal').modal({
            show: false,
            backdrop: 'static',
            keyboard: false
        });
    }

    // Manual button click handlers
    $('[data-toggle="modal"][data-target="#bulkSeasonalModal"]').click(function(e) {
        e.preventDefault();
        console.log('Create Pricing button clicked');
        $('#bulkSeasonalModal').modal('show');
    });

    $('[data-toggle="modal"][data-target="#quickSetupModal"]').click(function(e) {
        e.preventDefault();
        console.log('Quick Setup button clicked');
        $('#quickSetupModal').modal('show');
    });

    // Initialize date inputs with minimum date as today
    const today = new Date().toISOString().split('T')[0];
    $('#start_date, #end_date').attr('min', today);

    // Handle pricing scope change
    $('#pricing_scope').change(function() {
        const scope = $(this).val();
        $('#room_category_group, #room_group').slideUp(300);

        setTimeout(() => {
            if (scope === 'room_category') {
                $('#room_category_group').slideDown(300);
                $('#room_category_id').attr('required', true);
                $('#room_id').removeAttr('required');
            } else if (scope === 'specific_room') {
                $('#room_group').slideDown(300);
                $('#room_id').attr('required', true);
                $('#room_category_id').removeAttr('required');
            } else {
                $('#room_category_id, #room_id').removeAttr('required');
            }
        }, 300);
    });

    // Handle adjustment type change
    $('#adjustment_type').change(function() {
        const type = $(this).val();
        const helpText = $('#adjustment_help');
        const valueInput = $('#adjustment_value');

        helpText.fadeOut(200, function() {
            switch(type) {
                case 'percentage':
                    helpText.text('Enter percentage (e.g., 20 for 20% increase)');
                    valueInput.attr('placeholder', '20');
                    break;
                case 'fixed_amount':
                    helpText.text('Enter amount to add (e.g., 500 for ₹500 increase)');
                    valueInput.attr('placeholder', '500');
                    break;
                case 'fixed_price':
                    helpText.text('Enter the exact price (e.g., 3000 for ₹3000)');
                    valueInput.attr('placeholder', '3000');
                    break;
            }
            helpText.fadeIn(200);
        });
    });

    // Removed duplicate form handler - using the one in the document ready section below

    // Handle quick setup form submission
    $('#quickSetupForm').submit(function(e) {
        e.preventDefault();
        console.log('Quick setup form submitted');

        const formData = $(this).serialize();
        const submitBtn = $(this).find('button[type="submit"]');
        const originalText = submitBtn.html();

        console.log('Quick setup form data:', formData);

        submitBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-1"></i>Setting up...');

        $.ajax({
            url: '/admin/hotel/seasonal-pricing/quick-setup',
            method: 'POST',
            data: formData,
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            beforeSend: function() {
                console.log('Quick setup AJAX request starting...');
            },
            success: function(response) {
                console.log('Quick setup AJAX success:', response);
                if (response.error) {
                    showError(response.message);
                } else {
                    showSuccess(response.message);
                    $('#quickSetupModal').modal('hide');
                    setTimeout(() => location.reload(), 1000);
                }
            },
            error: function(xhr, status, error) {
                console.log('Quick setup AJAX error:', xhr, status, error);
                const response = xhr.responseJSON;
                if (response && response.message) {
                    showError(response.message);
                } else {
                    showError('An error occurred while setting up seasonal pricing: ' + error);
                }
            },
            complete: function() {
                console.log('Quick setup AJAX request completed');
                submitBtn.prop('disabled', false).html(originalText);
            }
        });
    });
});

// Preview functionality removed to simplify the form and eliminate conflicts

// All preview-related functions removed to simplify the form

// Form submission handlers
$(document).ready(function() {
    console.log('=== Document Ready - Initializing Form Handlers ===');

    // Handle Create Seasonal Pricing form submission
    $('#bulkSeasonalForm').on('submit', function(e) {
        e.preventDefault();
        console.log('=== Create Seasonal Pricing Form Submitted ===');

        const submitBtn = $('#createSeasonalBtn');
        const originalText = submitBtn.html();

        console.log('Submit button found:', submitBtn.length);
        console.log('Original button text:', originalText);

        // Show loading state
        submitBtn.html('<i class="fas fa-spinner fa-spin me-1"></i>Creating...').prop('disabled', true);

        // Get form data and ensure all expected fields are present
        let formData = $(this).serialize();

        // Clean form data based on pricing scope to avoid validation errors
        const pricingScope = $('#pricing_scope').val();
        console.log('Original form data:', formData);
        console.log('Pricing scope:', pricingScope);

        // Remove unnecessary fields that could cause validation errors
        if (pricingScope === 'specific_room') {
            // Remove room_category_id from form data
            formData = formData.replace(/&?room_category_id=[^&]*/g, '');
        } else if (pricingScope === 'room_category') {
            // Remove room_id from form data
            formData = formData.replace(/&?room_id=[^&]*/g, '');
        } else if (pricingScope === 'all_rooms') {
            // Remove both room_id and room_category_id
            formData = formData.replace(/&?room_id=[^&]*/g, '');
            formData = formData.replace(/&?room_category_id=[^&]*/g, '');
        }

        console.log('Cleaned form data:', formData);

        console.log('Serialized form data:', formData);
        console.log('CSRF token:', $('meta[name="csrf-token"]').attr('content'));

        // Log individual form values for debugging
        console.log('Individual form values:');
        console.log('- season_name:', $('#season_name').val());
        console.log('- season_type:', $('#season_type').val());
        console.log('- start_date:', $('#start_date').val());
        console.log('- end_date:', $('#end_date').val());
        console.log('- pricing_scope:', $('#pricing_scope').val());
        console.log('- room_id:', $('#room_id').val());
        console.log('- room_category_id:', $('#room_category_id').val());
        console.log('- adjustment_type:', $('#adjustment_type').val());
        console.log('- adjustment_value:', $('#adjustment_value').val());
        console.log('- price_reason:', $('#price_reason').val());
        console.log('- customer_message:', $('#customer_message').val());
        console.log('- priority:', $('#priority').val());

        // Parse and log the final form data for verification
        console.log('=== FINAL FORM DATA BREAKDOWN ===');
        const formParams = new URLSearchParams(formData);
        for (const [key, value] of formParams) {
            console.log(`${key}: "${value}"`);
        }

        // Additional validation - check for required fields
        const requiredFieldsCheck = {
            'season_name': $('#season_name').val(),
            'season_type': $('#season_type').val(),
            'start_date': $('#start_date').val(),
            'end_date': $('#end_date').val(),
            'pricing_scope': $('#pricing_scope').val(),
            'adjustment_type': $('#adjustment_type').val(),
            'adjustment_value': $('#adjustment_value').val()
        };

        console.log('=== REQUIRED FIELDS CHECK ===');
        for (const [field, value] of Object.entries(requiredFieldsCheck)) {
            console.log(`${field}: "${value}" (${value ? 'OK' : 'MISSING'})`);
            if (!value) {
                console.error(`❌ Missing required field: ${field}`);
            }
        }

        // Validate form before submission - using correct field names from HTML
        const form = document.getElementById('bulkSeasonalForm');
        const formDataObj = new FormData(form);
        const requiredFields = ['season_name', 'season_type', 'start_date', 'end_date', 'adjustment_type', 'adjustment_value'];
        const missingFields = [];

        requiredFields.forEach(field => {
            if (!formDataObj.get(field)) {
                missingFields.push(field);
            }
        });

        if (missingFields.length > 0) {
            console.error('Missing required fields:', missingFields);
            showError('Please fill in all required fields: ' + missingFields.join(', '));
            submitBtn.html(originalText).prop('disabled', false);
            return;
        }

        // Additional validation for dates
        const startDate = new Date($('#start_date').val());
        const endDate = new Date($('#end_date').val());
        const today = new Date();
        today.setHours(0, 0, 0, 0); // Reset time to start of day for comparison

        console.log('Date validation:');
        console.log('- Start date:', startDate);
        console.log('- End date:', endDate);
        console.log('- Today:', today);
        console.log('- Start date > today:', startDate > today);
        console.log('- End date > start date:', endDate > startDate);

        if (startDate <= today) {
            console.error('Start date must be in the future');
            showError('Start date must be in the future');
            submitBtn.html(originalText).prop('disabled', false);
            return;
        }

        if (endDate <= startDate) {
            console.error('End date must be after start date');
            showError('End date must be after start date');
            submitBtn.html(originalText).prop('disabled', false);
            return;
        }

        $.ajax({
            url: '/admin/hotel/seasonal-pricing/create-bulk',
            method: 'POST',
            data: formData,
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            beforeSend: function(xhr, settings) {
                console.log('=== AJAX REQUEST DETAILS ===');
                console.log('URL:', settings.url);
                console.log('Method:', settings.type);
                console.log('Data being sent:', settings.data);
                console.log('Headers:', xhr.getAllResponseHeaders ? 'Available' : 'Not available');
                console.log('CSRF Token:', $('meta[name="csrf-token"]').attr('content'));
                console.log('Create AJAX request starting...');
            },
            success: function(response) {
                console.log('=== AJAX SUCCESS ===');
                console.log('Full response:', response);
                console.log('Response type:', typeof response);
                console.log('Response error flag:', response.error);
                console.log('Response message:', response.message);

                if (response.error) {
                    console.error('Server returned error:', response.message);
                    showError(response.message);
                } else {
                    console.log('Success! Showing success message...');
                    showSuccess(response.message || 'Seasonal pricing created successfully!');
                    $('#bulkSeasonalModal').modal('hide');
                    // Refresh the page after a short delay
                    setTimeout(() => {
                        console.log('Refreshing page...');
                        location.reload();
                    }, 1500);
                }
            },
            error: function(xhr, status, error) {
                console.error('=== AJAX ERROR ===');
                console.error('Status:', status);
                console.error('Error:', error);
                console.error('HTTP Status Code:', xhr.status);
                console.error('Response Text:', xhr.responseText);
                console.error('Response Headers:', xhr.getAllResponseHeaders());

                let errorMessage = 'Unknown error occurred';

                try {
                    const response = xhr.responseJSON;
                    console.error('Parsed Response JSON:', response);

                    if (response) {
                        if (response.message) {
                            errorMessage = response.message;
                            console.error('Server error message:', response.message);

                            // If there are additional error details, log them
                            if (response.data && response.data.errors) {
                                console.error('Additional error details:', response.data.errors);
                                const additionalErrors = Object.values(response.data.errors).flat();
                                errorMessage += '<br><br>Details:<br>' + additionalErrors.join('<br>');
                            }
                        } else if (response.errors) {
                            console.error('Validation errors:', response.errors);
                            const errors = Object.values(response.errors).flat();
                            errorMessage = 'Validation failed:<br><br>' + errors.join('<br>');

                            // Log detailed validation errors
                            console.error('=== DETAILED VALIDATION ERRORS ===');
                            for (const [field, fieldErrors] of Object.entries(response.errors)) {
                                console.error(`Field "${field}":`, fieldErrors);
                            }
                        } else if (response.error) {
                            errorMessage = response.error;
                        } else if (response.data && response.data.conflicts) {
                            console.error('Pricing conflicts detected:', response.data.conflicts);
                            errorMessage = 'Pricing conflicts detected. There are existing pricing rules for the selected dates.';
                        }
                    } else {
                        // Try to parse response text manually
                        if (xhr.responseText) {
                            console.error('Raw response text:', xhr.responseText);
                            if (xhr.responseText.includes('<!DOCTYPE html>')) {
                                errorMessage = 'Server returned HTML page instead of JSON. This usually indicates a server error or routing issue.';
                            } else if (xhr.responseText.includes('TokenMismatchException')) {
                                errorMessage = 'CSRF token mismatch. Please refresh the page and try again.';
                            } else if (xhr.responseText.includes('MethodNotAllowedHttpException')) {
                                errorMessage = 'HTTP method not allowed. The endpoint may not support POST requests.';
                            } else {
                                errorMessage = 'Server error: ' + xhr.responseText.substring(0, 300) + '...';
                            }
                        }
                    }
                } catch (parseError) {
                    console.error('Error parsing response:', parseError);
                    errorMessage = 'Failed to parse server response: ' + error;
                }

                console.error('Final error message to show:', errorMessage);

                // Special handling for service provider issues
                if (errorMessage.includes('Target class') && errorMessage.includes('does not exist')) {
                    errorMessage = 'Service registration error. Please clear application cache and try again.<br><br>Run: <code>php artisan cache:clear && php artisan config:clear</code>';
                }

                showError(errorMessage);
            },
            complete: function() {
                console.log('=== AJAX COMPLETE ===');
                console.log('Request finished, resetting button state');
                // Reset button state
                submitBtn.html(originalText).prop('disabled', false);
            }
        });
    });

    // Handle Quick Setup form submission
    $('#quickSetupForm').on('submit', function(e) {
        e.preventDefault();
        console.log('Quick Setup form submitted');

        const submitBtn = $('#quickSetupBtn');
        const originalText = submitBtn.html();

        // Show loading state
        submitBtn.html('<i class="fas fa-spinner fa-spin me-1"></i>Setting up...').prop('disabled', true);

        const formData = $(this).serialize();
        console.log('Quick setup data:', formData);

        $.ajax({
            url: '/admin/hotel/seasonal-pricing/quick-setup',
            method: 'POST',
            data: formData,
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                console.log('Quick setup success:', response);
                if (response.error) {
                    showError(response.message);
                } else {
                    showSuccess(response.message || 'Seasonal pricing setup completed successfully!');
                    $('#quickSetupModal').modal('hide');
                    // Refresh the page after a short delay
                    setTimeout(() => {
                        location.reload();
                    }, 1500);
                }
            },
            error: function(xhr, status, error) {
                console.error('Quick setup error:', xhr, status, error);
                const response = xhr.responseJSON;
                if (response && response.message) {
                    showError(response.message);
                } else if (response && response.errors) {
                    const errors = Object.values(response.errors).flat();
                    showError(errors.join('<br>'));
                } else {
                    showError('Failed to setup seasonal pricing: ' + error);
                }
            },
            complete: function() {
                // Reset button state
                submitBtn.html(originalText).prop('disabled', false);
            }
        });
    });

    // Additional modal close handlers for better compatibility
    $('.btn-danger[data-bs-dismiss="modal"]').on('click', function() {
        const modalId = $(this).closest('.modal').attr('id');
        console.log('Closing modal:', modalId);
        $(`#${modalId}`).modal('hide');
    });

    // Debug modal functionality
    console.log('Modal buttons initialized');
    console.log('Cancel buttons found:', $('.btn-danger[data-bs-dismiss="modal"]').length);
    console.log('Form elements found:');
    console.log('- bulkSeasonalForm:', $('#bulkSeasonalForm').length);
    console.log('- quickSetupForm:', $('#quickSetupForm').length);
    console.log('- createSeasonalBtn:', $('#createSeasonalBtn').length);
    console.log('- quickSetupBtn:', $('#quickSetupBtn').length);

    // Test form submission handler
    console.log('Testing form submission handler attachment...');
    const formHandlers = $._data($('#bulkSeasonalForm')[0], 'events');
    console.log('Form event handlers:', formHandlers);
});

</script>
@endsection
