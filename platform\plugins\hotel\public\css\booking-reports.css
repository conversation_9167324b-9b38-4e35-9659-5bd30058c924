/* Booking Reports CSS */

/* Card hover effect */
.hover-translate-y {
    transition: transform 0.3s ease;
}

.hover-translate-y:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1) !important;
}

/* Progress bars */
.progress {
    background-color: rgba(0, 0, 0, 0.05);
    border-radius: 0.5rem;
    overflow: hidden;
}

.progress-bar {
    border-radius: 0.5rem;
}

/* Card icons */
.rounded-circle {
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Chart containers */
.chart-container {
    position: relative;
    min-height: 350px;
}

/* Table styles */
.table-responsive {
    border-radius: 0.5rem;
    overflow: hidden;
}

.table thead th {
    font-weight: 600;
    border-top: none;
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 0, 0, 0.02);
}

/* Badge styles */
.badge {
    padding: 0.5em 0.75em;
    font-weight: 500;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .chart-container {
        min-height: 300px;
    }
    
    .rounded-circle {
        width: 40px;
        height: 40px;
    }
    
    .card-body {
        padding: 1rem;
    }
}

/* Card shadows and borders */
.shadow-sm {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
}

.border-0 {
    border: none !important;
}

/* Card header styles */
.card-header {
    background-color: #fff;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

/* Animation for charts */
.apexcharts-canvas {
    transition: all 0.3s ease;
}
