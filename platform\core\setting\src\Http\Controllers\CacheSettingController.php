<?php

namespace Bo<PERSON>ble\Setting\Http\Controllers;

use Botble\Base\Http\Responses\BaseHttpResponse;
use Bo<PERSON>ble\Setting\Forms\CacheSettingForm;
use Botble\Setting\Http\Requests\CacheSettingRequest;

class CacheSettingController extends SettingController
{
    public function edit()
    {
        $this->pageTitle(trans('core/setting::setting.cache.title'));

        return CacheSettingForm::create()->renderForm();
    }

    public function update(CacheSettingRequest $request): BaseHttpResponse
    {
        return $this->performUpdate($request->validated());
    }
}
