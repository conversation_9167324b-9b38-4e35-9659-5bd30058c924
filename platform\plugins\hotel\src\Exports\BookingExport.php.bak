<?php

namespace Botble\Hotel\Exports;

use Botble\Hotel\Models\Booking;
use Botble\Hotel\Enums\BookingStatusEnum;
use Carbon\Carbon;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class BookingExport implements FromCollection, WithHeadings, WithMapping, WithStyles
{
    protected $startDate;
    protected $endDate;

    public function __construct($startDate, $endDate)
    {
        $this->startDate = $startDate;
        $this->endDate = $endDate;
    }

    public function collection()
    {
        return Booking::query()
            ->with(['customer', 'room'])
            ->whereDate('created_at', '>=', $this->startDate)
            ->whereDate('created_at', '<=', $this->endDate)
            ->orderBy('created_at', 'desc')
            ->get();
    }

    public function headings(): array
    {
        return [
            'ID',
            'Transaction ID',
            'Customer Name',
            'Customer Email',
            'Customer Phone',
            'Room',
            'Check-in Date',
            'Check-out Date',
            'Number of Guests',
            'Number of Children',
            'Amount',
            'Status',
            'Created At',
        ];
    }

    public function map($booking): array
    {
        return [
            $booking->id,
            $booking->transaction_id,
            $booking->customer ? $booking->customer->first_name . ' ' . $booking->customer->last_name : 'N/A',
            $booking->customer ? $booking->customer->email : 'N/A',
            $booking->customer ? $booking->customer->phone : 'N/A',
            $booking->room ? $booking->room->name : 'N/A',
            $booking->start_date ? Carbon::parse($booking->start_date)->format('Y-m-d') : 'N/A',
            $booking->end_date ? Carbon::parse($booking->end_date)->format('Y-m-d') : 'N/A',
            $booking->number_of_guests,
            $booking->number_of_children,
            format_price($booking->amount, null, true),
            $booking->status instanceof BookingStatusEnum ? $booking->status->label() : 'N/A',
            $booking->created_at->format('Y-m-d H:i:s'),
        ];
    }

    public function styles(Worksheet $sheet)
    {
        return [
            1 => ['font' => ['bold' => true]],
        ];
    }
}
