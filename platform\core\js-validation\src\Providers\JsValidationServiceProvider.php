<?php

namespace Bo<PERSON><PERSON>\JsValidation\Providers;

use Bo<PERSON>ble\Base\Supports\ServiceProvider;
use <PERSON><PERSON>ble\Base\Traits\LoadAndPublishDataTrait;
use <PERSON><PERSON>ble\JsValidation\Javascript\ValidatorHandler;
use <PERSON><PERSON><PERSON>\JsValidation\JsValidatorFactory;
use Botble\JsValidation\RemoteValidationMiddleware;
use Illuminate\Contracts\Http\Kernel;

class JsValidationServiceProvider extends ServiceProvider
{
    use LoadAndPublishDataTrait;

    public function boot(): void
    {
        $this
            ->setNamespace('core/js-validation')
            ->loadAndPublishConfigurations(['js-validation'])
            ->loadAndPublishViews()
            ->publishAssets();

        $this->bootstrapValidator();

        if ($this->app['config']->get('core.js-validation.js-validation.disable_remote_validation') === false) {
            $this->app[Kernel::class]->pushMiddleware(RemoteValidationMiddleware::class);
        }
    }

    protected function bootstrapValidator(): void
    {
        $callback = function () {
            return true;
        };

        $this->app['validator']->extend(ValidatorHandler::JS_VALIDATION_DISABLE, $callback);
    }

    public function register(): void
    {
        $this->app->singleton('js-validator', function ($app) {
            $config = $app['config']->get('core.js-validation.js-validation');

            return new JsValidatorFactory($app, $config);
        });
    }
}
