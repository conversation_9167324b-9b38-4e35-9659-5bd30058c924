<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('ht_room_dates', function (Blueprint $table) {
            if (!Schema::hasColumn('ht_room_dates', 'is_force_assigned')) {
                $table->boolean('is_force_assigned')->default(false)->after('is_booked');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('ht_room_dates', function (Blueprint $table) {
            if (Schema::hasColumn('ht_room_dates', 'is_force_assigned')) {
                $table->dropColumn('is_force_assigned');
            }
        });
    }
};