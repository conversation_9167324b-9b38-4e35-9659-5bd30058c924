<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        if (!Schema::hasColumn('ht_bookings', 'meta_data')) {
            Schema::table('ht_bookings', function (Blueprint $table) {
                $table->json('meta_data')->nullable()->after('transaction_id');
            });
        }
    }

    public function down(): void
    {
        if (Schema::hasColumn('ht_bookings', 'meta_data')) {
            Schema::table('ht_bookings', function (Blueprint $table) {
                $table->dropColumn('meta_data');
            });
        }
    }
};
