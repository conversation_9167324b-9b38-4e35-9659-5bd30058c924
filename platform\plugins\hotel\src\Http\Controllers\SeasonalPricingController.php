<?php

namespace Botble\Hotel\Http\Controllers;

use Botble\Base\Http\Controllers\BaseController;
use Botble\Base\Http\Responses\BaseHttpResponse;
use Botble\Hotel\Models\Room;
use Botble\Hotel\Models\RoomCategory;
use Botble\Hotel\Models\RoomPrice;
use Botble\Hotel\Services\SeasonalPricingService;
use Botble\Hotel\Services\DynamicPricingService;
use Illuminate\Support\Facades\Validator;
use Carbon\Carbon;
use Illuminate\Http\Request;


class SeasonalPricingController extends BaseController
{
    protected $seasonalPricingService;
    protected $dynamicPricingService;

    public function __construct(SeasonalPricingService $seasonalPricingService, DynamicPricingService $dynamicPricingService)
    {
        $this->seasonalPricingService = $seasonalPricingService;
        $this->dynamicPricingService = $dynamicPricingService;
    }

    /**
     * Display seasonal pricing management dashboard
     */
    public function index(Request $request)
    {
        $rooms = Room::where('status', 'published')->get();
        $roomCategories = RoomCategory::all();
        $seasonTypes = RoomPrice::getSeasonTypes();
        $stats = $this->seasonalPricingService->getSeasonalPricingStats();

        // Get future pricing for display (with fallback for missing columns)
        try {
            if (\Schema::hasColumn('ht_room_prices', 'is_future_pricing')) {
                $futurePricing = RoomPrice::futurePricing()
                    ->active()
                    ->approved()
                    ->with(['room', 'roomCategory'])
                    ->orderBy('start_date')
                    ->paginate(20);
            } else {
                // Fallback: get recent pricing if columns don't exist
                $futurePricing = RoomPrice::active()
                    ->approved()
                    ->with(['room', 'roomCategory'])
                    ->orderBy('start_date', 'desc')
                    ->paginate(20);
            }
        } catch (\Exception $e) {
            // Empty collection if error
            $futurePricing = new \Illuminate\Pagination\LengthAwarePaginator([], 0, 20);
        }

        return view('plugins/hotel::seasonal-pricing.index', compact(
            'rooms',
            'roomCategories', 
            'seasonTypes',
            'stats',
            'futurePricing'
        ));
    }

    /**
     * Create bulk seasonal pricing
     */
    public function createBulkSeasonal(Request $request, BaseHttpResponse $response)
    {
        // Log incoming request data for debugging
        \Log::info('Seasonal Pricing Request Data:', $request->all());

        // Build validation rules dynamically based on pricing scope
        $rules = [
            'season_name' => 'required|string|max:255',
            'season_type' => 'required|string|in:' . implode(',', array_keys(RoomPrice::getSeasonTypes())),
            'start_date' => 'required|date|after_or_equal:today',
            'end_date' => 'required|date|after:start_date',
            'pricing_scope' => 'required|in:all_rooms,room_category,specific_room',
            'adjustment_type' => 'required|in:percentage,fixed_amount,fixed_price',
            'adjustment_value' => 'required|numeric|min:0',
            'price_reason' => 'nullable|string|max:500',
            'customer_message' => 'nullable|string|max:1000',
            'priority' => 'nullable|integer|min:1|max:100',
        ];

        // Add conditional validation based on pricing scope
        $pricingScope = $request->input('pricing_scope');
        if ($pricingScope === 'specific_room') {
            $rules['room_id'] = 'required|exists:ht_rooms,id';
        } elseif ($pricingScope === 'room_category') {
            $rules['room_category_id'] = 'required|exists:ht_room_categories,id';
        }

        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            \Log::error('Seasonal Pricing Validation Failed:', [
                'errors' => $validator->errors()->toArray(),
                'request_data' => $request->all()
            ]);

            return $response
                ->setError()
                ->setMessage('Validation failed')
                ->setData(['errors' => $validator->errors()]);
        }

        // Check for conflicts first
        $conflicts = $this->seasonalPricingService->checkPricingConflicts($request->all());
        if (!empty($conflicts) && !$request->input('force_create', false)) {
            return $response
                ->setError()
                ->setMessage('Pricing conflicts detected')
                ->setData(['conflicts' => $conflicts]);
        }

        // Create seasonal pricing
        $result = $this->seasonalPricingService->createSeasonalPricing($request->all());

        if ($result['success'] > 0) {
            return $response
                ->setMessage("Successfully created seasonal pricing for {$result['success']} rooms")
                ->setData($result);
        } else {
            return $response
                ->setError()
                ->setMessage('Failed to create seasonal pricing')
                ->setData($result);
        }
    }

    /**
     * Preview seasonal pricing impact
     */
    public function previewSeasonal(Request $request, BaseHttpResponse $response)
    {
        $validator = Validator::make($request->all(), [
            'pricing_scope' => 'required|in:all_rooms,room_category,specific_room',
            'room_id' => 'required_if:pricing_scope,specific_room|exists:ht_rooms,id',
            'room_category_id' => 'required_if:pricing_scope,room_category|exists:ht_room_categories,id',
            'adjustment_type' => 'required|in:percentage,fixed_amount,fixed_price',
            'adjustment_value' => 'required|numeric',
        ]);

        if ($validator->fails()) {
            return $response
                ->setError()
                ->setMessage('Validation failed')
                ->setData(['errors' => $validator->errors()]);
        }

        $preview = $this->seasonalPricingService->previewSeasonalPricing($request->all());

        return $response
            ->setMessage('Preview generated successfully')
            ->setData(['preview' => $preview]);
    }

    /**
     * Get pricing transparency for a specific booking
     */
    public function getPricingTransparency(Request $request, BaseHttpResponse $response)
    {
        $validator = Validator::make($request->all(), [
            'room_id' => 'required|exists:ht_rooms,id',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after:start_date',
            'rooms' => 'nullable|integer|min:1',
        ]);

        if ($validator->fails()) {
            return $response
                ->setError()
                ->setMessage('Validation failed')
                ->setData(['errors' => $validator->errors()]);
        }

        $room = Room::findOrFail($request->room_id);
        $transparency = $this->dynamicPricingService->getPricingTransparency(
            $room,
            $request->start_date,
            $request->end_date,
            $request->rooms ?? 1
        );

        return $response
            ->setMessage('Pricing transparency retrieved successfully')
            ->setData(['transparency' => $transparency]);
    }

    /**
     * Get future pricing calendar for a room
     */
    public function getFuturePricingCalendar(Request $request, BaseHttpResponse $response)
    {
        $validator = Validator::make($request->all(), [
            'room_id' => 'required|exists:ht_rooms,id',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after:start_date',
        ]);

        if ($validator->fails()) {
            return $response
                ->setError()
                ->setMessage('Validation failed')
                ->setData(['errors' => $validator->errors()]);
        }

        $room = Room::findOrFail($request->room_id);
        $calendar = $this->dynamicPricingService->getFuturePricingCalendar(
            $room,
            $request->start_date,
            $request->end_date
        );

        return $response
            ->setMessage('Future pricing calendar retrieved successfully')
            ->setData(['calendar' => $calendar]);
    }

    /**
     * Delete seasonal pricing
     */
    public function deleteSeasonal(Request $request, BaseHttpResponse $response)
    {
        $validator = Validator::make($request->all(), [
            'season_type' => 'required|string',
            'start_date' => 'required|date',
            'end_date' => 'required|date',
        ]);

        if ($validator->fails()) {
            return $response
                ->setError()
                ->setMessage('Validation failed')
                ->setData(['errors' => $validator->errors()]);
        }

        $deletedCount = $this->seasonalPricingService->deleteSeasonalPricing(
            $request->season_type,
            $request->start_date,
            $request->end_date
        );

        return $response
            ->setMessage("Successfully deleted {$deletedCount} seasonal pricing rules")
            ->setData(['deleted_count' => $deletedCount]);
    }

    /**
     * Quick setup for common seasonal patterns
     */
    public function quickSeasonalSetup(Request $request, BaseHttpResponse $response)
    {
        $validator = Validator::make($request->all(), [
            'pattern' => 'required|in:function_season,peak_season,festival_season,wedding_season',
            'year' => 'required|integer|min:' . date('Y') . '|max:' . (date('Y') + 2),
        ]);

        if ($validator->fails()) {
            return $response
                ->setError()
                ->setMessage('Validation failed')
                ->setData(['errors' => $validator->errors()]);
        }

        $seasonalData = $this->getQuickSeasonalData($request->pattern, $request->year);
        $result = $this->seasonalPricingService->createSeasonalPricing($seasonalData);

        if ($result['success'] > 0) {
            return $response
                ->setMessage("Successfully set up {$request->pattern} pricing for {$result['success']} rooms")
                ->setData($result);
        } else {
            return $response
                ->setError()
                ->setMessage('Failed to set up seasonal pricing')
                ->setData($result);
        }
    }

    /**
     * Get predefined seasonal data patterns
     */
    protected function getQuickSeasonalData(string $pattern, int $year): array
    {
        $patterns = [
            'function_season' => [
                'season_name' => 'Function Season',
                'season_type' => RoomPrice::SEASON_FUNCTION,
                'start_date' => Carbon::create($year, 12, 15)->format('Y-m-d'),
                'end_date' => Carbon::create($year + 1, 1, 15)->format('Y-m-d'),
                'adjustment_type' => 'percentage',
                'adjustment_value' => 20,
                'price_reason' => 'Increased demand during function season',
                'customer_message' => 'Function Season Premium - Enhanced services and amenities for special events',
                'priority' => 15,
            ],
            'peak_season' => [
                'season_name' => 'Peak Season',
                'season_type' => RoomPrice::SEASON_PEAK,
                'start_date' => Carbon::create($year, 11, 1)->format('Y-m-d'),
                'end_date' => Carbon::create($year + 1, 2, 28)->format('Y-m-d'),
                'adjustment_type' => 'percentage',
                'adjustment_value' => 30,
                'price_reason' => 'Peak tourist season pricing',
                'customer_message' => 'Peak Season - Premium experience with full amenities',
                'priority' => 20,
            ],
            'festival_season' => [
                'season_name' => 'Festival Season',
                'season_type' => RoomPrice::SEASON_FESTIVAL,
                'start_date' => Carbon::create($year, 10, 1)->format('Y-m-d'),
                'end_date' => Carbon::create($year, 11, 30)->format('Y-m-d'),
                'adjustment_type' => 'percentage',
                'adjustment_value' => 25,
                'price_reason' => 'Festival season special pricing',
                'customer_message' => 'Festival Season - Special cultural experiences included',
                'priority' => 18,
            ],
            'wedding_season' => [
                'season_name' => 'Wedding Season',
                'season_type' => RoomPrice::SEASON_WEDDING,
                'start_date' => Carbon::create($year, 11, 15)->format('Y-m-d'),
                'end_date' => Carbon::create($year + 1, 3, 15)->format('Y-m-d'),
                'adjustment_type' => 'percentage',
                'adjustment_value' => 35,
                'price_reason' => 'Wedding season premium pricing',
                'customer_message' => 'Wedding Season - Exclusive wedding services and decorations',
                'priority' => 25,
            ],
        ];

        $data = $patterns[$pattern];
        $data['pricing_scope'] = 'all_rooms';

        return $data;
    }
}
