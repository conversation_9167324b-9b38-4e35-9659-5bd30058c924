'use strict';

class AdvancedReports {
    constructor() {
        this.init();
        this.handleChartFilters();
    }

    init() {
        // Initialize charts
        this.initBookingTrendChart();
        this.initRoomAvailabilityChart();
        this.initRevenueTrendChart();
        this.initBookingStatusChart();
    }

    handleChartFilters() {
        // Handle chart period filters
        $(document).on('click', '.chart-filter', (event) => {
            event.preventDefault();
            const period = $(event.currentTarget).data('period');
            const chartId = $(event.currentTarget).data('chart');
            
            // Update the button text
            const buttonText = $(event.currentTarget).text();
            $(event.currentTarget).closest('.dropdown').find('button').text(buttonText);
            
            // Update the chart based on the period
            this.updateChartByPeriod(chartId, period);
        });

        // Handle chart type filters
        $(document).on('click', '.chart-type', (event) => {
            event.preventDefault();
            const type = $(event.currentTarget).data('type');
            const chartId = $(event.currentTarget).data('chart');
            
            // Update the chart type
            this.updateChartType(chartId, type);
        });

        // Handle chart refresh
        $(document).on('click', '.refresh-chart', (event) => {
            event.preventDefault();
            const chartId = $(event.currentTarget).data('chart');
            
            // Refresh the chart
            this.refreshChart(chartId);
        });
    }

    updateChartByPeriod(chartId, period) {
        // Get the current date
        const now = new Date();
        let startDate, endDate;
        
        // Calculate the start and end dates based on the period
        switch (period) {
            case 'year':
                startDate = new Date(now.getFullYear(), 0, 1);
                endDate = new Date(now.getFullYear(), 11, 31);
                break;
            case '6months':
                startDate = new Date(now.getFullYear(), now.getMonth() - 5, 1);
                endDate = new Date(now.getFullYear(), now.getMonth() + 1, 0);
                break;
            case '3months':
                startDate = new Date(now.getFullYear(), now.getMonth() - 2, 1);
                endDate = new Date(now.getFullYear(), now.getMonth() + 1, 0);
                break;
            default:
                startDate = new Date(now.getFullYear(), 0, 1);
                endDate = new Date(now.getFullYear(), 11, 31);
        }
        
        // Format dates for API
        const formattedStartDate = this.formatDate(startDate);
        const formattedEndDate = this.formatDate(endDate);
        
        // Make API call to get updated data
        $.ajax({
            url: route('booking.reports.advanced.data'),
            method: 'GET',
            data: {
                start_date: formattedStartDate,
                end_date: formattedEndDate
            },
            success: (response) => {
                // Update the chart with new data
                switch (chartId) {
                    case 'booking-trend-chart':
                        this.updateBookingTrendChart(response.bookingTrend);
                        break;
                    case 'revenue-trend-chart':
                        this.updateRevenueTrendChart(response.revenueTrend);
                        break;
                }
            },
            error: (error) => {
                console.error('Error fetching chart data:', error);
            }
        });
    }

    updateChartType(chartId, type) {
        // Get the chart instance
        let chart;
        
        switch (chartId) {
            case 'booking-trend-chart':
                chart = window.bookingTrendChart;
                break;
            case 'revenue-trend-chart':
                chart = window.revenueTrendChart;
                break;
        }
        
        if (chart) {
            // Update the chart type
            chart.updateOptions({
                chart: {
                    type: type
                }
            });
        }
    }

    refreshChart(chartId) {
        // Refresh the chart data
        switch (chartId) {
            case 'room-availability-chart':
                this.initRoomAvailabilityChart();
                break;
            case 'booking-status-chart':
                this.initBookingStatusChart();
                break;
        }
    }

    formatDate(date) {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    }

    initBookingTrendChart() {
        const options = {
            series: [{
                name: 'Bookings',
                data: window.bookingTrendData || []
            }],
            chart: {
                height: 350,
                type: 'area',
                toolbar: {
                    show: true
                }
            },
            dataLabels: {
                enabled: false
            },
            stroke: {
                curve: 'smooth'
            },
            xaxis: {
                type: 'datetime',
                categories: window.bookingTrendLabels || []
            },
            tooltip: {
                x: {
                    format: 'dd MMM yyyy'
                }
            },
            colors: ['#0d6efd']
        };

        if (document.getElementById('booking-trend-chart')) {
            window.bookingTrendChart = new ApexCharts(document.getElementById('booking-trend-chart'), options);
            window.bookingTrendChart.render();
        }
    }

    updateBookingTrendChart(data) {
        if (window.bookingTrendChart) {
            window.bookingTrendChart.updateSeries([{
                name: 'Bookings',
                data: data.values || []
            }]);
            
            window.bookingTrendChart.updateOptions({
                xaxis: {
                    categories: data.labels || []
                }
            });
        }
    }

    initRoomAvailabilityChart() {
        const options = {
            series: window.roomAvailabilityData || [10, 20],
            chart: {
                width: '100%',
                type: 'pie',
            },
            labels: window.roomAvailabilityLabels || ['Available', 'Booked'],
            colors: ['#0d6efd', '#dc3545'],
            responsive: [{
                breakpoint: 480,
                options: {
                    chart: {
                        width: 200
                    },
                    legend: {
                        position: 'bottom'
                    }
                }
            }]
        };

        if (document.getElementById('room-availability-chart')) {
            if (window.roomAvailabilityChart) {
                window.roomAvailabilityChart.destroy();
            }
            window.roomAvailabilityChart = new ApexCharts(document.getElementById('room-availability-chart'), options);
            window.roomAvailabilityChart.render();
        }
    }

    initRevenueTrendChart() {
        const options = {
            series: [{
                name: 'Revenue',
                data: window.revenueTrendData || []
            }],
            chart: {
                height: 350,
                type: 'area',
                toolbar: {
                    show: true
                }
            },
            dataLabels: {
                enabled: false
            },
            stroke: {
                curve: 'smooth'
            },
            xaxis: {
                type: 'datetime',
                categories: window.revenueTrendLabels || []
            },
            tooltip: {
                x: {
                    format: 'dd MMM yyyy'
                },
                y: {
                    formatter: function(value) {
                        return window.currencyFormat.replace(':price', value);
                    }
                }
            },
            colors: ['#198754']
        };

        if (document.getElementById('revenue-trend-chart')) {
            window.revenueTrendChart = new ApexCharts(document.getElementById('revenue-trend-chart'), options);
            window.revenueTrendChart.render();
        }
    }

    updateRevenueTrendChart(data) {
        if (window.revenueTrendChart) {
            window.revenueTrendChart.updateSeries([{
                name: 'Revenue',
                data: data.values || []
            }]);
            
            window.revenueTrendChart.updateOptions({
                xaxis: {
                    categories: data.labels || []
                }
            });
        }
    }

    initBookingStatusChart() {
        const options = {
            series: window.bookingStatusData || [10, 20, 30, 40],
            chart: {
                width: '100%',
                type: 'donut',
            },
            labels: window.bookingStatusLabels || ['Pending', 'Processing', 'Completed', 'Cancelled'],
            colors: ['#ffc107', '#0dcaf0', '#198754', '#dc3545'],
            responsive: [{
                breakpoint: 480,
                options: {
                    chart: {
                        width: 200
                    },
                    legend: {
                        position: 'bottom'
                    }
                }
            }]
        };

        if (document.getElementById('booking-status-chart')) {
            if (window.bookingStatusChart) {
                window.bookingStatusChart.destroy();
            }
            window.bookingStatusChart = new ApexCharts(document.getElementById('booking-status-chart'), options);
            window.bookingStatusChart.render();
        }
    }
}

$(document).ready(() => {
    new AdvancedReports();
});
