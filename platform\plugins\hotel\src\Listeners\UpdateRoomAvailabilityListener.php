<?php

namespace Botble\Hotel\Listeners;

use Botble\Hotel\Events\BookingCreated;
use Botble\Hotel\Events\BookingUpdated;
use Botble\Hotel\Models\RoomDate;
use Botble\Hotel\Models\RoomInventory;
use Illuminate\Support\Facades\Log;

class UpdateRoomAvailabilityListener
{
    public function handle($event): void
    {
        // Handle both BookingCreated and BookingUpdated events
        $booking = $event->booking;

        // Get the booking room
        $bookingRoom = $booking->room;

        // If no booking room or no room_id_code is set, there's nothing to update
        if (!$bookingRoom || !$bookingRoom->room_id_code) {
            // Try using the booking's room_id_code as a fallback
            if ($booking->room_id_code) {
                $this->updateRoomAvailabilityForBooking($booking);
            }
            return;
        }

        try {
            // Check if the room exists in the inventory
            $roomInventory = RoomInventory::where('room_id_code', $bookingRoom->room_id_code)
                ->where('room_id', $bookingRoom->room_id)
                ->first();

            if (!$roomInventory) {
                Log::warning("Room inventory not found for room_id_code: {$bookingRoom->room_id_code}");
                return;
            }

            // Create or update a record in the room_dates table to mark this room as booked
            $roomDate = RoomDate::updateOrCreate(
                [
                    'room_id' => $bookingRoom->room_id,
                    'room_id_code' => $bookingRoom->room_id_code,
                    'booking_id' => $booking->id,
                ],
                [
                    'start_date' => $bookingRoom->start_date,
                    'end_date' => $bookingRoom->end_date,
                    'is_booked' => true,
                    'active' => true,
                ]
            );

            // Also update the RoomInventory table to mark the room as unavailable
            // $roomInventory->is_available = false;
            // $roomInventory->save();

           // Log::info("Room availability updated for booking #{$booking->id}, room: {$bookingRoom->room_id_code}, RoomInventory updated: is_available = false");
        } catch (\Exception $e) {
            Log::error("Error updating room availability: " . $e->getMessage());
        }
    }

    /**
     * Update room availability using the booking model directly
     */
    private function updateRoomAvailabilityForBooking($booking): void
    {
        try {
            // Find the room ID from the booking's room relationship
            $roomId = $booking->room->room_id ?? null;

            if (!$roomId) {
                Log::warning("No room ID found for booking #{$booking->id}");
                return;
            }

            // Check if the room exists in the inventory
            $roomInventory = RoomInventory::where('room_id_code', $booking->room_id_code)
                ->where('room_id', $roomId)
                ->first();

            if (!$roomInventory) {
                Log::warning("Room inventory not found for room_id_code: {$booking->room_id_code}");
                return;
            }

            // Get dates from the booking room
            $startDate = $booking->room->start_date ?? null;
            $endDate = $booking->room->end_date ?? null;

            if (!$startDate || !$endDate) {
                Log::warning("No date range found for booking #{$booking->id}");
                return;
            }

            // Create or update a record in the room_dates table to mark this room as booked
            $roomDate = RoomDate::updateOrCreate(
                [
                    'room_id' => $roomId,
                    'room_id_code' => $booking->room_id_code,
                    'booking_id' => $booking->id,
                ],
                [
                    'start_date' => $startDate,
                    'end_date' => $endDate,
                    'is_booked' => true,
                    'active' => true,
                ]
            );

            // Also update the RoomInventory table to mark the room as unavailable
            // $roomInventory->is_available = false;
            // $roomInventory->save();

            // Log::info("Room availability updated for booking #{$booking->id}, room: {$booking->room_id_code}, RoomInventory updated: is_available = false");
        } catch (\Exception $e) {
            Log::error("Error updating room availability from booking: " . $e->getMessage());
        }
    }
}
