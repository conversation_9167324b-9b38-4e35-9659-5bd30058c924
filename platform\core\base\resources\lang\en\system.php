<?php

return [
    'no_select' => 'Please select at least one record to take this action!',
    'cannot_find_user' => 'Unable to find specified user',
    'supper_revoked' => 'Super user access revoked',
    'cannot_revoke_yourself' => 'Can not revoke supper user access permission yourself!',
    'cant_remove_supper' => 'You don\'t has permission to remove this super user',
    'cant_find_user_with_email' => 'Unable to find user with specified email address',
    'supper_granted' => 'Super user access granted',
    'delete_log_success' => 'Delete log file successfully!',
    'get_member_success' => 'Member list retrieved successfully',
    'error_occur' => 'The following errors occurred',
    'user_management' => 'User Management',
    'user_management_description' => 'Manage users.',
    'role_and_permission' => 'Roles and Permissions',
    'role_and_permission_description' => 'Manage the available roles.',
    'user' => [
        'list_super' => 'List Super Users',
        'email' => 'Email',
        'last_login' => 'Last Login',
        'username' => 'Username',
        'add_user' => 'Add Super User',
        'cancel' => 'Cancel',
        'create' => 'Create',
    ],
    'options' => [
        'features' => 'Feature Access Control',
        'feature_description' => 'Manage the available.',
        'manage_super' => 'Super User Management',
        'manage_super_description' => 'Add/remove super users.',
        'info' => 'System information',
        'info_description' => 'All information about current system configuration.',
    ],
    'info' => [
        'title' => 'System Information',
        'description' => 'All information about current system configuration.',
        'cache' => 'Cache',
        'locale' => 'Active locale',
        'environment' => 'Environment',
    ],
    'disabled_in_demo_mode' => 'You cannot do it in demo mode!',
    'report_description' => 'Please share this information for troubleshooting',
    'get_system_report' => 'Get System Report',
    'system_environment' => 'System Environment',
    'framework_version' => 'Framework Version',
    'timezone' => 'Timezone',
    'debug_mode' => 'Debug Mode',
    'debug_mode_off' => 'Debug Mode Off',
    'storage_dir_writable' => 'Storage Dir Writable',
    'cache_dir_writable' => 'Cache Dir Writable',
    'app_size' => 'App Size',
    'server_environment' => 'Server Environment',
    'php_version' => 'PHP Version',
    'php_version_error' => 'PHP must be >= :version',
    'server_software' => 'Server Software',
    'server_os' => 'Server OS',
    'database' => 'Database',
    'ssl_installed' => 'SSL Installed',
    'cache_driver' => 'Cache Driver',
    'session_driver' => 'Session Driver',
    'queue_connection' => 'Queue Connection',
    'mbstring_ext' => 'Mbstring Ext',
    'openssl_ext' => 'OpenSSL Ext',
    'pdo_ext' => 'PDO Ext',
    'curl_ext' => 'CURL Ext',
    'exif_ext' => 'Exif Ext',
    'file_info_ext' => 'File info Ext',
    'tokenizer_ext' => 'Tokenizer Ext',
    'extra_stats' => 'Extra Stats',
    'installed_packages' => 'Installed packages and their version numbers',
    'extra_information' => 'Extra Information',
    'copy_report' => 'Copy Report',
    'package_name' => 'Package Name',
    'dependency_name' => 'Dependency Name',
    'server_ip' => 'Server IP',
    'version' => 'Version',
    'cms_version' => 'CMS Version',
    'imagick_or_gd_ext' => 'Imagick/GD Ext',
    'updater' => 'System Updater',
    'zip' => 'Zip Ext',
    'iconv' => 'Iconv Ext',
    'memory_limit' => 'Memory limit',
    'max_execution_time' => 'Max execution time (s)',
    'allow_url_fopen_enabled' => 'allow_url_fopen enabled',
    'cleanup' => [
        'title' => 'Cleanup System',
        'description' => 'Cleanup your unused data in database',
        'table' => [
            'name' => 'Table Name',
            'count' => 'Records',
        ],
        'backup_alert' => 'Please backup your database and script files before cleanup, it will clear your data in database.',
        'messenger_choose_without_table' => 'Please choose to ignore tables that do not want to be cleaned',
        'messenger_confirm_cleanup' => 'Are you sure you want to database cleanup action, it will clear your data in database?',
        'submit_button' => 'Cleanup',
        'success_message' => 'Cleaned data successfully',
        'not_enabled_yet' => 'This featured is not enabled yet. <br />Please add into .env: <code>CMS_ENABLED_CLEANUP_DATABASE=true</code> to enable this feature!',
    ],
];
