
$(document).ready(function() {
    // Immediately hide any room ID errors if a room is already selected
    if ($('select[name="room_id"]').val() || $('input[name="room_id"]').val()) {
        hideRoomIdErrors();
    }
    
    // Handle room selection
    $(document).on('change', 'select[name="room_id"], input[name="room_id"]', function() {
        if ($(this).val()) {
            hideRoomIdErrors();
        }
    });
    
    // Handle booking status changes
    $('select[name="status"]').on('change', function() {
        var newStatus = $(this).val();
        var oldStatus = $(this).data('old-status');
        
        // Store the old status for reference
        if (!oldStatus) {
            $(this).data('old-status', newStatus);
        }
        
        // Handle status change to "processing"
        if (newStatus === 'processing' && oldStatus !== 'processing') {
            // Set check-in time to current time
            var now = new Date();
            var formattedDateTime = formatDateTime(now);
            
            // Update the check-in time field if it exists and is empty
            if ($('.check-in-time-field').length && !$('.check-in-time-field').val()) {
                $('.check-in-time-field').val(formattedDateTime);
            }
            
            // Show a notification
            if (typeof toastr !== 'undefined') {
                toastr.info('Check-in time has been set to the current time: ' + formattedDateTime);
            } else {
                alert('Check-in time has been set to the current time: ' + formattedDateTime);
            }
        }
        
        // Handle status change to "completed"
        if (newStatus === 'completed' && oldStatus !== 'completed') {
            // Set check-out time to current time
            var now = new Date();
            var formattedDateTime = formatDateTime(now);
            
            // Update the check-out time field if it exists and is empty
            if ($('.check-out-time-field').length && !$('.check-out-time-field').val()) {
                $('.check-out-time-field').val(formattedDateTime);
            }
            
            // Show a notification about room availability
            if (typeof toastr !== 'undefined') {
                toastr.success('Check-out time has been set to the current time: ' + formattedDateTime + '. The room will be marked as available.');
            } else {
                alert('Check-out time has been set to the current time: ' + formattedDateTime + '. The room will be marked as available.');
            }
            
            // Add a hidden field to indicate that the room should be marked as available
            if (!$('input[name="mark_room_available"]').length) {
                $('form').append('<input type="hidden" name="mark_room_available" value="1">');
            }
            
            // Hide room ID errors for completed bookings
            hideRoomIdErrors();
        }
        
        // Remove any existing room ID error messages when status is changed to completed or cancelled
        if (newStatus === 'completed' || newStatus === 'cancelled') {
            hideRoomIdErrors();
            
            // For cancelled or completed bookings, add a hidden field to bypass room validation
            if (!$('input[name="bypass_room_validation"]').length) {
                $('form').append('<input type="hidden" name="bypass_room_validation" value="1">');
            }
        }
        
        // Update the old status
        $(this).data('old-status', newStatus);
    });
    
    // Function to hide all room ID related errors
    function hideRoomIdErrors() {
        // Remove any room ID error messages
        $('.room-id-error').remove();
        
        // Hide any alert containers with room ID messages
        $('.alert:contains("room ID")').hide();
        $('[data-error-message*="room ID"]').closest('.alert').hide();
        $('.alert-warning:contains("select a room ID")').hide();
        $('.alert-danger:contains("room ID")').hide();
        
        // If using Botble's notification system, clear any related notifications
        if (typeof Botble !== 'undefined') {
            if (Botble.hideNotice) {
                Botble.hideNotice();
            }
            
            // Remove specific error messages from the page
            $('.error-message:contains("room ID")').remove();
            $('.alert .text-danger:contains("room ID")').closest('.alert').hide();
        }
    }
    
    // Helper function to format date and time
    function formatDateTime(date) {
        var year = date.getFullYear();
        var month = padZero(date.getMonth() + 1);
        var day = padZero(date.getDate());
        var hours = padZero(date.getHours());
        var minutes = padZero(date.getMinutes());
        var seconds = padZero(date.getSeconds());
        
        return year + '-' + month + '-' + day + ' ' + hours + ':' + minutes + ':' + seconds;
    }
    
    // Helper function to pad zeros
    function padZero(num) {
        return (num < 10 ? '0' : '') + num;
    }
    
    // Add form submit handler to ensure errors are hidden before submission
    $('form').on('submit', function() {
        if ($('select[name="status"]').val() === 'completed' || $('select[name="status"]').val() === 'cancelled') {
            hideRoomIdErrors();
            
            // For cancelled or completed bookings, add a hidden field to bypass room validation
            if (!$('input[name="bypass_room_validation"]').length) {
                $(this).append('<input type="hidden" name="bypass_room_validation" value="1">');
            }
        }
    });
});

