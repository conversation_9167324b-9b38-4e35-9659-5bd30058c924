@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-dollar-sign"></i>
                        Dynamic Room Pricing Management
                    </h3>
                    <div class="card-tools">
                        <button type="button" class="btn btn-primary btn-sm" data-toggle="modal" data-target="#addPriceModal">
                            <i class="fas fa-plus"></i> Add New Price
                        </button>
                        <button type="button" class="btn btn-info btn-sm" data-toggle="modal" data-target="#bulkSeasonalModal">
                            <i class="fas fa-calendar"></i> Bulk Seasonal Pricing
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Room Selection -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <label for="roomSelect">Select Room:</label>
                            <select id="roomSelect" class="form-control">
                                <option value="">-- Select a Room --</option>
                                @foreach($rooms as $room)
                                    <option value="{{ $room->id }}" {{ $selectedRoom && $selectedRoom->id == $room->id ? 'selected' : '' }}>
                                        {{ $room->name }} (Base Price: ${{ number_format($room->price, 2) }})
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label>&nbsp;</label>
                            <div>
                                <button type="button" class="btn btn-secondary" onclick="previewPricing()">
                                    <i class="fas fa-eye"></i> Preview Pricing
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Pricing Table -->
                    @if($selectedRoom)
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="thead-dark">
                                <tr>
                                    <th>Name</th>
                                    <th>Date Range</th>
                                    <th>Price/Adjustment</th>
                                    <th>Type</th>
                                    <th>Priority</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($selectedRoom->prices as $price)
                                <tr>
                                    <td>
                                        <strong>{{ $price->name }}</strong>
                                        @if($price->description)
                                            <br><small class="text-muted">{{ $price->description }}</small>
                                        @endif
                                    </td>
                                    <td>
                                        {{ $price->start_date->format('M d, Y') }} -
                                        {{ $price->end_date->format('M d, Y') }}
                                        <br>
                                        <small class="text-muted">
                                            {{ $price->start_date->diffInDays($price->end_date) }} days
                                        </small>
                                    </td>
                                    <td>
                                        @if($price->pricing_type === 'fixed')
                                            <span class="badge badge-success">${{ number_format($price->price, 2) }}</span>
                                        @elseif($price->pricing_type === 'percentage')
                                            <span class="badge badge-info">{{ $price->adjustment_value > 0 ? '+' : '' }}{{ $price->adjustment_value }}%</span>
                                        @else
                                            <span class="badge badge-warning">${{ $price->adjustment_value > 0 ? '+' : '' }}{{ number_format($price->adjustment_value, 2) }}</span>
                                        @endif
                                    </td>
                                    <td>
                                        <span class="badge badge-secondary">{{ ucfirst($price->pricing_type) }}</span>
                                    </td>
                                    <td>
                                        <span class="badge badge-primary">{{ $price->priority }}</span>
                                    </td>
                                    <td>
                                        <div class="custom-control custom-switch">
                                            <input type="checkbox" class="custom-control-input"
                                                   id="switch{{ $price->id }}"
                                                   {{ $price->is_active ? 'checked' : '' }}
                                                   onchange="toggleActive({{ $price->id }})">
                                            <label class="custom-control-label" for="switch{{ $price->id }}"></label>
                                        </div>
                                    </td>
                                    <td>
                                        <button class="btn btn-sm btn-warning" onclick="editPrice({{ $price->id }})">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-sm btn-danger" onclick="deletePrice({{ $price->id }})">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="7" class="text-center text-muted">
                                        No pricing rules found for this room. Using base price only.
                                    </td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                    @else
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        Please select a room to view and manage its pricing rules.
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Price Modal -->
<div class="modal fade" id="addPriceModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add New Room Price</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form id="addPriceForm">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Room</label>
                                <select name="room_id" class="form-control" required>
                                    <option value="">Select Room</option>
                                    @foreach($rooms as $room)
                                        <option value="{{ $room->id }}">{{ $room->name }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Name</label>
                                <input type="text" name="name" class="form-control" placeholder="e.g., Summer Season" required>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Start Date</label>
                                <input type="date" name="start_date" class="form-control" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>End Date</label>
                                <input type="date" name="end_date" class="form-control" required>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>Pricing Type</label>
                                <select name="pricing_type" class="form-control" onchange="togglePricingFields()" required>
                                    <option value="fixed">Fixed Price</option>
                                    <option value="percentage">Percentage Adjustment</option>
                                    <option value="amount_adjust">Amount Adjustment</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group" id="priceField">
                                <label>Price ($)</label>
                                <input type="number" name="price" class="form-control" step="0.01" min="0">
                            </div>
                            <div class="form-group" id="adjustmentField" style="display: none;">
                                <label>Adjustment Value</label>
                                <input type="number" name="adjustment_value" class="form-control" step="0.01">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>Priority (1-100)</label>
                                <input type="number" name="priority" class="form-control" min="1" max="100" value="1" required>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label>Description (Optional)</label>
                        <textarea name="description" class="form-control" rows="2"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Add Price</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Pricing Preview Modal -->
<div class="modal fade" id="previewModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Pricing Preview</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body" id="previewContent">
                <!-- Preview content will be loaded here -->
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Room selection change
$('#roomSelect').change(function() {
    const roomId = $(this).val();
    if (roomId) {
        window.location.href = `{{ route('room-prices.index') }}?room_id=${roomId}`;
    }
});

// Toggle pricing fields based on type
function togglePricingFields() {
    const type = $('select[name="pricing_type"]').val();
    if (type === 'fixed') {
        $('#priceField').show();
        $('#adjustmentField').hide();
        $('input[name="price"]').prop('required', true);
        $('input[name="adjustment_value"]').prop('required', false);
    } else {
        $('#priceField').hide();
        $('#adjustmentField').show();
        $('input[name="price"]').prop('required', false);
        $('input[name="adjustment_value"]').prop('required', true);
    }
}

// Add price form submission
$('#addPriceForm').submit(function(e) {
    e.preventDefault();

    $.ajax({
        url: '{{ route("room-prices.store") }}',
        method: 'POST',
        data: $(this).serialize(),
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        success: function(response) {
            if (response.success) {
                $('#addPriceModal').modal('hide');
                location.reload();
            }
        },
        error: function(xhr) {
            const errors = xhr.responseJSON.errors;
            // Handle validation errors
            console.error(errors);
        }
    });
});

// Toggle active status
function toggleActive(priceId) {
    $.ajax({
        url: `/admin/hotel/room-prices/${priceId}/toggle-active`,
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        success: function(response) {
            if (response.success) {
                // Status updated successfully
                console.log('Status updated');
            }
        }
    });
}

// Delete price
function deletePrice(priceId) {
    if (confirm('Are you sure you want to delete this pricing rule?')) {
        $.ajax({
            url: `/admin/hotel/room-prices/${priceId}`,
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                if (response.success) {
                    location.reload();
                }
            }
        });
    }
}

// Preview pricing
function previewPricing() {
    const roomId = $('#roomSelect').val();
    if (!roomId) {
        alert('Please select a room first');
        return;
    }

    // Show modal with date selection
    $('#previewModal').modal('show');
    $('#previewContent').html(`
        <div class="row">
            <div class="col-md-4">
                <label>Start Date:</label>
                <input type="date" id="previewStartDate" class="form-control">
            </div>
            <div class="col-md-4">
                <label>End Date:</label>
                <input type="date" id="previewEndDate" class="form-control">
            </div>
            <div class="col-md-4">
                <label>&nbsp;</label>
                <button class="btn btn-primary form-control" onclick="loadPreview()">Load Preview</button>
            </div>
        </div>
        <div id="previewResults" class="mt-4"></div>
    `);
}

function loadPreview() {
    const roomId = $('#roomSelect').val();
    const startDate = $('#previewStartDate').val();
    const endDate = $('#previewEndDate').val();

    if (!startDate || !endDate) {
        alert('Please select both start and end dates');
        return;
    }

    $.ajax({
        url: '{{ route("room-prices.preview") }}',
        method: 'POST',
        data: {
            room_id: roomId,
            start_date: startDate,
            end_date: endDate
        },
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        success: function(response) {
            if (response.success) {
                displayPreviewResults(response.pricing);
            }
        }
    });
}

function displayPreviewResults(pricing) {
    let html = `
        <div class="alert alert-info">
            <h5>Pricing Summary</h5>
            <p><strong>Total Price:</strong> $${pricing.total_price.toFixed(2)}</p>
            <p><strong>Base Price:</strong> $${pricing.base_price.toFixed(2)}</p>
            <p><strong>Savings:</strong> $${pricing.savings.toFixed(2)}</p>
            <p><strong>Nights:</strong> ${pricing.nights}</p>
        </div>
        <table class="table table-sm">
            <thead>
                <tr>
                    <th>Date</th>
                    <th>Base Price</th>
                    <th>Final Price</th>
                    <th>Applied Rules</th>
                </tr>
            </thead>
            <tbody>
    `;

    Object.values(pricing.daily_prices).forEach(day => {
        html += `
            <tr>
                <td>${day.date}</td>
                <td>$${day.base_price.toFixed(2)}</td>
                <td>$${day.final_price.toFixed(2)}</td>
                <td>
        `;

        day.applied_rules.forEach(rule => {
            html += `<span class="badge badge-info mr-1">${rule.name}</span>`;
        });

        html += `
                </td>
            </tr>
        `;
    });

    html += `
            </tbody>
        </table>
    `;

    $('#previewResults').html(html);
}
</script>
@endpush
