$(document).ready(function() {
    // Initialize select2 for room_id_code with custom placeholder
    $('select[name="room_id_code"]').select2({
        placeholder: 'Select Room ID',
        allowClear: true,
        width: '100%'
    });
    
    // Force the placeholder to show initially if no value is selected
    var initialValue = $('select[name="room_id_code"]').val();
    if (!initialValue || initialValue === '') {
        // Trigger change to show placeholder
        $('select[name="room_id_code"]').val(null).trigger('change');
    }
    
    // Add validation for room_id_code
    $('form').on('submit', function(e) {
        var roomIdCode = $('select[name="room_id_code"]').val();
        
        // Check if room_id_code is empty or "Select Room ID"
        if (!roomIdCode || roomIdCode === '' || roomIdCode === 'Select Room ID') {
            e.preventDefault();
            alert('Please select a valid room ID for this booking.');
            $('select[name="room_id_code"]').focus();
            return false;
        }
        
        return true;
    });
    
    // Add visual indicators for room selection
    $('#room_id_code').on('change', function() {
        // Get the booking ID (if this is an edit form)
        var bookingId = $('input[name="id"]').val();
        
        // If this is an existing booking being edited, skip availability check
        if (bookingId) {
            return true;
        }
        
        var roomIdCode = $(this).val();
        
        if (roomIdCode && roomIdCode !== '' && roomIdCode !== 'Select Room ID') {
            // Valid room selected
            $(this).closest('.form-group').removeClass('has-warning').addClass('has-success');
            $(this).closest('.form-group').find('.help-block').html('<strong>Room will be marked as booked when you save this booking.</strong>');
        } else {
            // No room selected or placeholder selected
            $(this).closest('.form-group').removeClass('has-success').addClass('has-warning');
            $(this).closest('.form-group').find('.help-block').html('Select a room ID to assign to this booking. The room will only be marked as booked after you select a valid room ID.');
        }
    });
    
    // Check initial value
    if (!initialValue || initialValue === '' || initialValue === 'Select Room ID') {
        // Highlight that this booking needs a room ID
        $('select[name="room_id_code"]').closest('.form-group').addClass('has-warning');
        $('select[name="room_id_code"]').closest('.form-group').find('.help-block').html('<strong>This booking needs a room ID. Please select a room ID to assign.</strong>');
    } else {
        // Already has a valid room ID
        $('select[name="room_id_code"]').closest('.form-group').removeClass('has-warning').addClass('has-success');
    }
});

