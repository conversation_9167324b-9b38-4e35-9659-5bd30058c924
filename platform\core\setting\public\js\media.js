$((function(){$(".generate-thumbnails-trigger-button").on("click",(function(t){t.preventDefault();var e=$(t.currentTarget),n=e.closest("form");$httpClient.make().withButtonLoading(e).postForm(n.prop("action"),new FormData(n[0])).then((function(t){var e=t.data;$("#generate-thumbnails-modal").modal("show"),$("#generate-thumbnails-modal").data("total-files",e.data.files_count)}))})),$("#generate-thumbnails-button").on("click",(function(t){t.preventDefault();var e=$(t.currentTarget),n=e.closest(".modal"),a=e.closest("form"),o=n.data("total-files"),i=null;Botble.showButtonLoading(e),function t(){var l=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,c=arguments.length>1&&void 0!==arguments[1]?arguments[1]:n.data("chunk-limit");if(l>o)return Botble.hideButtonLoading(e),n.modal("hide"),void Botble.showSuccess(i);$httpClient.make().post(a.prop("action"),{total:o,offset:l,limit:c}).then((function(e){var n=e.data;i=n.message,n.data.next&&t(n.data.next,c)})).finally((function(){Botble.hideButtonLoading(e)}))}()})),$(document).on("change",".check-all",(function(t){var e=$(t.currentTarget),n=e.attr("data-set"),a=e.prop("checked");$(n).each((function(t,e){$(e).prop("checked",a)}))}))}));