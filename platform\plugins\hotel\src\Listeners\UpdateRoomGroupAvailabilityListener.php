<?php

namespace Botble\Hotel\Listeners;

use Botble\Hotel\Events\BookingCreated;
use Botble\Hotel\Events\BookingStatusChanged;
use Botble\Hotel\Events\BookingUpdated;
use Botble\Hotel\Services\RoomGroupService;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Schema;

class UpdateRoomGroupAvailabilityListener
{
    protected RoomGroupService $roomGroupService;

    public function __construct(RoomGroupService $roomGroupService)
    {
        $this->roomGroupService = $roomGroupService;
    }

    public function handle($event): void
    {
        try {
            // Check if the room_groups table exists
            if (!Schema::hasTable('ht_room_groups') || !Schema::hasTable('ht_room_group_items')) {
                // Skip processing if tables don't exist
                return;
            }

            // Handle both BookingCreated, BookingUpdated, and BookingStatusChanged events
            $booking = $event->booking;
            $status = $booking->status;

            // Get the room ID code
            $roomIdCode = null;

            // Try to get room_id_code from booking room
            if ($booking->room && $booking->room->room_id_code) {
                $roomIdCode = $booking->room->room_id_code;
            }
            // Fallback to booking's room_id_code
            elseif ($booking->room_id_code) {
                $roomIdCode = $booking->room_id_code;
            }

            if (!$roomIdCode) {
                return;
            }

            // Check if this room is part of a group
            if (!$this->roomGroupService->isRoomInGroup($roomIdCode)) {
                return;
            }

            // If booking is completed or cancelled, mark all rooms in the group as available
            if ($status == 'completed' || $status == 'cancelled') {
                $this->roomGroupService->updateGroupAvailability($roomIdCode, true);
                Log::info("Room group availability updated for {$roomIdCode}: All rooms marked as available");
            }
            // If booking is pending or processing, mark all rooms in the group as unavailable
            else {
                $this->roomGroupService->updateGroupAvailability($roomIdCode, false);
                Log::info("Room group availability updated for {$roomIdCode}: All rooms marked as unavailable");
            }
        } catch (\Exception $e) {
            Log::error('Error updating room group availability: ' . $e->getMessage());
        }
    }
}
