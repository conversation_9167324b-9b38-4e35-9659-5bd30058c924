$select-color-dropdown: var(--bb-bg-surface-secondary);
$select-color-dropdown-border-top: red;
$input-border-width: 1px;

.select2-container--default {
    .select2-selection--single {
        background-color: #fff;
        border: 1px solid #aaa;
        border-radius: 4px;

        .select2-selection__rendered {
            color: #444;
            line-height: 28px;
        }

        .select2-selection__clear {
            cursor: pointer;
            float: right;
            font-weight: bold;
            height: 26px;
            margin-right: 20px;
            padding-right: 0px;
        }

        .select2-selection__placeholder {
            color: #999;
        }

        .select2-selection__arrow {
            height: 26px;

            position: absolute;

            top: 1px;
            right: 1px;

            width: 20px;

            b {
                border-color: #888 transparent transparent transparent;
                border-style: solid;
                border-width: 5px 4px 0 4px;

                height: 0;
                left: 50%;

                margin-left: -4px;
                margin-top: -2px;

                position: absolute;

                top: 50%;
                width: 0;
            }
        }
    }

    &[dir="rtl"] {
        .select2-selection--single {
            .select2-selection__clear {
                float: left;
            }

            .select2-selection__arrow {
                left: 1px;
                right: auto;
            }
        }
    }

    &.select2-container--disabled {
        .select2-selection--single {
            background-color: #eee;
            cursor: default;

            .select2-selection__clear {
                display: none;
            }
        }
    }

    &.select2-container--open {
        .select2-selection--single {
            .select2-selection__arrow {
                b {
                    border-color: transparent transparent #888 transparent;
                    border-width: 0 4px 5px 4px;
                }
            }
        }
    }

    .select2-selection--multiple {
        background-color: white;
        border: 1px solid #aaa;
        border-radius: 4px;
        cursor: text;
        padding-bottom: 5px;
        padding-right: 5px;
        position: relative;

        &.select2-selection--clearable {
            padding-right: 25px;
        }

        .select2-selection__clear {
            cursor: pointer;
            font-weight: bold;
            height: 20px;
            margin-right: 10px;
            margin-top: 5px;

            position: absolute;
            right: 0;

            // This padding is to account for the bottom border for the first
            // selection row and the top border of the second selection row.
            // Without it, selections on the first row may be offset incorrectly
            // and appear in their own row instead of going to the second row
            padding: 1px;
        }

        .select2-selection__choice {
            background-color: #e4e4e4;
            border: 1px solid #aaa;
            border-radius: 4px;
            box-sizing: border-box;

            display: inline-block;
            margin-left: 5px;
            margin-top: 5px;
            padding: 0;
            padding-left: 20px;

            position: relative;

            max-width: 100%;
            overflow: hidden;
            text-overflow: ellipsis;
            vertical-align: bottom;
            white-space: nowrap;
        }

        .select2-selection__choice__display {
            cursor: default;

            padding-left: 2px;
            padding-right: 5px;
        }

        .select2-selection__choice__remove {
            background-color: transparent;
            border: none;
            border-right: 1px solid #aaa;
            border-top-left-radius: 4px;
            border-bottom-left-radius: 4px;

            color: #999;
            cursor: pointer;

            font-size: 1em;
            font-weight: bold;

            padding: 0 4px;

            position: absolute;
            left: 0;
            top: 0;

            &:hover, &:focus {
                background-color: #f1f1f1;
                color: #333;
                outline: none;
            }
        }
    }

    &[dir="rtl"] {
        .select2-selection--multiple {
            .select2-selection__choice {
                margin-left: 5px;
                margin-right: auto;
            }

            .select2-selection__choice__display {
                padding-left: 5px;
                padding-right: 2px;
            }

            .select2-selection__choice__remove {
                border-left: 1px solid #aaa;
                border-right: none;
                border-radius: 0 4px 4px 0;
            }

            .select2-selection__clear {
                float: left;
                margin-left: 10px;
                margin-right: auto;
            }
        }
    }

    &.select2-container--focus {
        .select2-selection--multiple {
            border: solid black 1px;
            outline: 0;
        }
    }

    &.select2-container--disabled {
        .select2-selection--multiple {
            background-color: #eee;
            cursor: default;
        }

        .select2-selection__choice__remove {
            display: none;
        }
    }

    &.select2-container--open.select2-container--above {
        .select2-selection--single, .select2-selection--multiple {
            border-top-left-radius: 0;
            border-top-right-radius: 0;
        }
    }

    &.select2-container--open.select2-container--below {
        .select2-selection--single, .select2-selection--multiple {
            border-bottom-left-radius: 0;
            border-bottom-right-radius: 0;
        }
    }

    .select2-search--dropdown {
        .select2-search__field {
            border: 1px solid #aaa;
        }
    }

    .select2-search--inline {
        .select2-search__field {
            background: transparent;
            border: none;
            outline: 0;
            box-shadow: none;
            -webkit-appearance: textfield;
        }
    }

    .select2-results > .select2-results__options {
        max-height: 200px;
        overflow-y: auto;
    }

    .select2-results__option {
        .select2-results__option {
            padding-left: 1em;

            .select2-results__group {
                padding-left: 0;
            }

            .select2-results__option {
                margin-left: -1em;
                padding-left: 2em;

                .select2-results__option {
                    margin-left: -2em;
                    padding-left: 3em;

                    .select2-results__option {
                        margin-left: -3em;
                        padding-left: 4em;

                        .select2-results__option {
                            margin-left: -4em;
                            padding-left: 5em;

                            .select2-results__option {
                                margin-left: -5em;
                                padding-left: 6em;
                            }
                        }
                    }
                }
            }
        }
    }

    .select2-results__option--group {
        padding: 0;
    }

    .select2-results__option--disabled {
        color: #999;
    }

    .select2-results__option--selected {
        background-color: #ddd;
    }

    .select2-results__option--highlighted.select2-results__option--selectable {
        background-color: #5897fb;
        color: white;
    }

    .select2-results__group {
        cursor: default;
        display: block;
        padding: 6px;
    }
}
