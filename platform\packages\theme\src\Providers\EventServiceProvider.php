<?php

namespace Bo<PERSON><PERSON>\Theme\Providers;

use Bo<PERSON>ble\Base\Events\FormRendering;
use Bo<PERSON>ble\Base\Events\SeederPrepared;
use Bo<PERSON>ble\Base\Events\SystemUpdateDBMigrated;
use Bo<PERSON>ble\Base\Events\SystemUpdatePublished;
use Bo<PERSON>ble\Theme\Listeners\AddFormJsValidation;
use Bo<PERSON>ble\Theme\Listeners\CoreUpdateThemeDB;
use Botble\Theme\Listeners\PublishThemeAssets;
use Botble\Theme\Listeners\SetDefaultTheme;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;

class EventServiceProvider extends ServiceProvider
{
    protected $listen = [
        SystemUpdateDBMigrated::class => [
            CoreUpdateThemeDB::class,
        ],
        SystemUpdatePublished::class => [
            PublishThemeAssets::class,
        ],
        SeederPrepared::class => [
            SetDefaultTheme::class,
        ],
        FormRendering::class => [
            AddFormJsValidation::class,
        ],
    ];
}
