<?php

namespace Botble\Hotel\Services;

use Botble\Hotel\Models\Room;
use Botble\Hotel\Models\RoomPrice;
use Botble\Hotel\Models\RoomCategory;
use Carbon\Carbon;
use Illuminate\Support\Collection;

class SeasonalPricingService
{
    /**
     * Create seasonal pricing for all rooms or specific category
     */
    public function createSeasonalPricing(array $data): array
    {
        $results = [
            'success' => 0,
            'failed' => 0,
            'errors' => [],
            'created_prices' => []
        ];

        try {
            // Convert dates to Carbon instances for validation
            $startDate = Carbon::parse($data['start_date']);
            $endDate = Carbon::parse($data['end_date']);

            // Additional validation
            if ($startDate->isPast()) {
                $results['errors'][] = "Start date cannot be in the past";
                return $results;
            }

            if ($endDate->isBefore($startDate)) {
                $results['errors'][] = "End date must be after start date";
                return $results;
            }

            $rooms = $this->getRoomsForPricing($data);
            
            if ($rooms->isEmpty()) {
                $results['errors'][] = "No rooms found matching the selected criteria";
                return $results;
            }

            \DB::beginTransaction();
            
            foreach ($rooms as $room) {
                try {
                    // Validate the price calculation before creating
                    $priceData = $this->preparePriceData($room, $data);
                    
                    // Additional validation for the calculated price
                    if ($priceData['price'] <= 0) {
                        throw new \Exception("Invalid price calculation for room {$room->name}");
                    }

                    $roomPrice = RoomPrice::create($priceData);
                    
                    $results['created_prices'][] = $roomPrice;
                    $results['success']++;
                } catch (\Exception $e) {
                    $results['failed']++;
                    $results['errors'][] = "Room {$room->name}: " . $e->getMessage();
                    \DB::rollBack();
                    return $results;
                }
            }

            \DB::commit();
        } catch (\Exception $e) {
            \DB::rollBack();
            $results['errors'][] = "System error: " . $e->getMessage();
        }

        return $results;
    }

    /**
     * Get rooms based on pricing scope
     */
    protected function getRoomsForPricing(array $data): Collection
    {
        $scope = $data['pricing_scope'] ?? 'all_rooms';
        
        switch ($scope) {
            case 'specific_room':
                return Room::where('id', $data['room_id'])->get();
                
            case 'room_category':
                return Room::where('room_category_id', $data['room_category_id'])->get();
                
            case 'all_rooms':
            default:
                return Room::where('status', 'published')->get();
        }
    }

    /**
     * Prepare price data for room
     */
    protected function preparePriceData(Room $room, array $data): array
    {
        $basePrice = $room->price;

        if ($basePrice <= 0) {
            throw new \Exception("Invalid base price for room");
        }

        // Map the form field names to internal names
        $data['adjustment_type'] = $data['adjustment_type'] ?? $data['pricing_type'] ?? 'fixed_price';
        $data['adjustment_value'] = $data['adjustment_value'] ?? $data['value'] ?? 0;

        $finalPrice = $this->calculateSeasonalPrice($basePrice, $data);

        // Validate final price
        if ($finalPrice <= 0) {
            throw new \Exception("Invalid final price calculation");
        }

        // Map adjustment_type to pricing_type for database storage
        $pricingType = $this->mapAdjustmentTypeToPricingType($data['adjustment_type']);

        $priceData = [
            'room_id' => $room->id,
            'pricing_category' => 'individual_room',
            'name' => $data['season_name'] ?? $this->generateSeasonName($data),
            'description' => $data['description'] ?? '',
            'price' => $finalPrice,
            'start_date' => $data['start_date'],
            'end_date' => $data['end_date'],
            'priority' => $data['priority'] ?? 10,
            'is_active' => true,
            'pricing_type' => $pricingType,
            'adjustment_value' => $data['adjustment_value'],
        ];

        // Add optional fields only if the columns exist
        if (\Schema::hasColumn('ht_room_prices', 'season_type')) {
            $priceData['season_type'] = $data['season_type'] ?? 'festival';
        }

        if (\Schema::hasColumn('ht_room_prices', 'is_future_pricing')) {
            $priceData['is_future_pricing'] = Carbon::parse($data['start_date'])->isFuture();
        }

        if (\Schema::hasColumn('ht_room_prices', 'price_reason')) {
            $priceData['price_reason'] = $data['price_reason'] ?? $this->generatePriceReason($data);
        }

        if (\Schema::hasColumn('ht_room_prices', 'customer_display_message')) {
            $priceData['customer_display_message'] = $data['customer_message'] ?? $this->generateCustomerMessage($data, $basePrice, $finalPrice);
        }

        if (\Schema::hasColumn('ht_room_prices', 'approval_status')) {
            $priceData['approval_status'] = RoomPrice::APPROVAL_APPROVED;
        }

        if (\Schema::hasColumn('ht_room_prices', 'approved_by')) {
            $priceData['approved_by'] = auth()->id();
        }

        if (\Schema::hasColumn('ht_room_prices', 'approved_at')) {
            $priceData['approved_at'] = now();
        }

        return $priceData;
    }

    /**
     * Calculate the final price based on adjustment type and value
     */
    protected function calculateSeasonalPrice(float $basePrice, array $data): float
    {
        $adjustmentType = $data['adjustment_type'] ?? $data['pricing_type'] ?? 'fixed_price';
        $adjustmentValue = (float) ($data['adjustment_value'] ?? $data['value'] ?? 0);

        if ($adjustmentValue < 0) {
            throw new \Exception("Adjustment value cannot be negative");
        }

        switch ($adjustmentType) {
            case 'percentage':
            case 'percent':
                if ($adjustmentValue > 100) {
                    throw new \Exception("Percentage adjustment cannot exceed 100%");
                }
                return $basePrice * (1 + ($adjustmentValue / 100));

            case 'fixed_amount':
            case 'Fixed Amount Increase':
            case 'amount_adjust':
                return $basePrice + $adjustmentValue;

            case 'fixed_price':
            case 'Fixed Price':
                if ($adjustmentValue === 0) {
                    throw new \Exception("Fixed price must be specified");
                }
                return $adjustmentValue;

            default:
                throw new \Exception("Invalid adjustment type: " . $adjustmentType);
        }
    }

    /**
     * Map adjustment type to pricing type
     */
    protected function mapAdjustmentTypeToPricingType(string $adjustmentType): string
    {
        switch ($adjustmentType) {
            case 'percentage':
            case 'percent':
                return 'percent';
            case 'fixed_amount':
            case 'Fixed Amount Increase':
            case 'amount_adjust':
                return 'amount_adjust';
            case 'fixed_price':
            case 'Fixed Price':
                return 'fixed';
            default:
                return 'fixed';
        }
    }

    /**
     * Generate season name if not provided
     */
    protected function generateSeasonName(array $data): string
    {
        $seasonTypes = RoomPrice::getSeasonTypes();
        $seasonName = $seasonTypes[$data['season_type']] ?? 'Special Season';
        
        $startDate = Carbon::parse($data['start_date'])->format('M j');
        $endDate = Carbon::parse($data['end_date'])->format('M j');
        
        return "{$seasonName} ({$startDate} - {$endDate})";
    }

    /**
     * Generate price reason
     */
    protected function generatePriceReason(array $data): string
    {
        $seasonTypes = RoomPrice::getSeasonTypes();
        $seasonName = $seasonTypes[$data['season_type']] ?? 'Special Season';
        
        return "Increased pricing due to {$seasonName}";
    }

    /**
     * Generate customer message
     */
    protected function generateCustomerMessage(array $data, float $basePrice, float $finalPrice): string
    {
        $seasonTypes = RoomPrice::getSeasonTypes();
        $seasonName = $seasonTypes[$data['season_type']] ?? 'Special Season';
        
        $increase = $finalPrice - $basePrice;
        $percentage = $basePrice > 0 ? round(($increase / $basePrice) * 100, 2) : 0;
        
        if ($data['customer_message'] ?? null) {
            return $data['customer_message'];
        }
        
        $message = "Special {$seasonName} pricing";
        if ($percentage > 0) {
            $message .= " (+{$percentage}%)";
        }
        $message .= " - Enhanced services and amenities included";
        
        return $message;
    }

    /**
     * Get future pricing for a room
     */
    public function getFuturePricing(Room $room, string $startDate, string $endDate): Collection
    {
        $query = RoomPrice::where('room_id', $room->id)
            ->futurePricing()
            ->forDateRange($startDate, $endDate)
            ->active();

        // Only add approval status filter if the column exists
        if (\Schema::hasColumn('ht_room_prices', 'approval_status')) {
            $query->approved();
        }

        return $query->orderBy('start_date')->get();
    }

    /**
     * Preview seasonal pricing impact
     */
    public function previewSeasonalPricing(array $data): array
    {
        $rooms = $this->getRoomsForPricing($data);
        $preview = [];

        foreach ($rooms as $room) {
            $basePrice = $room->price;
            $seasonalPrice = $this->calculateSeasonalPrice($basePrice, $data);
            $increase = $seasonalPrice - $basePrice;
            $percentage = $basePrice > 0 ? round(($increase / $basePrice) * 100, 2) : 0;

            $preview[] = [
                'room_id' => $room->id,
                'room_name' => $room->name,
                'base_price' => $basePrice,
                'seasonal_price' => $seasonalPrice,
                'price_increase' => $increase,
                'percentage_increase' => $percentage,
            ];
        }

        return $preview;
    }

    /**
     * Check for pricing conflicts
     */
    public function checkPricingConflicts(array $data): array
    {
        $rooms = $this->getRoomsForPricing($data);
        $conflicts = [];

        foreach ($rooms as $room) {
            try {
                // Build base query without any optional columns
                $query = \DB::table('ht_room_prices')
                    ->where('room_id', $room->id)
                    ->where(function ($q) use ($data) {
                        $q->where(function ($q) use ($data) {
                            $q->whereRaw('DATE(start_date) <= ?', [$data['end_date']])
                              ->whereRaw('DATE(end_date) >= ?', [$data['start_date']]);
                        });
                    });

                // Add is_active check if column exists
                if (\Schema::hasColumn('ht_room_prices', 'is_active')) {
                    $query->where('is_active', 1);
                }

                // Add approval status check if column exists
                if (\Schema::hasColumn('ht_room_prices', 'approval_status')) {
                    $query->where('approval_status', RoomPrice::APPROVAL_APPROVED);
                }

                $existingPrices = $query->get();

                if ($existingPrices->isNotEmpty()) {
                    $conflicts[] = [
                        'room_id' => $room->id,
                        'room_name' => $room->name,
                        'conflicting_prices' => collect($existingPrices)->map(function ($price) {
                            return [
                                'id' => $price->id,
                                'name' => $price->name ?? '',
                                'start_date' => Carbon::parse($price->start_date)->format('Y-m-d'),
                                'end_date' => Carbon::parse($price->end_date)->format('Y-m-d'),
                                'price' => $price->price ?? 0,
                            ];
                        })->toArray()
                    ];
                }
            } catch (\Exception $e) {
                \Log::error('Error checking pricing conflicts: ' . $e->getMessage());
            }
        }

        return $conflicts;
    }

    /**
     * Delete seasonal pricing
     */
    public function deleteSeasonalPricing(string $seasonType, string $startDate, string $endDate): int
    {
        return RoomPrice::seasonal($seasonType)
            ->forDateRange($startDate, $endDate)
            ->delete();
    }

    /**
     * Get seasonal pricing statistics
     */
    public function getSeasonalPricingStats(): array
    {
        $stats = [];
        $seasonTypes = RoomPrice::getSeasonTypes();

        try {
            foreach ($seasonTypes as $type => $name) {
                // Check if columns exist before querying
                if (\Schema::hasColumn('ht_room_prices', 'season_type')) {
                    $count = RoomPrice::seasonal($type)->active()->count();
                    $futureCount = RoomPrice::seasonal($type)->futurePricing()->active()->count();
                } else {
                    $count = 0;
                    $futureCount = 0;
                }

                $stats[$type] = [
                    'name' => $name,
                    'total_count' => $count,
                    'future_count' => $futureCount,
                    'active_count' => $count - $futureCount,
                ];
            }
        } catch (\Exception $e) {
            // Return empty stats if database error
            foreach ($seasonTypes as $type => $name) {
                $stats[$type] = [
                    'name' => $name,
                    'total_count' => 0,
                    'future_count' => 0,
                    'active_count' => 0,
                ];
            }
        }

        return $stats;
    }
}
