<?php

namespace Botble\Hotel\Http\Controllers;

use Botble\Base\Facades\Assets;
use Botble\Base\Facades\JavaScript;
use Botble\Base\Http\Controllers\BaseController;
use Botble\Hotel\Facades\HotelHelper;
use Botble\Hotel\Models\Booking;
use Botble\Hotel\Models\Customer;
use Botble\Hotel\Models\Room;
use Botble\Hotel\Enums\BookingStatusEnum;
use Botble\Payment\Enums\PaymentStatusEnum;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Response;
use Maatwebsite\Excel\Facades\Excel;
use Botble\Hotel\Exports\BookingExport;

class AdvancedBookingReportController extends BaseController
{
    public function index(Request $request)
    {
        try {
            $this->pageTitle('Advanced Booking Reports');

            Assets::addScriptsDirectly([
                'vendor/core/plugins/hotel/libraries/daterangepicker/daterangepicker.js',
                'vendor/core/plugins/hotel/js/report.js',
                'vendor/core/plugins/hotel/js/advanced-reports.js',
            ])
                ->addStylesDirectly([
                    'vendor/core/plugins/hotel/libraries/daterangepicker/daterangepicker.css',
                    'vendor/core/plugins/hotel/css/report.css',
                    'vendor/core/plugins/hotel/css/booking-reports.css',
                ])
                ->addScripts(['moment', 'bootstrap', 'apexcharts']);

            // Map start_date and end_date to date_from and date_to for HotelHelper
            if ($request->has('start_date') && !$request->has('date_from')) {
                $request->merge(['date_from' => $request->input('start_date')]);
            }

            if ($request->has('end_date') && !$request->has('date_to')) {
                $request->merge(['date_to' => $request->input('end_date')]);
            }

            [$startDate, $endDate] = HotelHelper::getDateRangeInReport($request);

            // Get booking statistics
            $totalBookings = Booking::query()
                ->whereDate('created_at', '>=', $startDate)
                ->whereDate('created_at', '<=', $endDate)
                ->count();

            $totalCustomers = Customer::query()
                ->whereDate('created_at', '>=', $startDate)
                ->whereDate('created_at', '<=', $endDate)
                ->count();

            $totalRooms = Room::query()->count();

            // Get revenue data
            if (is_plugin_active('payment')) {
                $revenue = Booking::query()
                    ->select([
                        DB::raw('SUM(COALESCE(payments.amount, 0) - COALESCE(payments.refunded_amount, 0)) as revenue'),
                    ])
                    ->join('payments', 'payments.id', '=', 'ht_bookings.payment_id')
                    ->whereIn('payments.status', [PaymentStatusEnum::COMPLETED])
                    ->whereDate('payments.created_at', '>=', $startDate)
                    ->whereDate('payments.created_at', '<=', $endDate)
                    ->first();
            } else {
                $revenue = Booking::query()
                    ->select([
                        DB::raw('SUM(COALESCE(amount, 0)) as revenue'),
                    ])
                    ->where('status', BookingStatusEnum::COMPLETED)
                    ->whereDate('created_at', '>=', $startDate)
                    ->whereDate('created_at', '<=', $endDate)
                    ->first();
            }

            // Get booking status statistics
            $bookingStatuses = BookingStatusEnum::labels();
            $bookingStatusStats = [];
            $statusColors = [
                BookingStatusEnum::PENDING => '#fbbf24',    // Amber
                BookingStatusEnum::PROCESSING => '#3b82f6', // Blue
                BookingStatusEnum::COMPLETED => '#10b981',  // Green
                BookingStatusEnum::CANCELLED => '#ef4444',  // Red
            ];

            foreach ($bookingStatuses as $status => $label) {
                $count = Booking::query()
                    ->where('status', $status)
                    ->whereDate('created_at', '>=', $startDate)
                    ->whereDate('created_at', '<=', $endDate)
                    ->count();

                $bookingStatusStats[$status] = [
                    'label' => $label,
                    'count' => $count,
                    'color' => $statusColors[$status] ?? '#6b7280', // Default gray if status not found
                ];
            }

            // Get monthly booking data for chart
            $monthlyBookings = Booking::query()
                ->select(DB::raw('COUNT(*) as count'), DB::raw('MONTH(created_at) as month'))
                ->whereYear('created_at', Carbon::now()->year)
                ->groupBy('month')
                ->orderBy('month')
                ->get()
                ->pluck('count', 'month')
                ->toArray();

            $chartData = [];
            for ($i = 1; $i <= 12; $i++) {
                $chartData[] = $monthlyBookings[$i] ?? 0;
            }

            // Get monthly revenue data for chart
            if (is_plugin_active('payment')) {
                $monthlyRevenue = Booking::query()
                    ->select([
                        DB::raw('SUM(COALESCE(payments.amount, 0) - COALESCE(payments.refunded_amount, 0)) as revenue'),
                        DB::raw('MONTH(payments.created_at) as month')
                    ])
                    ->join('payments', 'payments.id', '=', 'ht_bookings.payment_id')
                    ->whereIn('payments.status', [PaymentStatusEnum::COMPLETED])
                    ->whereYear('payments.created_at', Carbon::now()->year)
                    ->groupBy('month')
                    ->orderBy('month')
                    ->get()
                    ->pluck('revenue', 'month')
                    ->toArray();
            } else {
                $monthlyRevenue = Booking::query()
                    ->select([
                        DB::raw('SUM(COALESCE(amount, 0)) as revenue'),
                        DB::raw('MONTH(created_at) as month')
                    ])
                    ->where('status', BookingStatusEnum::COMPLETED)
                    ->whereYear('created_at', Carbon::now()->year)
                    ->groupBy('month')
                    ->orderBy('month')
                    ->get()
                    ->pluck('revenue', 'month')
                    ->toArray();
            }

            $revenueChartData = [];
            for ($i = 1; $i <= 12; $i++) {
                $revenueChartData[] = $monthlyRevenue[$i] ?? 0;
            }

            // Get room availability data
            $roomAvailability = $this->getRoomAvailabilityData();

            // Prepare chart data for JavaScript
            $months = [
                'January', 'February', 'March', 'April', 'May', 'June',
                'July', 'August', 'September', 'October', 'November', 'December'
            ];

            $bookingTrendLabels = [];
            foreach ($months as $index => $month) {
                $bookingTrendLabels[] = $month;
            }

            // Prepare booking status data for chart
            $bookingStatusLabels = [];
            $bookingStatusData = [];
            foreach ($bookingStatusStats as $status) {
                $bookingStatusLabels[] = $status['label'];
                $bookingStatusData[] = $status['count'];
            }

            // Prepare room availability data for chart
            $roomAvailabilityLabels = ['Available', 'Booked'];
            $roomAvailabilityData = [$roomAvailability['available'], $roomAvailability['booked']];

            // Add chart data to JavaScript
            JavaScript::put([
                'bookingTrendLabels' => $bookingTrendLabels,
                'bookingTrendData' => $chartData,
                'revenueTrendLabels' => $bookingTrendLabels,
                'revenueTrendData' => $revenueChartData,
                'roomAvailabilityLabels' => $roomAvailabilityLabels,
                'roomAvailabilityData' => $roomAvailabilityData,
                'bookingStatusLabels' => $bookingStatusLabels,
                'bookingStatusData' => $bookingStatusData,
                'currencyFormat' => get_application_currency()->symbol . ':price',
            ]);

            return view(
                'plugins/hotel::reports.advanced',
                compact(
                    'startDate',
                    'endDate',
                    'totalBookings',
                    'totalCustomers',
                    'totalRooms',
                    'revenue',
                    'bookingStatusStats',
                    'chartData',
                    'revenueChartData',
                    'roomAvailability'
                )
            );
        } catch (\Exception $e) {
            // Log the error
            \Log::error('Error in advanced booking report: ' . $e->getMessage());
            \Log::error($e->getTraceAsString());

            // Return a simple view with an error message
            return view('plugins/hotel::reports.error', [
                'message' => 'An error occurred while loading the advanced booking report.',
                'error' => $e->getMessage()
            ]);
        }
    }

    public function export(Request $request)
    {
        $this->pageTitle('Export Booking Reports');

        try {
            // Get date range from request
            $startDate = null;
            $endDate = null;

            // Check for different parameter names that might be used
            if ($request->has('date_from')) {
                $startDate = Carbon::parse($request->input('date_from'));
            } elseif ($request->has('start_date')) {
                $startDate = Carbon::parse($request->input('start_date'));
            }

            if ($request->has('date_to')) {
                $endDate = Carbon::parse($request->input('date_to'));
            } elseif ($request->has('end_date')) {
                $endDate = Carbon::parse($request->input('end_date'));
            }

            // If dates are not provided, use default date range
            if (!$startDate || !$endDate) {
                [$startDate, $endDate] = HotelHelper::getDateRangeInReport($request);
            }

            // Create the export file
            $format = $request->input('format', 'xlsx');
            $filename = 'booking_reports_' . Carbon::now()->format('Y-m-d');

            if ($format === 'pdf') {
                return Excel::download(
                    new BookingExport($startDate, $endDate),
                    $filename . '.pdf',
                    \Maatwebsite\Excel\Excel::DOMPDF
                );
            }

            return Excel::download(
                new BookingExport($startDate, $endDate),
                $filename . '.xlsx'
            );
        } catch (\Exception $e) {
            // Log the error for debugging
            \Log::error('Export error: ' . $e->getMessage());
            \Log::error($e->getTraceAsString());

            return redirect()->back()->with('error', 'Error exporting data: ' . $e->getMessage());
        }
    }

    /**
     * Get room availability data
     *
     * @return array
     */
    protected function getRoomAvailabilityData()
    {
        $totalRooms = Room::query()->count();
        $bookedRooms = Booking::query()
            ->where('status', BookingStatusEnum::PROCESSING)
            ->count();

        return [
            'available' => $totalRooms - $bookedRooms,
            'booked' => $bookedRooms,
            'total' => $totalRooms,
        ];
    }

    /**
     * API endpoint for chart data
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getChartData(Request $request)
    {
        try {
            // Get date range from request
            $startDate = null;
            $endDate = null;

            if ($request->has('start_date')) {
                $startDate = Carbon::parse($request->input('start_date'));
            }

            if ($request->has('end_date')) {
                $endDate = Carbon::parse($request->input('end_date'));
            }

            // If dates are not provided, use default date range
            if (!$startDate || !$endDate) {
                [$startDate, $endDate] = HotelHelper::getDateRangeInReport($request);
            }

            // Get monthly booking data for chart
            $monthlyBookings = Booking::query()
                ->select(DB::raw('COUNT(*) as count'), DB::raw('MONTH(created_at) as month'))
                ->whereDate('created_at', '>=', $startDate)
                ->whereDate('created_at', '<=', $endDate)
                ->groupBy('month')
                ->orderBy('month')
                ->get()
                ->pluck('count', 'month')
                ->toArray();

            $chartData = [];
            $months = [
                'January', 'February', 'March', 'April', 'May', 'June',
                'July', 'August', 'September', 'October', 'November', 'December'
            ];

            // Fill in missing months with 0
            for ($i = 1; $i <= 12; $i++) {
                $chartData[] = $monthlyBookings[$i] ?? 0;
            }

            // Get monthly revenue data for chart
            if (is_plugin_active('payment')) {
                $monthlyRevenue = Booking::query()
                    ->select([
                        DB::raw('SUM(COALESCE(payments.amount, 0) - COALESCE(payments.refunded_amount, 0)) as revenue'),
                        DB::raw('MONTH(payments.created_at) as month')
                    ])
                    ->join('payments', 'payments.id', '=', 'ht_bookings.payment_id')
                    ->whereIn('payments.status', [PaymentStatusEnum::COMPLETED])
                    ->whereDate('payments.created_at', '>=', $startDate)
                    ->whereDate('payments.created_at', '<=', $endDate)
                    ->groupBy('month')
                    ->orderBy('month')
                    ->get()
                    ->pluck('revenue', 'month')
                    ->toArray();
            } else {
                $monthlyRevenue = Booking::query()
                    ->select([
                        DB::raw('SUM(COALESCE(amount, 0)) as revenue'),
                        DB::raw('MONTH(created_at) as month')
                    ])
                    ->where('status', BookingStatusEnum::COMPLETED)
                    ->whereDate('created_at', '>=', $startDate)
                    ->whereDate('created_at', '<=', $endDate)
                    ->groupBy('month')
                    ->orderBy('month')
                    ->get()
                    ->pluck('revenue', 'month')
                    ->toArray();
            }

            $revenueChartData = [];
            // Fill in missing months with 0
            for ($i = 1; $i <= 12; $i++) {
                $revenueChartData[] = $monthlyRevenue[$i] ?? 0;
            }

            return response()->json([
                'bookingTrend' => [
                    'labels' => $months,
                    'values' => $chartData,
                ],
                'revenueTrend' => [
                    'labels' => $months,
                    'values' => $revenueChartData,
                ],
            ]);
        } catch (\Exception $e) {
            // Log the error
            \Log::error('Error in getChartData: ' . $e->getMessage());
            \Log::error($e->getTraceAsString());

            // Return a simple error response
            return response()->json([
                'error' => 'An error occurred while fetching chart data.',
                'message' => $e->getMessage(),
            ], 500);
        }
    }
}
