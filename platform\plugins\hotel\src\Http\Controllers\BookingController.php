<?php

namespace Botble\Hotel\Http\Controllers;

use Botble\Base\Http\Actions\DeleteResourceAction;
use Botble\Base\Http\Controllers\BaseController;
use Botble\Hotel\Events\BookingStatusChanged;
use Botble\Hotel\Events\BookingUpdated;
use Botble\Hotel\Forms\BookingForm;
use Botble\Hotel\Http\Requests\UpdateBookingRequest;
use Botble\Hotel\Models\Booking;
use Botble\Hotel\Models\RoomDate;
use Botble\Hotel\Models\RoomInventory;
use Botble\Hotel\Services\RoomGroupService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Botble\Hotel\Tables\BookingTable;

class BookingController extends BaseController
{
    public function __construct()
    {
        $this
            ->breadcrumb()
            ->add(trans('plugins/hotel::booking.name'), route('booking.index'));
    }

    public function index(BookingTable $table)
    {
        $this->pageTitle(trans('plugins/hotel::booking.name'));

        return $table->renderTable();
    }

    public function edit(Booking $booking)
    {
        try {
            $this->pageTitle(trans('core/base::forms.edit_item', ['name' => 'Booking #' . $booking->id]));

            return BookingForm::createFromModel($booking)->renderForm();
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Error in edit method: ' . $e->getMessage());

            return redirect()->route('booking.index')
                ->with('error', 'Error loading booking: ' . $e->getMessage());
        }
    }

    public function update(Booking $booking, UpdateBookingRequest $request, RoomGroupService $roomGroupService)
    {
        $oldStatus = $booking->status->getValue();
        $newStatus = $request->input('status');
        $roomIdCode = $request->input('room_id_code');
        $originalRoomIdCode = $booking->room ? $booking->room->room_id_code : $booking->room_id_code;

        // Begin transaction to ensure all database operations are atomic
        DB::beginTransaction();

        try {
            // Log the request data for debugging
            \Illuminate\Support\Facades\Log::info("Updating booking #{$booking->id} with data: " . json_encode($request->all()));

            // If room is being changed, free up the original room first
            if ($roomIdCode && $roomIdCode !== $originalRoomIdCode && $originalRoomIdCode && $originalRoomIdCode !== 'Select Room ID') {
                \Illuminate\Support\Facades\Log::info("Freeing up original room {$originalRoomIdCode} for booking #{$booking->id}");
                
                // Free up the original room in RoomInventory
                RoomInventory::where('room_id_code', $originalRoomIdCode)
                    ->update(['is_available' => true]);
                
                // Free up original room dates
                RoomDate::where('booking_id', $booking->id)
                    ->where('room_id_code', $originalRoomIdCode)
                    ->update([
                        'is_booked' => false,
                        'active' => false
                    ]);

                // If original room was part of a group, handle group availability
                if ($roomGroupService->isRoomInGroup($originalRoomIdCode)) {
                    $roomGroupService->updateGroupAvailability($originalRoomIdCode, true);
                }
            }

            // Check if the new room is available
            if ($roomIdCode && $roomIdCode !== $booking->room_id_code && $roomIdCode !== 'Select Room ID') {
                // Special handling for 2BHK package
                if ($roomIdCode === '2BHK') {
                    // For 2BHK package, we don't need to check availability
                    // Just mark all rooms in the group as unavailable
                    try {
                        if (Schema::hasTable('ht_room_groups')) {
                            $roomGroupService->updateGroupAvailability('MRR-999', false);
                            $roomGroupService->updateGroupAvailability('MRR-777', false);
                        }
                    } catch (\Exception $e) {
                        \Illuminate\Support\Facades\Log::error('Error updating 2BHK availability: ' . $e->getMessage());
                        // Continue with the booking even if there's an error
                    }
                } else {
                    // Regular room availability check
                    try {
                        // First check if the room exists in inventory
                        $roomExists = RoomInventory::where('room_id_code', $roomIdCode)->exists();

                        if (!$roomExists) {
                            // If room doesn't exist in inventory, create it
                            $room = $booking->room;
                            RoomInventory::create([
                                'room_id' => $room ? $room->room_id : 1,
                                'room_id_code' => $roomIdCode,
                                'is_available' => true,
                            ]);

                            \Illuminate\Support\Facades\Log::info("Created room inventory for {$roomIdCode}");
                        } else {
                            // Check if the room is available
                            $isAvailable = RoomInventory::where('room_id_code', $roomIdCode)
                                ->where('is_available', 1)
                                ->exists();

                            if (!$isAvailable) {
                                DB::rollBack();
                                return $this
                                    ->httpResponse()
                                    ->setError()
                                    ->setMessage('The selected room is not available. Please choose another room.');
                            }
                        }

                        // If this is a room in a group, mark all rooms in the group as unavailable
                        if (Schema::hasTable('ht_room_groups') && $roomGroupService->isRoomInGroup($roomIdCode)) {
                            $roomGroupService->updateGroupAvailability($roomIdCode, false);
                        }
                    } catch (\Exception $e) {
                        \Illuminate\Support\Facades\Log::error('Error checking room availability: ' . $e->getMessage());
                        // Continue with the booking even if there's an error
                    }
                }
            }

            // Save the booking
            try {
                BookingForm::createFromModel($booking)
                    ->setRequest($request)
                    ->save();

                \Illuminate\Support\Facades\Log::info("Booking #{$booking->id} saved successfully");
            } catch (\Exception $e) {
                \Illuminate\Support\Facades\Log::error('Error saving booking: ' . $e->getMessage());
                throw $e; // Re-throw to be caught by the outer try-catch
            }

            // Update the BookingRoom model with the same room_id_code (only if it's a real room ID)
            if ($roomIdCode && $roomIdCode !== 'Select Room ID' && $booking->room && $roomIdCode !== $booking->room->room_id_code) {
                try {
                    // Update using DB facade to avoid model validation issues
                    DB::table('ht_booking_rooms')
                        ->where('id', $booking->room->id)
                        ->update([
                            'room_id_code' => $roomIdCode,
                            'updated_at' => now(),
                        ]);

                    \Illuminate\Support\Facades\Log::info("Updated booking room #{$booking->room->id} with room_id_code {$roomIdCode}");
                } catch (\Exception $e) {
                    \Illuminate\Support\Facades\Log::error('Error updating booking room: ' . $e->getMessage());
                    // Continue with the booking even if there's an error updating the room_id_code
                }
            }

            // Dispatch BookingUpdated event
            try {
                BookingUpdated::dispatch($booking);
                \Illuminate\Support\Facades\Log::info("BookingUpdated event dispatched for booking #{$booking->id}");
            } catch (\Exception $e) {
                \Illuminate\Support\Facades\Log::error('Error dispatching BookingUpdated event: ' . $e->getMessage());
                // Continue with the booking even if there's an error dispatching the event
            }

            // Handle status changes
            if ($newStatus !== $oldStatus) {
                try {
                    // If status is changing to Processing, set check_in_time
                    if ($newStatus === 'processing' && $oldStatus !== 'processing') {
                        $booking->check_in_time = now();
                        $booking->save();
                        \Illuminate\Support\Facades\Log::info("Setting check_in_time for booking #{$booking->id} to " . $booking->check_in_time);
                    }

                    // If status is changing to Complete, set check_out_time and free up the room
                    if ($newStatus === 'completed' && $oldStatus !== 'completed') {
                        $booking->check_out_time = now();
                        $booking->save();
                        \Illuminate\Support\Facades\Log::info("Setting check_out_time for booking #{$booking->id} to " . $booking->check_out_time);

                        // Free up the room in the inventory
                        $this->freeUpRoom($booking);

                        // Double-check that all rooms are freed
                        try {
                            // Update all room inventory records for this booking to be available
                            if ($booking->room && $booking->room->room_id_code && $booking->room->room_id_code !== 'Select Room ID') {
                                RoomInventory::where('room_id_code', $booking->room->room_id_code)
                                    ->update(['is_available' => true]);
                            }

                            // If booking has a room_id_code directly, make sure it's freed too
                            if ($booking->room_id_code && $booking->room_id_code !== 'Select Room ID') {
                                RoomInventory::where('room_id_code', $booking->room_id_code)
                                    ->update(['is_available' => true]);
                            }
                        } catch (\Exception $e) {
                            \Illuminate\Support\Facades\Log::error("Error in double-check room freeing: " . $e->getMessage());
                        }

                        \Illuminate\Support\Facades\Log::info("Room freed up for booking #{$booking->id} due to status change to completed");
                    }

                    // Dispatch BookingStatusChanged event
                    BookingStatusChanged::dispatch($oldStatus, $booking);
                    \Illuminate\Support\Facades\Log::info("BookingStatusChanged event dispatched for booking #{$booking->id} from {$oldStatus} to {$newStatus}");
                } catch (\Exception $e) {
                    \Illuminate\Support\Facades\Log::error('Error handling status change: ' . $e->getMessage());
                    // Continue with the booking even if there's an error handling the status change
                }
            }

            // Commit the transaction
            DB::commit();
            \Illuminate\Support\Facades\Log::info("Transaction committed for booking #{$booking->id}");

            return $this
                ->httpResponse()
                ->setPreviousUrl(route('booking.index'))
                ->withUpdatedSuccessMessage();
        } catch (\Exception $e) {
            // Rollback the transaction on error
            DB::rollBack();
            \Illuminate\Support\Facades\Log::error('Error updating booking: ' . $e->getMessage() . "\n" . $e->getTraceAsString());

            return $this
                ->httpResponse()
                ->setError()
                ->setMessage('Error updating booking: ' . $e->getMessage());
        }
    }

    /**
     * Check if a room is available for a specific date range
     */
    public function checkRoomAvailability(Request $request)
    {
        try {
            $roomIdCode = $request->input('room_id_code');
            $startDate = $request->input('start_date');
            $endDate = $request->input('end_date');
            $bookingId = $request->input('booking_id');
            
            // Skip availability check if this is an existing booking being edited
            if (!empty($bookingId)) {
                return $this->httpResponse()
                    ->setData(['available' => true])
                    ->setMessage('The selected room is available for this existing booking.');
            }

            // Check if this is a 2BHK package code
            if ($roomIdCode === '2BHK') {
                $isAvailable = $roomGroupService->isGroupAvailable('2BHK');

                return $this->httpResponse()
                    ->setData(['available' => $isAvailable])
                    ->setMessage($isAvailable ? 'The 2BHK package is available.' : 'The 2BHK package is not available. Some rooms are already booked.');
            }

            // Check if this room is part of a group
            if ($roomGroupService->isRoomInGroup($roomIdCode)) {
                $group = $roomGroupService->getRoomGroup($roomIdCode);

                if ($group && $group->code === '2BHK') {
                    // For admin bookings of individual rooms in the 2BHK, we check only that specific room
                    $isAvailable = $roomGroupService->isRoomAvailable($roomIdCode);

                    return $this->httpResponse()
                        ->setData(['available' => $isAvailable, 'isGroupRoom' => true, 'groupCode' => $group->code])
                        ->setMessage($isAvailable ? 'The room is available.' : 'The room is not available. Please choose another room.');
                }
            }

            // Regular room availability check
            try {
                // First check if the room exists in inventory
                $roomInventory = RoomInventory::where('room_id_code', $roomIdCode)->first();

                if (!$roomInventory) {
                    // If room doesn't exist in inventory, consider it available
                    return $this->httpResponse()
                        ->setData(['available' => true])
                        ->setMessage('The selected room is available.');
                }

                // Check if the room is available for the specific date range
                $isAvailable = $roomInventory->isAvailableForDateRange($startDate, $endDate, $bookingId);

                return $this->httpResponse()
                    ->setData(['available' => $isAvailable])
                    ->setMessage($isAvailable ? 'The selected room is available.' : 'The selected room is not available for the selected dates. Please choose another room or different dates.');
            } catch (\Exception $e) {
                \Illuminate\Support\Facades\Log::error('Error checking room availability: ' . $e->getMessage());
                // If there's an error, consider the room available
                return $this->httpResponse()
                    ->setData(['available' => true])
                    ->setMessage('The selected room is available.');
            }
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Error checking room availability: ' . $e->getMessage());
            // If there's an error, consider the room available
            return $this->httpResponse()
                ->setData(['available' => true])
                ->setMessage('The selected room is available.');
        }
    }

    /**
     * Free up a room in the inventory when a booking is completed or cancelled
     */
    protected function freeUpRoom(Booking $booking): void
    {
        try {
            \Illuminate\Support\Facades\Log::info("Starting room freeing process for booking #{$booking->id}");

            // Get the booking room
            $bookingRoom = $booking->room;

            // Get the RoomGroupService
            $roomGroupService = app(RoomGroupService::class);

            // If no booking room or no valid room_id_code is set, there's nothing to update
            if (!$bookingRoom || !$bookingRoom->room_id_code || $bookingRoom->room_id_code === 'Select Room ID') {
                // Try using the booking's room_id_code as a fallback
                if (!$booking->room_id_code || $booking->room_id_code === 'Select Room ID') {
                    \Illuminate\Support\Facades\Log::info("No real room ID to free up for booking #{$booking->id}");

                    // Even though there's no specific room ID, let's make sure all room dates for this booking are freed
                    $this->freeAllRoomDatesForBooking($booking->id);
                    return;
                }

                $roomIdCode = $booking->room_id_code;
                $roomId = $bookingRoom ? $bookingRoom->room_id : null;
            } else {
                $roomIdCode = $bookingRoom->room_id_code;
                $roomId = $bookingRoom->room_id;
            }

            // Skip if the room ID is "Select Room ID" but still free any room dates
            if ($roomIdCode === 'Select Room ID') {
                \Illuminate\Support\Facades\Log::info("Booking #{$booking->id} has placeholder 'Select Room ID', freeing any associated room dates");
                $this->freeAllRoomDatesForBooking($booking->id);
                return;
            }

            // Special handling for 2BHK package
            if ($roomIdCode === '2BHK') {
                try {
                    if (Schema::hasTable('ht_room_groups')) {
                        // For 2BHK package, free up both rooms
                        $roomGroupService->updateGroupAvailability('MRR-999', true);
                        $roomGroupService->updateGroupAvailability('MRR-777', true);

                        // Update the room inventory for both rooms
                        RoomInventory::where('room_id_code', 'MRR-999')->update(['is_available' => true]);
                        RoomInventory::where('room_id_code', 'MRR-777')->update(['is_available' => true]);

                        // Also free any other rooms that might be associated with this booking
                        $this->freeAllRoomDatesForBooking($booking->id);

                        \Illuminate\Support\Facades\Log::info("2BHK package freed for booking #{$booking->id}");
                        return;
                    }
                } catch (\Exception $e) {
                    \Illuminate\Support\Facades\Log::error('Error freeing 2BHK package: ' . $e->getMessage());

                    // Even if there's an error, try to free all room dates
                    $this->freeAllRoomDatesForBooking($booking->id);
                }
            }

            // Update the room_dates table to mark this room as free
            $roomDates = RoomDate::where('booking_id', $booking->id)
                ->where('room_id_code', $roomIdCode);

            if ($roomId) {
                $roomDates->where('room_id', $roomId);
            }

            // Update the records to mark as not booked
            $updated = $roomDates->update([
                'is_booked' => false,
                'active' => false,
            ]);

            // Also update the RoomInventory table to mark the room as available
            $roomInventory = RoomInventory::where('room_id_code', $roomIdCode);

            if ($roomId) {
                $roomInventory->where('room_id', $roomId);
            }

            $inventoryUpdated = $roomInventory->update([
                'is_available' => true,
            ]);

            // Check if this room is part of a group and free up all rooms in the group
            $roomGroupService = app(RoomGroupService::class);
            if ($roomGroupService->isRoomInGroup($roomIdCode)) {
                $roomGroupService->updateGroupAvailability($roomIdCode, true);
                \Illuminate\Support\Facades\Log::info("Room group freed for room: {$roomIdCode}");
            }

            \Illuminate\Support\Facades\Log::info("Room freed: Booking #{$booking->id}, Room: {$roomIdCode}, RoomDate records updated: {$updated}, RoomInventory records updated: {$inventoryUpdated}");

            // As a final safety measure, free all room dates for this booking
            // This ensures that even if there was an issue with a specific room, all rooms are freed
            $this->freeAllRoomDatesForBooking($booking->id);
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error("Error freeing room: " . $e->getMessage());

            // Even if there's an error, try to free all room dates as a fallback
            try {
                $this->freeAllRoomDatesForBooking($booking->id);
            } catch (\Exception $innerException) {
                \Illuminate\Support\Facades\Log::error("Error in fallback room freeing: " . $innerException->getMessage());
            }
        }
    }

    /**
     * Free all room dates associated with a booking
     * This ensures all rooms are properly freed even if there's no specific room ID
     */
    protected function freeAllRoomDatesForBooking(int $bookingId): void
    {
        try {
            // Free all room dates for this booking
            $roomDates = RoomDate::where('booking_id', $bookingId);
            $roomDateCount = $roomDates->count();

            if ($roomDateCount > 0) {
                // Get all room IDs before updating
                $roomIdCodes = $roomDates->pluck('room_id_code')->unique()->toArray();

                // Update all room dates to not booked
                $updated = $roomDates->update([
                    'is_booked' => false,
                    'active' => false,
                ]);

                // Mark all associated rooms as available in inventory
                foreach ($roomIdCodes as $code) {
                    if ($code && $code !== 'Select Room ID') {
                        RoomInventory::where('room_id_code', $code)->update(['is_available' => true]);

                        // If room is part of a group, free up the group
                        $roomGroupService = app(RoomGroupService::class);
                        if ($roomGroupService->isRoomInGroup($code)) {
                            $roomGroupService->updateGroupAvailability($code, true);
                        }
                    }
                }

                \Illuminate\Support\Facades\Log::info("Freed all room dates for booking #{$bookingId}: {$updated} records updated, room IDs: " . implode(', ', $roomIdCodes));
            } else {
                \Illuminate\Support\Facades\Log::info("No room dates found for booking #{$bookingId}");
            }
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error("Error freeing all room dates for booking #{$bookingId}: " . $e->getMessage());
        }
    }

    public function destroy(Booking $booking)
    {
        return DeleteResourceAction::make($booking);
    }

    /**
     * Reset all rooms to available (for development purposes only)
     */
    public function resetAllRooms()
    {
        try {
            // Mark all rooms as available in the inventory
            $updated = RoomInventory::query()->update(['is_available' => true]);

            // Mark all room dates as not booked
            $datesUpdated = RoomDate::query()->update([
                'is_booked' => false,
                'active' => false,
            ]);

            // Reset all room groups if the table exists
            if (Schema::hasTable('ht_room_groups')) {
                $roomGroupService = app(RoomGroupService::class);

                // Reset 2BHK package rooms
                $roomGroupService->updateGroupAvailability('MRR-999', true);
                $roomGroupService->updateGroupAvailability('MRR-777', true);

                // Reset any other room groups
                if (method_exists($roomGroupService, 'resetAllGroups')) {
                    $roomGroupService->resetAllGroups();
                }
            }

            \Illuminate\Support\Facades\Log::info("DEVELOPMENT: Reset all rooms to available. Updated {$updated} room inventory records and {$datesUpdated} room date records.");

            return $this->httpResponse()
                ->setMessage("All rooms have been reset to available. Updated {$updated} room inventory records and {$datesUpdated} room date records.")
                ->setData(['updated' => $updated, 'dates_updated' => $datesUpdated]);
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error("Error resetting rooms: " . $e->getMessage());

            return $this->httpResponse()
                ->setError()
                ->setMessage('Error resetting rooms: ' . $e->getMessage());
        }
    }
}

