(()=>{"use strict";$((function(){$(document).on("change",".shortcode-tabs-quantity-select",(function(){var a=$(this),t=parseInt(a.val())||1,n=a.data("key");a.val(t);for(var e=a.closest(".shortcode-admin-config"),o=(e.find(".shortcode-template").first().clone().removeClass("shortcode-template"),1);o<=a.data("max");o++){var d=n?e.find("[data-tab-id=".concat(n,"_").concat(o,"]")):e.find("[data-tab-id=".concat(o,"]"));o<=t?d.is(":visible")||(d.slideDown(),d.find("[data-name]").map((function(a,t){return $(t).prop("name",$(t).data("name"))}))):(d.slideUp(),d.find("[name]").map((function(a,t){$(t).data("name",$(t).prop("name")),$(t).removeProp("name")})))}}))}))})();