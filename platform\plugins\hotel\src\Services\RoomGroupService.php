<?php

namespace Botble\Hotel\Services;

use Botble\Hotel\Models\RoomGroup;
use Botble\Hotel\Models\RoomInventory;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Schema;

class RoomGroupService
{
    /**
     * Check if a room is part of a group
     */
    public function isRoomInGroup(string $roomIdCode): bool
    {
        try {
            // Check if the table exists
            if (!Schema::hasTable('ht_room_group_items')) {
                return false;
            }

            return DB::table('ht_room_group_items')
                ->where('room_id_code', $roomIdCode)
                ->exists();
        } catch (\Exception $e) {
            Log::error('Error checking if room is in group: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Get the group that a room belongs to
     */
    public function getRoomGroup(string $roomIdCode): ?RoomGroup
    {
        try {
            // Check if the table exists
            if (!Schema::hasTable('ht_room_group_items')) {
                return null;
            }

            $groupItem = DB::table('ht_room_group_items')
                ->where('room_id_code', $roomIdCode)
                ->first();

            if (!$groupItem) {
                return null;
            }

            return RoomGroup::find($groupItem->room_group_id);
        } catch (\Exception $e) {
            Log::error('Error getting room group: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Update availability for all rooms in a group
     */
    public function updateGroupAvailability(string $roomIdCode, bool $isAvailable): void
    {
        $group = $this->getRoomGroup($roomIdCode);

        if (!$group) {
            return;
        }

        $roomIdCodes = $group->items()->pluck('room_id_code')->toArray();

        foreach ($roomIdCodes as $code) {
            $this->updateRoomAvailability($code, $isAvailable);
        }
    }

    /**
     * Update a single room's availability
     */
    private function updateRoomAvailability(string $roomIdCode, bool $isAvailable): void
    {
        try {
            RoomInventory::where('room_id_code', $roomIdCode)
                ->update(['is_available' => $isAvailable]);
        } catch (\Exception $e) {
            Log::error('Failed to update room availability: ' . $e->getMessage());
        }
    }

    /**
     * Check if a room is available
     */
    public function isRoomAvailable(string $roomIdCode): bool
    {
        try {
            // Check if the table exists
            if (!Schema::hasTable('ht_room_inventory')) {
                return true;
            }

            return RoomInventory::where('room_id_code', $roomIdCode)
                ->where('is_available', true)
                ->exists();
        } catch (\Exception $e) {
            Log::error('Error checking if room is available: ' . $e->getMessage());
            return true;
        }
    }

    /**
     * Check if a group is available (all rooms in the group are available)
     */
    public function isGroupAvailable(string $groupCode): bool
    {
        try {
            // Check if the table exists
            if (!Schema::hasTable('ht_room_groups')) {
                return true;
            }

            $group = RoomGroup::where('code', $groupCode)->first();

            if (!$group) {
                return true;
            }

            return $group->areAllRoomsAvailable();
        } catch (\Exception $e) {
            Log::error('Error checking if group is available: ' . $e->getMessage());
            return true;
        }
    }
}
