<?php

namespace Bo<PERSON>ble\Widget\Repositories\Eloquent;

use Bo<PERSON><PERSON>\Support\Repositories\Eloquent\RepositoriesAbstract;
use Bo<PERSON>ble\Widget\Repositories\Interfaces\WidgetInterface;
use Illuminate\Database\Eloquent\Collection;

class WidgetRepository extends RepositoriesAbstract implements WidgetInterface
{
    public function getByTheme(string $theme): Collection
    {
        $data = $this->model->where('theme', $theme)->get();
        $this->resetModel();

        return $data;
    }
}
