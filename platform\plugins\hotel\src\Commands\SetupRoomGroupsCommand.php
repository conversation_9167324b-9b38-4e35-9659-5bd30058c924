<?php

namespace Botble\Hotel\Commands;

use Botble\Hotel\Database\Seeders\RoomGroupSeeder;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class SetupRoomGroupsCommand extends Command
{
    protected $signature = 'hotel:setup-room-groups';

    protected $description = 'Set up room groups for the 2BHK package';

    public function handle(): int
    {
        $this->info('Setting up room groups...');
        
        DB::transaction(function () {
            $seeder = new RoomGroupSeeder();
            $seeder->run();
        });
        
        $this->info('Room groups set up successfully!');
        
        return self::SUCCESS;
    }
}
