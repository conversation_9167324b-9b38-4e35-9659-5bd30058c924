<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Create room prices table for date-range based pricing
        if (!Schema::hasTable('ht_room_prices')) {
            Schema::create('ht_room_prices', function (Blueprint $table) {
                $table->id();
                $table->foreignId('room_id')->constrained('ht_rooms')->onDelete('cascade');
                $table->string('name')->nullable(); // e.g., "Summer Season", "Holiday Pricing"
                $table->decimal('price', 15, 2);
                $table->date('start_date');
                $table->date('end_date');
                $table->integer('priority')->default(1); // Higher number = higher priority
                $table->boolean('is_active')->default(true);
                $table->text('description')->nullable();
                $table->string('pricing_type', 50)->default('fixed'); // fixed, percentage, amount_adjust
                $table->decimal('adjustment_value', 15, 2)->nullable(); // For percentage or amount adjustments
                $table->json('days_of_week')->nullable(); // [1,2,3,4,5] for weekdays only
                $table->integer('min_nights')->nullable(); // Minimum nights required
                $table->integer('max_nights')->nullable(); // Maximum nights allowed
                $table->timestamps();
                
                $table->index(['room_id', 'start_date', 'end_date']);
                $table->index(['priority', 'is_active']);
            });
        }

        // Create pricing rules table for advanced dynamic pricing
        if (!Schema::hasTable('ht_pricing_rules')) {
            Schema::create('ht_pricing_rules', function (Blueprint $table) {
                $table->id();
                $table->string('name');
                $table->string('type'); // seasonal, demand_based, early_bird, last_minute, group_discount
                $table->boolean('is_active')->default(true);
                $table->integer('priority')->default(1);
                $table->json('conditions'); // Flexible conditions storage
                $table->string('adjustment_type'); // percentage, fixed_amount, fixed_price
                $table->decimal('adjustment_value', 15, 2);
                $table->date('valid_from')->nullable();
                $table->date('valid_to')->nullable();
                $table->json('applicable_rooms')->nullable(); // Room IDs or categories
                $table->json('applicable_categories')->nullable(); // Room category IDs
                $table->text('description')->nullable();
                $table->timestamps();
                
                $table->index(['type', 'is_active']);
                $table->index(['priority', 'valid_from', 'valid_to']);
            });
        }

        // Create pricing history table for tracking price changes
        if (!Schema::hasTable('ht_pricing_history')) {
            Schema::create('ht_pricing_history', function (Blueprint $table) {
                $table->id();
                $table->foreignId('room_id')->constrained('ht_rooms')->onDelete('cascade');
                $table->date('date');
                $table->decimal('base_price', 15, 2);
                $table->decimal('final_price', 15, 2);
                $table->json('applied_rules')->nullable(); // Which rules were applied
                $table->json('price_breakdown')->nullable(); // Detailed breakdown
                $table->timestamps();
                
                $table->unique(['room_id', 'date']);
                $table->index(['date', 'room_id']);
            });
        }

        // Create seasonal pricing templates
        if (!Schema::hasTable('ht_seasonal_templates')) {
            Schema::create('ht_seasonal_templates', function (Blueprint $table) {
                $table->id();
                $table->string('name');
                $table->text('description')->nullable();
                $table->json('seasons'); // Array of season definitions
                $table->boolean('is_active')->default(true);
                $table->timestamps();
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ht_seasonal_templates');
        Schema::dropIfExists('ht_pricing_history');
        Schema::dropIfExists('ht_pricing_rules');
        Schema::dropIfExists('ht_room_prices');
    }
};
