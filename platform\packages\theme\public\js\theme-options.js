$((function(){$(document).find(".colorpicker-input").length>0&&$(document).find(".colorpicker-input").colorpicker(),$(document).find(".iconpicker-input").length>0&&$(document).find(".iconpicker-input").iconpicker({selected:!0,hideOnSelect:!0}),$(document).on("submit",".theme-option form",(function(t){t.preventDefault();var n=$(t.currentTarget),e=n.find('button[type="submit"]');if("undefined"!=typeof tinymce)for(var o in tinymce.editors)tinymce.editors[o].getContent&&$("#"+o).html(tinymce.editors[o].getContent());Botble.showButtonLoading(e),$httpClient.make().post(n.prop("action"),new FormData(n[0])).then((function(t){var e=t.data;Botble.showSuccess(e.message),n.removeClass("dirty")})).finally((function(){Botble.hideButtonLoading(e)}))})),$('.theme-option button[data-bs-toggle="pill"]').on("shown.bs.tab",(function(){Botble.initResources(),"undefined"!=typeof EditorManagement&&(window.EDITOR=(new EditorManagement).init(),window.EditorManagement=window.EditorManagement||EditorManagement)}))}));