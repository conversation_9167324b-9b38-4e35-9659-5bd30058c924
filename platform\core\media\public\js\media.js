(()=>{var t={6294:(t,e,n)=>{"use strict";n.d(e,{T:()=>r,y:()=>o});var r=$.parseJSON(localStorage.getItem("MediaConfig"))||{},i={app_key:RV_MEDIA_CONFIG.random_hash?RV_MEDIA_CONFIG.random_hash:"21d06709fe1d3abdf0e35ddda89c4b282",request_params:{view_type:"tiles",filter:"everything",view_in:"all_media",sort_by:"created_at-desc",folder_id:0},hide_details_pane:!1,icons:{folder:'<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">\n            <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>\n            <path d="M5 4h4l3 3h7a2 2 0 0 1 2 2v8a2 2 0 0 1 -2 2h-14a2 2 0 0 1 -2 -2v-11a2 2 0 0 1 2 -2"></path>\n        </svg>'},actions_list:{basic:[{icon:'<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">\n                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>\n                    <path d="M10 12a2 2 0 1 0 4 0a2 2 0 0 0 -4 0"></path>\n                    <path d="M21 12c-2.4 4 -5.4 6 -9 6c-3.6 0 -6.6 -2 -9 -6c2.4 -4 5.4 -6 9 -6c3.6 0 6.6 2 9 6"></path>\n                </svg>',name:"Preview",action:"preview",order:0,class:"rv-action-preview"},{icon:'<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">\n                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>\n                    <path d="M8 5v10a1 1 0 0 0 1 1h10"></path>\n                    <path d="M5 8h10a1 1 0 0 1 1 1v10"></path>\n                </svg>',name:"Crop",action:"crop",order:1,class:"rv-action-crop"}],file:[{icon:'<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">\n                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>\n                    <path d="M7 7h-1a2 2 0 0 0 -2 2v9a2 2 0 0 0 2 2h9a2 2 0 0 0 2 -2v-1"></path>\n                    <path d="M20.385 6.585a2.1 2.1 0 0 0 -2.97 -2.97l-8.415 8.385v3h3l8.385 -8.415z"></path>\n                    <path d="M16 5l3 3"></path>\n                </svg>',name:"Rename",action:"rename",order:0,class:"rv-action-rename"},{icon:'<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">\n                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>\n                    <path d="M8 8m0 2a2 2 0 0 1 2 -2h8a2 2 0 0 1 2 2v8a2 2 0 0 1 -2 2h-8a2 2 0 0 1 -2 -2z"></path>\n                    <path d="M16 8v-2a2 2 0 0 0 -2 -2h-8a2 2 0 0 0 -2 2v8a2 2 0 0 0 2 2h2"></path>\n                </svg>',name:"Make a copy",action:"make_copy",order:1,class:"rv-action-make-copy"},{icon:'<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">\n                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>\n                    <path d="M15 8h.01"></path>\n                    <path d="M11 20h-4a3 3 0 0 1 -3 -3v-10a3 3 0 0 1 3 -3h10a3 3 0 0 1 3 3v4"></path>\n                    <path d="M4 15l4 -4c.928 -.893 2.072 -.893 3 0l3 3"></path>\n                    <path d="M14 14l1 -1c.31 -.298 .644 -.497 .987 -.596"></path>\n                    <path d="M18.42 15.61a2.1 2.1 0 0 1 2.97 2.97l-3.39 3.42h-3v-3l3.42 -3.39z"></path>\n                </svg>',name:"Alt text",action:"alt_text",order:2,class:"rv-action-alt-text"},{icon:'<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">\n                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>\n                    <path d="M9 15l6 -6"></path>\n                    <path d="M11 6l.463 -.536a5 5 0 0 1 7.071 7.072l-.534 .464"></path>\n                    <path d="M13 18l-.397 .534a5.068 5.068 0 0 1 -7.127 0a4.972 4.972 0 0 1 0 -7.071l.524 -.463"></path>\n                </svg>',name:"Copy link",action:"copy_link",order:3,class:"rv-action-copy-link"},{icon:'<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">\n                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>\n                    <path d="M9 15l6 -6"></path>\n                    <path d="M11 6l.463 -.536a5 5 0 0 1 7.071 7.072l-.534 .464"></path>\n                    <path d="M13 18l-.397 .534a5.068 5.068 0 0 1 -7.127 0a4.972 4.972 0 0 1 0 -7.071l.524 -.463"></path>\n                </svg>',name:"Copy indirect link",action:"copy_indirect_link",order:4,class:"rv-action-copy-indirect-link"},{icon:'<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">\n                  <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>\n                  <path d="M6 12m-3 0a3 3 0 1 0 6 0a3 3 0 1 0 -6 0"></path>\n                  <path d="M18 6m-3 0a3 3 0 1 0 6 0a3 3 0 1 0 -6 0"></path>\n                  <path d="M18 18m-3 0a3 3 0 1 0 6 0a3 3 0 1 0 -6 0"></path>\n                  <path d="M8.7 10.7l6.6 -3.4"></path>\n                  <path d="M8.7 13.3l6.6 3.4"></path>\n                </svg>',name:"Share",action:"share",order:5,class:"rv-action-share"}],user:[{icon:'<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">\n                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>\n                    <path d="M12 17.75l-6.172 3.245l1.179 -6.873l-5 -4.867l6.9 -1l3.086 -6.253l3.086 6.253l6.9 1l-5 4.867l1.179 6.873z"></path>\n                </svg>',name:"Favorite",action:"favorite",order:2,class:"rv-action-favorite"},{icon:'<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">\n                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>\n                    <path d="M12 17.75l-6.172 3.245l1.179 -6.873l-5 -4.867l6.9 -1l3.086 -6.253l3.086 6.253l6.9 1l-5 4.867l1.179 6.873z"></path>\n                </svg>',name:"Remove favorite",action:"remove_favorite",order:3,class:"rv-action-favorite"}],other:[{icon:'<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">\n                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>\n                    <path d="M4 17v2a2 2 0 0 0 2 2h12a2 2 0 0 0 2 -2v-2"></path>\n                    <path d="M7 11l5 5l5 -5"></path>\n                    <path d="M12 4l0 12"></path>\n                </svg>',name:"Download",action:"download",order:0,class:"rv-action-download"},{icon:'<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">\n                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>\n                    <path d="M4 7l16 0"></path>\n                    <path d="M10 11l0 6"></path>\n                    <path d="M14 11l0 6"></path>\n                    <path d="M5 7l1 12a2 2 0 0 0 2 2h8a2 2 0 0 0 2 -2l1 -12"></path>\n                    <path d="M9 7v-3a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v3"></path>\n                </svg>',name:"Move to trash",action:"trash",order:1,class:"rv-action-trash"},{icon:'<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">\n                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>\n                    <path d="M19 20h-10.5l-4.21 -4.3a1 1 0 0 1 0 -1.41l10 -10a1 1 0 0 1 1.41 0l5 5a1 1 0 0 1 0 1.41l-9.2 9.3"></path>\n                    <path d="M18 13.3l-6.3 -6.3"></path>\n                </svg>',name:"Delete permanently",action:"delete",order:2,class:"rv-action-delete"},{icon:'<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">\n                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>\n                    <path d="M9 11l-4 4l4 4m-4 -4h11a4 4 0 0 0 0 -8h-1"></path>\n                </svg>',name:"Restore",action:"restore",order:3,class:"rv-action-restore"},{icon:'<svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-palette" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M12 21a9 9 0 0 1 0 -18c4.97 0 9 3.582 9 8c0 1.06 -.474 2.078 -1.318 2.828c-.844 .75 -1.989 1.172 -3.182 1.172h-2.5a2 2 0 0 0 -1 3.75a1.3 1.3 0 0 1 -1 2.25" /><path d="M8.5 10.5m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0" /><path d="M12.5 7.5m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0" /><path d="M16.5 10.5m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0" /></svg>',name:"Properties",action:"properties",order:4,class:"rv-action-properties"}]}};r.app_key&&r.app_key===i.app_key||(r=i),r.request_params.search="";var o=$.parseJSON(localStorage.getItem("RecentItems"))||[]},418:(t,e,n)=>{"use strict";n.d(e,{M:()=>h});var r=n(6294);function i(t){return i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},i(t)}function o(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function a(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?o(Object(n),!0).forEach((function(e){s(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):o(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function s(t,e,n){return(e=l(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function c(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,l(r.key),r)}}function l(t){var e=function(t,e){if("object"!=i(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=i(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==i(e)?e:e+""}var h=function(){function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t)}return e=t,i=[{key:"getUrlParam",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;e||(e=window.location.search);var n=new RegExp("(?:[?&]|&)"+t+"=([^&]+)","i"),r=e.match(n);return r&&r.length>1?r[1]:null}},{key:"asset",value:function(t){if("//"===t.substring(0,2)||"http://"===t.substring(0,7)||"https://"===t.substring(0,8))return t;var e="/"!==RV_MEDIA_URL.base_url.substr(-1,1)?RV_MEDIA_URL.base_url+"/":RV_MEDIA_URL.base_url;return"/"===t.substring(0,1)?e+t.substring(1):e+t}},{key:"showAjaxLoading",value:function(){(arguments.length>0&&void 0!==arguments[0]?arguments[0]:$(".rv-media-main")).addClass("on-loading").append($("#rv_media_loading").html())}},{key:"hideAjaxLoading",value:function(){(arguments.length>0&&void 0!==arguments[0]?arguments[0]:$(".rv-media-main")).removeClass("on-loading").find(".loading-spinner").remove()}},{key:"isOnAjaxLoading",value:function(){return(arguments.length>0&&void 0!==arguments[0]?arguments[0]:$(".rv-media-items")).hasClass("on-loading")}},{key:"jsonEncode",value:function(t){return void 0===t&&(t=null),JSON.stringify(t)}},{key:"jsonDecode",value:function(t,e){if(!t)return e;if("string"==typeof t){var n;try{n=$.parseJSON(t)}catch(t){n=e}return n}return t}},{key:"getRequestParams",value:function(){return window.rvMedia.options&&"modal"===window.rvMedia.options.open_in?a(a({},r.T.request_params),window.rvMedia.options):r.T.request_params}},{key:"setSelectedFile",value:function(t){void 0!==window.rvMedia.options?window.rvMedia.options.selected_file_id=t:r.T.request_params.selected_file_id=t}},{key:"getConfigs",value:function(){return r.T}},{key:"storeConfig",value:function(){localStorage.setItem("MediaConfig",t.jsonEncode(r.T))}},{key:"storeRecentItems",value:function(){localStorage.setItem("RecentItems",t.jsonEncode(r.y))}},{key:"addToRecent",value:function(e){e instanceof Array?t.each(e,(function(t){r.y.push(t)})):(r.y.push(e),this.storeRecentItems())}},{key:"getItems",value:function(){var t=[];return $(".js-media-list-title").each((function(e,n){var r=$(n),i=r.data()||{};i.index_key=r.index(),t.push(i)})),t}},{key:"getSelectedItems",value:function(){var t=[];return $(".js-media-list-title input[type=checkbox]:checked").each((function(e,n){var r=$(n).closest(".js-media-list-title"),i=r.data()||{};i.index_key=r.index(),t.push(i)})),t}},{key:"getSelectedFiles",value:function(){var t=[];return $(".js-media-list-title[data-context=file] input[type=checkbox]:checked").each((function(e,n){var r=$(n).closest(".js-media-list-title"),i=r.data()||{};i.index_key=r.index(),t.push(i)})),t}},{key:"getSelectedFolder",value:function(){var t=[];return $(".js-media-list-title[data-context=folder] input[type=checkbox]:checked").each((function(e,n){var r=$(n).closest(".js-media-list-title"),i=r.data()||{};i.index_key=r.index(),t.push(i)})),t}},{key:"isUseInModal",value:function(){return window.rvMedia&&window.rvMedia.options&&"modal"===window.rvMedia.options.open_in}},{key:"resetPagination",value:function(){RV_MEDIA_CONFIG.pagination={paged:1,posts_per_page:40,in_process_get_media:!1,has_more:!0}}},{key:"trans",value:function(t){return _.get(RV_MEDIA_CONFIG.translations,t,t)}},{key:"config",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return _.get(RV_MEDIA_CONFIG,t,e)}},{key:"hasPermission",value:function(e){return t.inArray(t.config("permissions",[]),e)}},{key:"inArray",value:function(t,e){return _.includes(t,e)}},{key:"each",value:function(t,e){return _.each(t,e)}},{key:"forEach",value:function(t,e){return _.forEach(t,e)}},{key:"arrayReject",value:function(t,e){return _.reject(t,e)}},{key:"arrayFilter",value:function(t,e){return _.filter(t,e)}},{key:"arrayFirst",value:function(t){return _.first(t)}},{key:"isArray",value:function(t){return _.isArray(t)}},{key:"isEmpty",value:function(t){return _.isEmpty(t)}},{key:"size",value:function(t){return _.size(t)}}],(n=null)&&c(e.prototype,n),i&&c(e,i),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,n,i}()},8758:(t,e,n)=>{"use strict";n.d(e,{d:()=>f});var r=n(5643),i=n.n(r),o=n(6294),a=n(418),s=n(1994);function c(t){return c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},c(t)}function l(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */l=function(){return e};var t,e={},n=Object.prototype,r=n.hasOwnProperty,i=Object.defineProperty||function(t,e,n){t[e]=n.value},o="function"==typeof Symbol?Symbol:{},a=o.iterator||"@@iterator",s=o.asyncIterator||"@@asyncIterator",h=o.toStringTag||"@@toStringTag";function d(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{d({},"")}catch(t){d=function(t,e,n){return t[e]=n}}function u(t,e,n,r){var o=e&&e.prototype instanceof w?e:w,a=Object.create(o.prototype),s=new P(r||[]);return i(a,"_invoke",{value:E(t,n,s)}),a}function p(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}e.wrap=u;var f="suspendedStart",m="suspendedYield",v="executing",g="completed",y={};function w(){}function b(){}function _(){}var M={};d(M,a,(function(){return this}));var k=Object.getPrototypeOf,x=k&&k(k(T([])));x&&x!==n&&r.call(x,a)&&(M=x);var C=_.prototype=w.prototype=Object.create(M);function j(t){["next","throw","return"].forEach((function(e){d(t,e,(function(t){return this._invoke(e,t)}))}))}function S(t,e){function n(i,o,a,s){var l=p(t[i],t,o);if("throw"!==l.type){var h=l.arg,d=h.value;return d&&"object"==c(d)&&r.call(d,"__await")?e.resolve(d.__await).then((function(t){n("next",t,a,s)}),(function(t){n("throw",t,a,s)})):e.resolve(d).then((function(t){h.value=t,a(h)}),(function(t){return n("throw",t,a,s)}))}s(l.arg)}var o;i(this,"_invoke",{value:function(t,r){function i(){return new e((function(e,i){n(t,r,e,i)}))}return o=o?o.then(i,i):i()}})}function E(e,n,r){var i=f;return function(o,a){if(i===v)throw Error("Generator is already running");if(i===g){if("throw"===o)throw a;return{value:t,done:!0}}for(r.method=o,r.arg=a;;){var s=r.delegate;if(s){var c=O(s,r);if(c){if(c===y)continue;return c}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(i===f)throw i=g,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);i=v;var l=p(e,n,r);if("normal"===l.type){if(i=r.done?g:m,l.arg===y)continue;return{value:l.arg,done:r.done}}"throw"===l.type&&(i=g,r.method="throw",r.arg=l.arg)}}}function O(e,n){var r=n.method,i=e.iterator[r];if(i===t)return n.delegate=null,"throw"===r&&e.iterator.return&&(n.method="return",n.arg=t,O(e,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),y;var o=p(i,e.iterator,n.arg);if("throw"===o.type)return n.method="throw",n.arg=o.arg,n.delegate=null,y;var a=o.arg;return a?a.done?(n[e.resultName]=a.value,n.next=e.nextLoc,"return"!==n.method&&(n.method="next",n.arg=t),n.delegate=null,y):a:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,y)}function $(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function D(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function P(t){this.tryEntries=[{tryLoc:"root"}],t.forEach($,this),this.reset(!0)}function T(e){if(e||""===e){var n=e[a];if(n)return n.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var i=-1,o=function n(){for(;++i<e.length;)if(r.call(e,i))return n.value=e[i],n.done=!1,n;return n.value=t,n.done=!0,n};return o.next=o}}throw new TypeError(c(e)+" is not iterable")}return b.prototype=_,i(C,"constructor",{value:_,configurable:!0}),i(_,"constructor",{value:b,configurable:!0}),b.displayName=d(_,h,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===b||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,_):(t.__proto__=_,d(t,h,"GeneratorFunction")),t.prototype=Object.create(C),t},e.awrap=function(t){return{__await:t}},j(S.prototype),d(S.prototype,s,(function(){return this})),e.AsyncIterator=S,e.async=function(t,n,r,i,o){void 0===o&&(o=Promise);var a=new S(u(t,n,r,i),o);return e.isGeneratorFunction(n)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},j(C),d(C,h,"Generator"),d(C,a,(function(){return this})),d(C,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),n=[];for(var r in e)n.push(r);return n.reverse(),function t(){for(;n.length;){var r=n.pop();if(r in e)return t.value=r,t.done=!1,t}return t.done=!0,t}},e.values=T,P.prototype={constructor:P,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(D),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var n=this;function i(r,i){return s.type="throw",s.arg=e,n.next=r,i&&(n.method="next",n.arg=t),!!i}for(var o=this.tryEntries.length-1;o>=0;--o){var a=this.tryEntries[o],s=a.completion;if("root"===a.tryLoc)return i("end");if(a.tryLoc<=this.prev){var c=r.call(a,"catchLoc"),l=r.call(a,"finallyLoc");if(c&&l){if(this.prev<a.catchLoc)return i(a.catchLoc,!0);if(this.prev<a.finallyLoc)return i(a.finallyLoc)}else if(c){if(this.prev<a.catchLoc)return i(a.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return i(a.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var i=this.tryEntries[n];if(i.tryLoc<=this.prev&&r.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var o=i;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=e&&e<=o.finallyLoc&&(o=null);var a=o?o.completion:{};return a.type=t,a.arg=e,o?(this.method="next",this.next=o.finallyLoc,y):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),y},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),D(n),y}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var r=n.completion;if("throw"===r.type){var i=r.arg;D(n)}return i}}throw Error("illegal catch attempt")},delegateYield:function(e,n,r){return this.delegate={iterator:T(e),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=t),y}},e}function h(t,e,n,r,i,o,a){try{var s=t[o](a),c=s.value}catch(t){return void n(t)}s.done?e(c):Promise.resolve(c).then(r,i)}function d(t){return function(){var e=this,n=arguments;return new Promise((function(r,i){var o=t.apply(e,n);function a(t){h(o,r,i,a,s,"next",t)}function s(t){h(o,r,i,a,s,"throw",t)}a(void 0)}))}}function u(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,p(r.key),r)}}function p(t){var e=function(t,e){if("object"!=c(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=c(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==c(e)?e:e+""}var f=function(){function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t)}return e=t,n=null,r=[{key:"handleDropdown",value:function(){var e=a.M.size(a.M.getSelectedItems());t.renderActions(),e>0?$(".rv-dropdown-actions > .dropdown-toggle").removeClass("disabled").prop("disabled",!1):$(".rv-dropdown-actions > .dropdown-toggle").addClass("disabled").prop("disabled",!0)}},{key:"handlePreview",value:function(){var t=[];a.M.each(a.M.getSelectedFiles(),(function(e){if(e.preview_url){if("document"===e.type){var n=document.createElement("iframe");n.src=e.preview_url,n.allowFullscreen=!0,n.style.width="100vh",n.style.height="100vh",t.push(n)}else t.push(e.preview_url);o.y.push(e.id)}})),a.M.size(t)>0?(Botble.lightbox(t),a.M.storeRecentItems()):this.handleGlobalAction("download")}},{key:"renderCropImage",value:function(){var t,e=$("#rv_media_crop_image").html(),n=$("#modal_crop_image .crop-image").empty(),r=a.M.getSelectedItems()[0],o=$("#modal_crop_image .form-crop"),s=e.replace(/__src__/gi,r.full_url);n.append(s);var c=n.find("img")[0],l={minContainerWidth:500,minContainerHeight:550,dragMode:"move",crop:function(e){t=e.detail,o.find('input[name="image_id"]').val(r.id),o.find('input[name="crop_data"]').val(JSON.stringify(t)),d(t.height),u(t.width)}},h=new(i())(c,l);o.find("#aspectRatio").on("click",(function(){h.destroy(),$(this).is(":checked")?l.aspectRatio=t.width/t.height:l.aspectRatio=null,h=new(i())(c,l)})),o.find("#dataHeight").on("change",(function(){t.height=parseFloat($(this).val()),h.setData(t),d(t.height)})),o.find("#dataWidth").on("change",(function(){t.width=parseFloat($(this).val()),h.setData(t),u(t.width)}));var d=function(t){o.find("#dataHeight").val(parseInt(t))},u=function(t){o.find("#dataWidth").val(parseInt(t))}}},{key:"handleCopyLink",value:(h=d(l().mark((function t(){var e;return l().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return e="",a.M.each(a.M.getSelectedFiles(),(function(t){a.M.isEmpty(e)||(e+="\n"),e+=t.full_url})),t.next=4,Botble.copyToClipboard(e);case 4:s.b.showMessage("success",a.M.trans("clipboard.success"),a.M.trans("message.success_header"));case 5:case"end":return t.stop()}}),t)}))),function(){return h.apply(this,arguments)})},{key:"handleCopyIndirectLink",value:(c=d(l().mark((function t(){var e;return l().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return e="",a.M.each(a.M.getSelectedFiles(),(function(t){a.M.isEmpty(e)||(e+="\n"),e+=t.indirect_url})),t.next=4,Botble.copyToClipboard(e);case 4:s.b.showMessage("success",a.M.trans("clipboard.success"),a.M.trans("message.success_header"));case 5:case"end":return t.stop()}}),t)}))),function(){return c.apply(this,arguments)})},{key:"handleShare",value:function(){$("#modal_share_items").modal("show").find("form.form-alt-text").data("action",type)}},{key:"handleGlobalAction",value:function(e,n){var r=[];switch(a.M.each(a.M.getSelectedItems(),(function(t){r.push({is_folder:t.is_folder,id:t.id,full_url:t.full_url})})),e){case"rename":$("#modal_rename_items").modal("show").find("form.form-rename").data("action",e);break;case"copy_link":t.handleCopyLink().then((function(){}));break;case"copy_indirect_link":t.handleCopyIndirectLink().then((function(){}));break;case"share":$("#modal_share_items").modal("show");break;case"preview":t.handlePreview();break;case"alt_text":$("#modal_alt_text_items").modal("show").find("form.form-alt-text").data("action",e);break;case"crop":$("#modal_crop_image").modal("show").find("form.rv-form").data("action",e);break;case"trash":$("#modal_trash_items").modal("show").find("form.form-delete-items").data("action",e);break;case"delete":$("#modal_delete_items").modal("show").find("form.form-delete-items").data("action",e);break;case"empty_trash":$("#modal_empty_trash").modal("show").find("form.form-empty-trash").data("action",e);break;case"download":var i=[];a.M.each(a.M.getSelectedItems(),(function(t){a.M.inArray(a.M.getConfigs().denied_download,t.mime_type)||i.push({id:t.id,is_folder:t.is_folder})})),i.length?t.handleDownload(i):s.b.showMessage("error",a.M.trans("download.error"),a.M.trans("message.error_header"));break;case"properties":$("#modal-properties").modal("show");break;default:t.processAction({selected:r,action:e},n)}}},{key:"processAction",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;a.M.showAjaxLoading(),$httpClient.make().post(RV_MEDIA_URL.global_actions,t).then((function(t){var n=t.data;a.M.resetPagination(),s.b.showMessage("success",n.message,a.M.trans("message.success_header")),e&&e(n)})).catch((function(t){var n=t.response;return e&&e(n.data)})).finally((function(){return a.M.hideAjaxLoading()}))}},{key:"renderRenameItems",value:function(){var t=$("#rv_media_rename_item").html(),e=$("#modal_rename_items .rename-items").empty();a.M.each(a.M.getSelectedItems(),(function(n){var r=t.replace(/__icon__/gi,n.icon||'<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">\n                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>\n                    <path d="M5 4h4l3 3h7a2 2 0 0 1 2 2v8a2 2 0 0 1 -2 2h-14a2 2 0 0 1 -2 -2v-11a2 2 0 0 1 2 -2"></path>\n                </svg>').replace(/__placeholder__/gi,"Input file name").replace(/__value__/gi,n.name),i=$(r);i.data("id",n.id.toString()),i.data("is_folder",n.is_folder),i.data("name",n.name);var o=i.find('input[name="rename_physical_file"]');o.closest(".form-check").find("span").text(n.is_folder?o.data("folder-label"):o.data("file-label")),i.find('input[name="rename_physical_file"]').on("change",(function(){i.data("rename_physical_file",$(this).is(":checked"))})),e.append(i),Botble.initFieldCollapse()}))}},{key:"renderAltTextItems",value:function(){var t=$("#rv_media_alt_text_item").html(),e=$("#modal_alt_text_items .alt-text-items").empty();a.M.each(a.M.getSelectedItems(),(function(n){var r=t.replace(/__icon__/gi,n.icon||'<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">\n                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>\n                    <path d="M5 4h4l3 3h7a2 2 0 0 1 2 2v8a2 2 0 0 1 -2 2h-14a2 2 0 0 1 -2 -2v-11a2 2 0 0 1 2 -2"></path>\n                </svg>').replace(/__placeholder__/gi,"Input file alt").replace(/__value__/gi,null===n.alt?"":n.alt),i=$(r);i.data("id",n.id),i.data("alt",n.alt),e.append(i)}))}},{key:"renderShareItems",value:function(){var t=$('#modal_share_items [data-bb-value="share-result"]'),e=$('#modal_share_items select[data-bb-value="share-type"]').val();t.val("");var n=[];a.M.each(a.M.getSelectedItems(),(function(t){switch(e){case"html":n.push("image"===t.type?'<img src="'.concat(t.full_url,'" alt="').concat(t.alt,'" />'):'<a href="'.concat(t.full_url,'" target="_blank">').concat(t.alt,"</a>"));break;case"markdown":n.push(("image"===t.type?"!":"")+"[".concat(t.alt,"](").concat(t.full_url,")"));break;case"indirect_url":n.push(t.indirect_url);break;default:n.push(t.full_url)}})),t.val(n.join("\n"))}},{key:"renderActions",value:function(){var t=a.M.getSelectedFolder().length>0,e=$("#rv_action_item").html(),n=0,r=$(".rv-dropdown-actions .dropdown-menu");r.empty();var i=$.extend({},!0,a.M.getConfigs().actions_list);if(t){var o=["preview","crop","alt_text","copy_link","copy_direct_link","share"];i.basic=a.M.arrayReject(i.basic,(function(t){return o.includes(t.action)})),a.M.hasPermission("folders.create")||(i.file=a.M.arrayReject(i.file,(function(t){return"make_copy"===t.action}))),a.M.hasPermission("folders.edit")||(i.file=a.M.arrayReject(i.file,(function(t){return a.M.inArray(["rename"],t.action)})),i.user=a.M.arrayReject(i.user,(function(t){return a.M.inArray(["rename"],t.action)}))),a.M.hasPermission("folders.trash")||(i.other=a.M.arrayReject(i.other,(function(t){return a.M.inArray(["trash","restore"],t.action)}))),a.M.hasPermission("folders.destroy")||(i.other=a.M.arrayReject(i.other,(function(t){return a.M.inArray(["delete"],t.action)}))),a.M.hasPermission("folders.favorite")||(i.other=a.M.arrayReject(i.other,(function(t){return a.M.inArray(["favorite","remove_favorite"],t.action)})))}var s=a.M.getSelectedFiles();a.M.arrayFilter(s,(function(t){return t.preview_url})).length||(i.basic=a.M.arrayReject(i.basic,(function(t){return"preview"===t.action}))),a.M.arrayFilter(s,(function(t){return"image"===t.type})).length||(i.basic=a.M.arrayReject(i.basic,(function(t){return"crop"===t.action})),i.file=a.M.arrayReject(i.file,(function(t){return"alt_text"===t.action}))),s.length>0&&(a.M.hasPermission("files.create")||(i.file=a.M.arrayReject(i.file,(function(t){return"make_copy"===t.action}))),a.M.hasPermission("files.edit")||(i.file=a.M.arrayReject(i.file,(function(t){return a.M.inArray(["rename"],t.action)}))),a.M.hasPermission("files.trash")||(i.other=a.M.arrayReject(i.other,(function(t){return a.M.inArray(["trash","restore"],t.action)}))),a.M.hasPermission("files.destroy")||(i.other=a.M.arrayReject(i.other,(function(t){return a.M.inArray(["delete"],t.action)}))),a.M.hasPermission("files.favorite")||(i.other=a.M.arrayReject(i.other,(function(t){return a.M.inArray(["favorite","remove_favorite"],t.action)}))),s.length>1&&(i.basic=a.M.arrayReject(i.basic,(function(t){return"crop"===t.action})))),(!a.M.hasPermission("folders.edit")||s.length>0)&&(i.other=a.M.arrayReject(i.other,(function(t){return a.M.inArray(["properties"],t.action)}))),a.M.each(i,(function(t,i){a.M.each(t,(function(t,o){var s=!1;switch(a.M.getRequestParams().view_in){case"all_media":a.M.inArray(["remove_favorite","delete","restore"],t.action)&&(s=!0);break;case"recent":a.M.inArray(["remove_favorite","delete","restore","make_copy"],t.action)&&(s=!0);break;case"favorites":a.M.inArray(["favorite","delete","restore","make_copy"],t.action)&&(s=!0);break;case"trash":a.M.inArray(["preview","delete","restore","rename","download"],t.action)||(s=!0)}if(!s){var c=e.replace(/__action__/gi,t.action||"").replace('<i class="__icon__ dropdown-item-icon dropdown-item-icon"></i>','<span class="icon-tabler-wrapper dropdown-item-icon">__icon__</span>').replace("__icon__",'<span class="icon-tabler-wrapper dropdown-item-icon">__icon__</span>').replace("__icon__",t.icon||"").replace(/__name__/gi,a.M.trans("actions_list.".concat(i,".").concat(t.action))||t.name);t.icon&&(c=c.replace("media-icon","media-icon dropdown-item-icon")),!o&&n&&(c='<li role="separator" class="divider"></li>'.concat(c)),r.append(c)}})),t.length>0&&n++}))}},{key:"handleDownload",value:function(t){var e=$(".media-download-popup");e.show(),$httpClient.make().withResponseType("blob").post(RV_MEDIA_URL.download,{selected:t}).then((function(t){var e=(t.headers["content-disposition"]||"").split("filename=")[1].split(";")[0],n=URL.createObjectURL(t.data),r=document.createElement("a");r.href=n,r.download=e,document.body.appendChild(r),r.click(),r.remove(),URL.revokeObjectURL(n)})).finally((function(){e.hide(),clearTimeout(null)}))}}],n&&u(e.prototype,n),r&&u(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,n,r,c,h}()},27:(t,e,n)=>{"use strict";n.d(e,{K:()=>c});var r=n(8758),i=n(418);function o(t){return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},o(t)}function a(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,s(r.key),r)}}function s(t){var e=function(t,e){if("object"!=o(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=o(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==o(e)?e:e+""}var c=function(){function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t)}return e=t,o=[{key:"initContext",value:function(){jQuery().contextMenu&&($.contextMenu({selector:'.js-context-menu[data-context="file"]',build:function(){return{items:t._fileContextMenu()}}}),$.contextMenu({selector:'.js-context-menu[data-context="folder"]',build:function(){return{items:t._folderContextMenu()}}}))}},{key:"_fileContextMenu",value:function(){var t={preview:{name:"Preview",icon:function(t,e,n,r){return e.html('<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">\n                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>\n                        <path d="M10 12a2 2 0 1 0 4 0a2 2 0 0 0 -4 0"></path>\n                        <path d="M21 12c-2.4 4 -5.4 6 -9 6c-3.6 0 -6.6 -2 -9 -6c2.4 -4 5.4 -6 9 -6c3.6 0 6.6 2 9 6"></path>\n                    </svg> '.concat(r.name)),"context-menu-icon-updated"},callback:function(){r.d.handlePreview()}}};i.M.each(i.M.getConfigs().actions_list,(function(e,n){i.M.each(e,(function(e){t[e.action]={name:e.name,icon:function(t,r,o,a){return r.html("".concat(e.icon," ").concat(i.M.trans("actions_list.".concat(n,".").concat(e.action))||a.name)),"context-menu-icon-updated"},callback:function(){$('.js-files-action[data-action="'.concat(e.action,'"]')).trigger("click")}}}))}));var e=[];switch(i.M.getRequestParams().view_in){case"all_media":e=["remove_favorite","delete","restore"];break;case"recent":e=["remove_favorite","delete","restore","make_copy"];break;case"favorites":e=["favorite","delete","restore","make_copy"];break;case"trash":t={preview:t.preview,rename:t.rename,download:t.download,delete:t.delete,restore:t.restore}}i.M.each(e,(function(e){t[e]=void 0})),i.M.getSelectedFolder().length>0&&(t.preview=void 0,t.crop=void 0,t.copy_link=void 0,t.copy_indirect_link=void 0,t.share=void 0,t.alt_text=void 0,i.M.hasPermission("folders.create")||(t.make_copy=void 0),i.M.hasPermission("folders.edit")||(t.rename=void 0),i.M.hasPermission("folders.trash")||(t.trash=void 0,t.restore=void 0),i.M.hasPermission("folders.destroy")||(t.delete=void 0),i.M.hasPermission("folders.favorite")||(t.favorite=void 0,t.remove_favorite=void 0));var n=i.M.getSelectedFiles();return n.length>0&&(i.M.hasPermission("files.create")||(t.make_copy=void 0),i.M.hasPermission("files.edit")||(t.rename=void 0),i.M.hasPermission("files.trash")||(t.trash=void 0,t.restore=void 0),i.M.hasPermission("files.destroy")||(t.delete=void 0),i.M.hasPermission("files.favorite")||(t.favorite=void 0,t.remove_favorite=void 0),n.length>1&&(t.crop=void 0),t.properties=void 0),i.M.arrayFilter(n,(function(t){return t.preview_url})).length||(t.preview=void 0),i.M.arrayFilter(n,(function(t){return"image"===t.type})).length||(t.crop=void 0,t.alt_text=void 0),i.M.arrayFilter(n,(function(t){return t.full_url})).length||(t.copy_link=void 0),t}},{key:"_folderContextMenu",value:function(){var e=t._fileContextMenu();return e.preview=void 0,e.copy_link=void 0,e}},{key:"destroyContext",value:function(){jQuery().contextMenu&&$.contextMenu("destroy")}}],(n=null)&&a(e.prototype,n),o&&a(e,o),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,n,o}()},1994:(t,e,n)=>{"use strict";function r(t){return r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},r(t)}function i(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,o(r.key),r)}}function o(t){var e=function(t,e){if("object"!=r(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var i=n.call(t,e||"default");if("object"!=r(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==r(e)?e:e+""}n.d(e,{b:()=>a});var a=function(){return t=function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t)},n=[{key:"showMessage",value:function(t,e){Botble.showNotice(t,e)}}],(e=null)&&i(t.prototype,e),n&&i(t,n),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,e,n}()},3702:(t,e,n)=>{"use strict";n.d(e,{x:()=>d});var r=n(418),i=n(6294),o=n(27);function a(t){return a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},a(t)}function s(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function c(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,h(r.key),r)}}function l(t,e,n){return e&&c(t.prototype,e),n&&c(t,n),Object.defineProperty(t,"prototype",{writable:!1}),t}function h(t){var e=function(t,e){if("object"!=a(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=a(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==a(e)?e:e+""}var d=function(){return l((function t(){s(this,t)}),null,[{key:"editorSelectFile",value:function(t){var e=r.M.getUrlParam("CKEditor")||r.M.getUrlParam("CKEditorFuncNum");if(window.opener&&e){var n=r.M.arrayFirst(t);window.opener.CKEDITOR.tools.callFunction(r.M.getUrlParam("CKEditorFuncNum"),n.full_url),window.opener&&window.close()}}}])}(),u=l((function t(e,n){s(this,t);var a=window.RvMediaCustomCallback||null;if("function"!=typeof a){window.rvMedia=window.rvMedia||{};var c=$("body");n=$.extend(!0,{multiple:!0,type:"*",onSelectFiles:function(t,e){}},n);var l=function(t){t.preventDefault();var e=$(t.currentTarget);$("#rv_media_modal").modal("show"),window.rvMedia.options=n,window.rvMedia.options.open_in="modal",window.rvMedia.$el=e,i.T.request_params.filter="everything",r.M.storeConfig();var a=window.rvMedia.$el.data("rv-media");void 0!==a&&a.length>0&&(a=a[0],window.rvMedia.options=$.extend(!0,window.rvMedia.options,a||{}),void 0!==a.selected_file_id?window.rvMedia.options.is_popup=!0:void 0!==window.rvMedia.options.is_popup&&(window.rvMedia.options.is_popup=void 0)),0===$("#rv_media_body .rv-media-container").length?$("#rv_media_body").load(RV_MEDIA_URL.popup,(function(t){t.error&&alert(t.message),$("#rv_media_body").removeClass("media-modal-loading").closest(".modal-content").removeClass("bb-loading"),$(document).find(".rv-media-container .js-change-action[data-type=refresh]").trigger("click"),"everything"!==r.M.getRequestParams().filter&&$(".rv-media-actions .btn.js-rv-media-change-filter-group.js-filter-by-type").hide(),o.K.destroyContext(),o.K.initContext()})):$(document).find(".rv-media-container .js-change-action[data-type=refresh]").trigger("click")};"string"==typeof e?c.off("click",e).on("click",e,l):e.off("click").on("click",l)}else a(e,n)}));window.RvMediaStandAlone=u,$(".js-insert-to-editor").off("click").on("click",(function(t){t.preventDefault();var e=r.M.getSelectedFiles();r.M.size(e)>0&&d.editorSelectFile(e)})),$.fn.rvMedia=function(t){var e=$(this);i.T.request_params.filter="everything",$(document).find(".js-insert-to-editor").prop("disabled","trash"===i.T.request_params.view_in),r.M.storeConfig();var n=window.RvMediaCustomCallback||null;"function"!=typeof n?new u(e,t):n(e,t)},document.dispatchEvent(new CustomEvent("core-media-loaded"))},2555:()=>{jQuery.event.special.doubletap={bindType:"touchend",delegateType:"touchend",handle:function(t){var e=t.handleObj,n=jQuery.data(t.target),r=(new Date).getTime(),i=n.lastTouch?r-n.lastTouch:0,o=null==o?300:o;i<o&&i>30?(n.lastTouch=null,t.type=e.origType,["clientX","clientY","pageX","pageY"].forEach((function(e){t[e]=t.originalEvent.changedTouches[0][e]})),e.handler.apply(this,arguments)):n.lastTouch=r}}},5643:function(t){
/*!
 * Cropper.js v1.6.2
 * https://fengyuanchen.github.io/cropperjs
 *
 * Copyright 2015-present Chen Fengyuan
 * Released under the MIT license
 *
 * Date: 2024-04-21T07:43:05.335Z
 */
t.exports=function(){"use strict";function t(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function e(e){for(var n=1;n<arguments.length;n++){var r=null!=arguments[n]?arguments[n]:{};n%2?t(Object(r),!0).forEach((function(t){c(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):t(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function n(t,e){if("object"!=typeof t||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}function r(t){var e=n(t,"string");return"symbol"==typeof e?e:e+""}function i(t){return i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},i(t)}function o(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function a(t,e){for(var n=0;n<e.length;n++){var i=e[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,r(i.key),i)}}function s(t,e,n){return e&&a(t.prototype,e),n&&a(t,n),Object.defineProperty(t,"prototype",{writable:!1}),t}function c(t,e,n){return(e=r(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function l(t){return h(t)||d(t)||u(t)||f()}function h(t){if(Array.isArray(t))return p(t)}function d(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}function u(t,e){if(t){if("string"==typeof t)return p(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?p(t,e):void 0}}function p(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function f(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var m="undefined"!=typeof window&&void 0!==window.document,v=m?window:{},g=!(!m||!v.document.documentElement)&&"ontouchstart"in v.document.documentElement,y=!!m&&"PointerEvent"in v,w="cropper",b="all",_="crop",M="move",k="zoom",x="e",C="w",j="s",S="n",E="ne",O="nw",$="se",D="sw",P="".concat(w,"-crop"),T="".concat(w,"-disabled"),L="".concat(w,"-hidden"),I="".concat(w,"-hide"),R="".concat(w,"-invisible"),B="".concat(w,"-modal"),A="".concat(w,"-move"),N="".concat(w,"Action"),z="".concat(w,"Preview"),F="crop",H="move",W="none",G="crop",U="cropend",Y="cropmove",q="cropstart",X="dblclick",V=g?"touchstart":"mousedown",K=g?"touchmove":"mousemove",Z=g?"touchend touchcancel":"mouseup",Q=y?"pointerdown":V,J=y?"pointermove":K,tt=y?"pointerup pointercancel":Z,et="ready",nt="resize",rt="wheel",it="zoom",ot="image/jpeg",at=/^e|w|s|n|se|sw|ne|nw|all|crop|move|zoom$/,st=/^data:/,ct=/^data:image\/jpeg;base64,/,lt=/^img|canvas$/i,ht=200,dt=100,ut={viewMode:0,dragMode:F,initialAspectRatio:NaN,aspectRatio:NaN,data:null,preview:"",responsive:!0,restore:!0,checkCrossOrigin:!0,checkOrientation:!0,modal:!0,guides:!0,center:!0,highlight:!0,background:!0,autoCrop:!0,autoCropArea:.8,movable:!0,rotatable:!0,scalable:!0,zoomable:!0,zoomOnTouch:!0,zoomOnWheel:!0,wheelZoomRatio:.1,cropBoxMovable:!0,cropBoxResizable:!0,toggleDragModeOnDblclick:!0,minCanvasWidth:0,minCanvasHeight:0,minCropBoxWidth:0,minCropBoxHeight:0,minContainerWidth:ht,minContainerHeight:dt,ready:null,cropstart:null,cropmove:null,cropend:null,crop:null,zoom:null},pt='<div class="cropper-container" touch-action="none"><div class="cropper-wrap-box"><div class="cropper-canvas"></div></div><div class="cropper-drag-box"></div><div class="cropper-crop-box"><span class="cropper-view-box"></span><span class="cropper-dashed dashed-h"></span><span class="cropper-dashed dashed-v"></span><span class="cropper-center"></span><span class="cropper-face"></span><span class="cropper-line line-e" data-cropper-action="e"></span><span class="cropper-line line-n" data-cropper-action="n"></span><span class="cropper-line line-w" data-cropper-action="w"></span><span class="cropper-line line-s" data-cropper-action="s"></span><span class="cropper-point point-e" data-cropper-action="e"></span><span class="cropper-point point-n" data-cropper-action="n"></span><span class="cropper-point point-w" data-cropper-action="w"></span><span class="cropper-point point-s" data-cropper-action="s"></span><span class="cropper-point point-ne" data-cropper-action="ne"></span><span class="cropper-point point-nw" data-cropper-action="nw"></span><span class="cropper-point point-sw" data-cropper-action="sw"></span><span class="cropper-point point-se" data-cropper-action="se"></span></div></div>',ft=Number.isNaN||v.isNaN;function mt(t){return"number"==typeof t&&!ft(t)}var vt=function(t){return t>0&&t<1/0};function gt(t){return void 0===t}function yt(t){return"object"===i(t)&&null!==t}var wt=Object.prototype.hasOwnProperty;function bt(t){if(!yt(t))return!1;try{var e=t.constructor,n=e.prototype;return e&&n&&wt.call(n,"isPrototypeOf")}catch(t){return!1}}function _t(t){return"function"==typeof t}var Mt=Array.prototype.slice;function kt(t){return Array.from?Array.from(t):Mt.call(t)}function xt(t,e){return t&&_t(e)&&(Array.isArray(t)||mt(t.length)?kt(t).forEach((function(n,r){e.call(t,n,r,t)})):yt(t)&&Object.keys(t).forEach((function(n){e.call(t,t[n],n,t)}))),t}var Ct=Object.assign||function(t){for(var e=arguments.length,n=new Array(e>1?e-1:0),r=1;r<e;r++)n[r-1]=arguments[r];return yt(t)&&n.length>0&&n.forEach((function(e){yt(e)&&Object.keys(e).forEach((function(n){t[n]=e[n]}))})),t},jt=/\.\d*(?:0|9){12}\d*$/;function St(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1e11;return jt.test(t)?Math.round(t*e)/e:t}var Et=/^width|height|left|top|marginLeft|marginTop$/;function Ot(t,e){var n=t.style;xt(e,(function(t,e){Et.test(e)&&mt(t)&&(t="".concat(t,"px")),n[e]=t}))}function $t(t,e){return t.classList?t.classList.contains(e):t.className.indexOf(e)>-1}function Dt(t,e){if(e)if(mt(t.length))xt(t,(function(t){Dt(t,e)}));else if(t.classList)t.classList.add(e);else{var n=t.className.trim();n?n.indexOf(e)<0&&(t.className="".concat(n," ").concat(e)):t.className=e}}function Pt(t,e){e&&(mt(t.length)?xt(t,(function(t){Pt(t,e)})):t.classList?t.classList.remove(e):t.className.indexOf(e)>=0&&(t.className=t.className.replace(e,"")))}function Tt(t,e,n){e&&(mt(t.length)?xt(t,(function(t){Tt(t,e,n)})):n?Dt(t,e):Pt(t,e))}var Lt=/([a-z\d])([A-Z])/g;function It(t){return t.replace(Lt,"$1-$2").toLowerCase()}function Rt(t,e){return yt(t[e])?t[e]:t.dataset?t.dataset[e]:t.getAttribute("data-".concat(It(e)))}function Bt(t,e,n){yt(n)?t[e]=n:t.dataset?t.dataset[e]=n:t.setAttribute("data-".concat(It(e)),n)}function At(t,e){if(yt(t[e]))try{delete t[e]}catch(n){t[e]=void 0}else if(t.dataset)try{delete t.dataset[e]}catch(n){t.dataset[e]=void 0}else t.removeAttribute("data-".concat(It(e)))}var Nt=/\s\s*/,zt=function(){var t=!1;if(m){var e=!1,n=function(){},r=Object.defineProperty({},"once",{get:function(){return t=!0,e},set:function(t){e=t}});v.addEventListener("test",n,r),v.removeEventListener("test",n,r)}return t}();function Ft(t,e,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},i=n;e.trim().split(Nt).forEach((function(e){if(!zt){var o=t.listeners;o&&o[e]&&o[e][n]&&(i=o[e][n],delete o[e][n],0===Object.keys(o[e]).length&&delete o[e],0===Object.keys(o).length&&delete t.listeners)}t.removeEventListener(e,i,r)}))}function Ht(t,e,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},i=n;e.trim().split(Nt).forEach((function(e){if(r.once&&!zt){var o=t.listeners,a=void 0===o?{}:o;i=function(){delete a[e][n],t.removeEventListener(e,i,r);for(var o=arguments.length,s=new Array(o),c=0;c<o;c++)s[c]=arguments[c];n.apply(t,s)},a[e]||(a[e]={}),a[e][n]&&t.removeEventListener(e,a[e][n],r),a[e][n]=i,t.listeners=a}t.addEventListener(e,i,r)}))}function Wt(t,e,n){var r;return _t(Event)&&_t(CustomEvent)?r=new CustomEvent(e,{detail:n,bubbles:!0,cancelable:!0}):(r=document.createEvent("CustomEvent")).initCustomEvent(e,!0,!0,n),t.dispatchEvent(r)}function Gt(t){var e=t.getBoundingClientRect();return{left:e.left+(window.pageXOffset-document.documentElement.clientLeft),top:e.top+(window.pageYOffset-document.documentElement.clientTop)}}var Ut=v.location,Yt=/^(\w+:)\/\/([^:/?#]*):?(\d*)/i;function qt(t){var e=t.match(Yt);return null!==e&&(e[1]!==Ut.protocol||e[2]!==Ut.hostname||e[3]!==Ut.port)}function Xt(t){var e="timestamp=".concat((new Date).getTime());return t+(-1===t.indexOf("?")?"?":"&")+e}function Vt(t){var e=t.rotate,n=t.scaleX,r=t.scaleY,i=t.translateX,o=t.translateY,a=[];mt(i)&&0!==i&&a.push("translateX(".concat(i,"px)")),mt(o)&&0!==o&&a.push("translateY(".concat(o,"px)")),mt(e)&&0!==e&&a.push("rotate(".concat(e,"deg)")),mt(n)&&1!==n&&a.push("scaleX(".concat(n,")")),mt(r)&&1!==r&&a.push("scaleY(".concat(r,")"));var s=a.length?a.join(" "):"none";return{WebkitTransform:s,msTransform:s,transform:s}}function Kt(t){var n=e({},t),r=0;return xt(t,(function(t,e){delete n[e],xt(n,(function(e){var n=Math.abs(t.startX-e.startX),i=Math.abs(t.startY-e.startY),o=Math.abs(t.endX-e.endX),a=Math.abs(t.endY-e.endY),s=Math.sqrt(n*n+i*i),c=(Math.sqrt(o*o+a*a)-s)/s;Math.abs(c)>Math.abs(r)&&(r=c)}))})),r}function Zt(t,n){var r=t.pageX,i=t.pageY,o={endX:r,endY:i};return n?o:e({startX:r,startY:i},o)}function Qt(t){var e=0,n=0,r=0;return xt(t,(function(t){var i=t.startX,o=t.startY;e+=i,n+=o,r+=1})),{pageX:e/=r,pageY:n/=r}}function Jt(t){var e=t.aspectRatio,n=t.height,r=t.width,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"contain",o=vt(r),a=vt(n);if(o&&a){var s=n*e;"contain"===i&&s>r||"cover"===i&&s<r?n=r/e:r=n*e}else o?n=r/e:a&&(r=n*e);return{width:r,height:n}}function te(t){var e=t.width,n=t.height,r=t.degree;if(90==(r=Math.abs(r)%180))return{width:n,height:e};var i=r%90*Math.PI/180,o=Math.sin(i),a=Math.cos(i),s=e*a+n*o,c=e*o+n*a;return r>90?{width:c,height:s}:{width:s,height:c}}function ee(t,e,n,r){var i=e.aspectRatio,o=e.naturalWidth,a=e.naturalHeight,s=e.rotate,c=void 0===s?0:s,h=e.scaleX,d=void 0===h?1:h,u=e.scaleY,p=void 0===u?1:u,f=n.aspectRatio,m=n.naturalWidth,v=n.naturalHeight,g=r.fillColor,y=void 0===g?"transparent":g,w=r.imageSmoothingEnabled,b=void 0===w||w,_=r.imageSmoothingQuality,M=void 0===_?"low":_,k=r.maxWidth,x=void 0===k?1/0:k,C=r.maxHeight,j=void 0===C?1/0:C,S=r.minWidth,E=void 0===S?0:S,O=r.minHeight,$=void 0===O?0:O,D=document.createElement("canvas"),P=D.getContext("2d"),T=Jt({aspectRatio:f,width:x,height:j}),L=Jt({aspectRatio:f,width:E,height:$},"cover"),I=Math.min(T.width,Math.max(L.width,m)),R=Math.min(T.height,Math.max(L.height,v)),B=Jt({aspectRatio:i,width:x,height:j}),A=Jt({aspectRatio:i,width:E,height:$},"cover"),N=Math.min(B.width,Math.max(A.width,o)),z=Math.min(B.height,Math.max(A.height,a)),F=[-N/2,-z/2,N,z];return D.width=St(I),D.height=St(R),P.fillStyle=y,P.fillRect(0,0,I,R),P.save(),P.translate(I/2,R/2),P.rotate(c*Math.PI/180),P.scale(d,p),P.imageSmoothingEnabled=b,P.imageSmoothingQuality=M,P.drawImage.apply(P,[t].concat(l(F.map((function(t){return Math.floor(St(t))}))))),P.restore(),D}var ne=String.fromCharCode;function re(t,e,n){var r="";n+=e;for(var i=e;i<n;i+=1)r+=ne(t.getUint8(i));return r}var ie=/^data:.*,/;function oe(t){var e=t.replace(ie,""),n=atob(e),r=new ArrayBuffer(n.length),i=new Uint8Array(r);return xt(i,(function(t,e){i[e]=n.charCodeAt(e)})),r}function ae(t,e){for(var n=[],r=8192,i=new Uint8Array(t);i.length>0;)n.push(ne.apply(null,kt(i.subarray(0,r)))),i=i.subarray(r);return"data:".concat(e,";base64,").concat(btoa(n.join("")))}function se(t){var e,n=new DataView(t);try{var r,i,o;if(255===n.getUint8(0)&&216===n.getUint8(1))for(var a=n.byteLength,s=2;s+1<a;){if(255===n.getUint8(s)&&225===n.getUint8(s+1)){i=s;break}s+=1}if(i){var c=i+10;if("Exif"===re(n,i+4,4)){var l=n.getUint16(c);if(((r=18761===l)||19789===l)&&42===n.getUint16(c+2,r)){var h=n.getUint32(c+4,r);h>=8&&(o=c+h)}}}if(o){var d,u,p=n.getUint16(o,r);for(u=0;u<p;u+=1)if(d=o+12*u+2,274===n.getUint16(d,r)){d+=8,e=n.getUint16(d,r),n.setUint16(d,1,r);break}}}catch(t){e=1}return e}function ce(t){var e=0,n=1,r=1;switch(t){case 2:n=-1;break;case 3:e=-180;break;case 4:r=-1;break;case 5:e=90,r=-1;break;case 6:e=90;break;case 7:e=90,n=-1;break;case 8:e=-90}return{rotate:e,scaleX:n,scaleY:r}}var le={render:function(){this.initContainer(),this.initCanvas(),this.initCropBox(),this.renderCanvas(),this.cropped&&this.renderCropBox()},initContainer:function(){var t=this.element,e=this.options,n=this.container,r=this.cropper,i=Number(e.minContainerWidth),o=Number(e.minContainerHeight);Dt(r,L),Pt(t,L);var a={width:Math.max(n.offsetWidth,i>=0?i:ht),height:Math.max(n.offsetHeight,o>=0?o:dt)};this.containerData=a,Ot(r,{width:a.width,height:a.height}),Dt(t,L),Pt(r,L)},initCanvas:function(){var t=this.containerData,e=this.imageData,n=this.options.viewMode,r=Math.abs(e.rotate)%180==90,i=r?e.naturalHeight:e.naturalWidth,o=r?e.naturalWidth:e.naturalHeight,a=i/o,s=t.width,c=t.height;t.height*a>t.width?3===n?s=t.height*a:c=t.width/a:3===n?c=t.width/a:s=t.height*a;var l={aspectRatio:a,naturalWidth:i,naturalHeight:o,width:s,height:c};this.canvasData=l,this.limited=1===n||2===n,this.limitCanvas(!0,!0),l.width=Math.min(Math.max(l.width,l.minWidth),l.maxWidth),l.height=Math.min(Math.max(l.height,l.minHeight),l.maxHeight),l.left=(t.width-l.width)/2,l.top=(t.height-l.height)/2,l.oldLeft=l.left,l.oldTop=l.top,this.initialCanvasData=Ct({},l)},limitCanvas:function(t,e){var n=this.options,r=this.containerData,i=this.canvasData,o=this.cropBoxData,a=n.viewMode,s=i.aspectRatio,c=this.cropped&&o;if(t){var l=Number(n.minCanvasWidth)||0,h=Number(n.minCanvasHeight)||0;a>1?(l=Math.max(l,r.width),h=Math.max(h,r.height),3===a&&(h*s>l?l=h*s:h=l/s)):a>0&&(l?l=Math.max(l,c?o.width:0):h?h=Math.max(h,c?o.height:0):c&&(l=o.width,(h=o.height)*s>l?l=h*s:h=l/s));var d=Jt({aspectRatio:s,width:l,height:h});l=d.width,h=d.height,i.minWidth=l,i.minHeight=h,i.maxWidth=1/0,i.maxHeight=1/0}if(e)if(a>(c?0:1)){var u=r.width-i.width,p=r.height-i.height;i.minLeft=Math.min(0,u),i.minTop=Math.min(0,p),i.maxLeft=Math.max(0,u),i.maxTop=Math.max(0,p),c&&this.limited&&(i.minLeft=Math.min(o.left,o.left+(o.width-i.width)),i.minTop=Math.min(o.top,o.top+(o.height-i.height)),i.maxLeft=o.left,i.maxTop=o.top,2===a&&(i.width>=r.width&&(i.minLeft=Math.min(0,u),i.maxLeft=Math.max(0,u)),i.height>=r.height&&(i.minTop=Math.min(0,p),i.maxTop=Math.max(0,p))))}else i.minLeft=-i.width,i.minTop=-i.height,i.maxLeft=r.width,i.maxTop=r.height},renderCanvas:function(t,e){var n=this.canvasData,r=this.imageData;if(e){var i=te({width:r.naturalWidth*Math.abs(r.scaleX||1),height:r.naturalHeight*Math.abs(r.scaleY||1),degree:r.rotate||0}),o=i.width,a=i.height,s=n.width*(o/n.naturalWidth),c=n.height*(a/n.naturalHeight);n.left-=(s-n.width)/2,n.top-=(c-n.height)/2,n.width=s,n.height=c,n.aspectRatio=o/a,n.naturalWidth=o,n.naturalHeight=a,this.limitCanvas(!0,!1)}(n.width>n.maxWidth||n.width<n.minWidth)&&(n.left=n.oldLeft),(n.height>n.maxHeight||n.height<n.minHeight)&&(n.top=n.oldTop),n.width=Math.min(Math.max(n.width,n.minWidth),n.maxWidth),n.height=Math.min(Math.max(n.height,n.minHeight),n.maxHeight),this.limitCanvas(!1,!0),n.left=Math.min(Math.max(n.left,n.minLeft),n.maxLeft),n.top=Math.min(Math.max(n.top,n.minTop),n.maxTop),n.oldLeft=n.left,n.oldTop=n.top,Ot(this.canvas,Ct({width:n.width,height:n.height},Vt({translateX:n.left,translateY:n.top}))),this.renderImage(t),this.cropped&&this.limited&&this.limitCropBox(!0,!0)},renderImage:function(t){var e=this.canvasData,n=this.imageData,r=n.naturalWidth*(e.width/e.naturalWidth),i=n.naturalHeight*(e.height/e.naturalHeight);Ct(n,{width:r,height:i,left:(e.width-r)/2,top:(e.height-i)/2}),Ot(this.image,Ct({width:n.width,height:n.height},Vt(Ct({translateX:n.left,translateY:n.top},n)))),t&&this.output()},initCropBox:function(){var t=this.options,e=this.canvasData,n=t.aspectRatio||t.initialAspectRatio,r=Number(t.autoCropArea)||.8,i={width:e.width,height:e.height};n&&(e.height*n>e.width?i.height=i.width/n:i.width=i.height*n),this.cropBoxData=i,this.limitCropBox(!0,!0),i.width=Math.min(Math.max(i.width,i.minWidth),i.maxWidth),i.height=Math.min(Math.max(i.height,i.minHeight),i.maxHeight),i.width=Math.max(i.minWidth,i.width*r),i.height=Math.max(i.minHeight,i.height*r),i.left=e.left+(e.width-i.width)/2,i.top=e.top+(e.height-i.height)/2,i.oldLeft=i.left,i.oldTop=i.top,this.initialCropBoxData=Ct({},i)},limitCropBox:function(t,e){var n=this.options,r=this.containerData,i=this.canvasData,o=this.cropBoxData,a=this.limited,s=n.aspectRatio;if(t){var c=Number(n.minCropBoxWidth)||0,l=Number(n.minCropBoxHeight)||0,h=a?Math.min(r.width,i.width,i.width+i.left,r.width-i.left):r.width,d=a?Math.min(r.height,i.height,i.height+i.top,r.height-i.top):r.height;c=Math.min(c,r.width),l=Math.min(l,r.height),s&&(c&&l?l*s>c?l=c/s:c=l*s:c?l=c/s:l&&(c=l*s),d*s>h?d=h/s:h=d*s),o.minWidth=Math.min(c,h),o.minHeight=Math.min(l,d),o.maxWidth=h,o.maxHeight=d}e&&(a?(o.minLeft=Math.max(0,i.left),o.minTop=Math.max(0,i.top),o.maxLeft=Math.min(r.width,i.left+i.width)-o.width,o.maxTop=Math.min(r.height,i.top+i.height)-o.height):(o.minLeft=0,o.minTop=0,o.maxLeft=r.width-o.width,o.maxTop=r.height-o.height))},renderCropBox:function(){var t=this.options,e=this.containerData,n=this.cropBoxData;(n.width>n.maxWidth||n.width<n.minWidth)&&(n.left=n.oldLeft),(n.height>n.maxHeight||n.height<n.minHeight)&&(n.top=n.oldTop),n.width=Math.min(Math.max(n.width,n.minWidth),n.maxWidth),n.height=Math.min(Math.max(n.height,n.minHeight),n.maxHeight),this.limitCropBox(!1,!0),n.left=Math.min(Math.max(n.left,n.minLeft),n.maxLeft),n.top=Math.min(Math.max(n.top,n.minTop),n.maxTop),n.oldLeft=n.left,n.oldTop=n.top,t.movable&&t.cropBoxMovable&&Bt(this.face,N,n.width>=e.width&&n.height>=e.height?M:b),Ot(this.cropBox,Ct({width:n.width,height:n.height},Vt({translateX:n.left,translateY:n.top}))),this.cropped&&this.limited&&this.limitCanvas(!0,!0),this.disabled||this.output()},output:function(){this.preview(),Wt(this.element,G,this.getData())}},he={initPreview:function(){var t=this.element,e=this.crossOrigin,n=this.options.preview,r=e?this.crossOriginUrl:this.url,i=t.alt||"The image to preview",o=document.createElement("img");if(e&&(o.crossOrigin=e),o.src=r,o.alt=i,this.viewBox.appendChild(o),this.viewBoxImage=o,n){var a=n;"string"==typeof n?a=t.ownerDocument.querySelectorAll(n):n.querySelector&&(a=[n]),this.previews=a,xt(a,(function(t){var n=document.createElement("img");Bt(t,z,{width:t.offsetWidth,height:t.offsetHeight,html:t.innerHTML}),e&&(n.crossOrigin=e),n.src=r,n.alt=i,n.style.cssText='display:block;width:100%;height:auto;min-width:0!important;min-height:0!important;max-width:none!important;max-height:none!important;image-orientation:0deg!important;"',t.innerHTML="",t.appendChild(n)}))}},resetPreview:function(){xt(this.previews,(function(t){var e=Rt(t,z);Ot(t,{width:e.width,height:e.height}),t.innerHTML=e.html,At(t,z)}))},preview:function(){var t=this.imageData,e=this.canvasData,n=this.cropBoxData,r=n.width,i=n.height,o=t.width,a=t.height,s=n.left-e.left-t.left,c=n.top-e.top-t.top;this.cropped&&!this.disabled&&(Ot(this.viewBoxImage,Ct({width:o,height:a},Vt(Ct({translateX:-s,translateY:-c},t)))),xt(this.previews,(function(e){var n=Rt(e,z),l=n.width,h=n.height,d=l,u=h,p=1;r&&(u=i*(p=l/r)),i&&u>h&&(d=r*(p=h/i),u=h),Ot(e,{width:d,height:u}),Ot(e.getElementsByTagName("img")[0],Ct({width:o*p,height:a*p},Vt(Ct({translateX:-s*p,translateY:-c*p},t))))})))}},de={bind:function(){var t=this.element,e=this.options,n=this.cropper;_t(e.cropstart)&&Ht(t,q,e.cropstart),_t(e.cropmove)&&Ht(t,Y,e.cropmove),_t(e.cropend)&&Ht(t,U,e.cropend),_t(e.crop)&&Ht(t,G,e.crop),_t(e.zoom)&&Ht(t,it,e.zoom),Ht(n,Q,this.onCropStart=this.cropStart.bind(this)),e.zoomable&&e.zoomOnWheel&&Ht(n,rt,this.onWheel=this.wheel.bind(this),{passive:!1,capture:!0}),e.toggleDragModeOnDblclick&&Ht(n,X,this.onDblclick=this.dblclick.bind(this)),Ht(t.ownerDocument,J,this.onCropMove=this.cropMove.bind(this)),Ht(t.ownerDocument,tt,this.onCropEnd=this.cropEnd.bind(this)),e.responsive&&Ht(window,nt,this.onResize=this.resize.bind(this))},unbind:function(){var t=this.element,e=this.options,n=this.cropper;_t(e.cropstart)&&Ft(t,q,e.cropstart),_t(e.cropmove)&&Ft(t,Y,e.cropmove),_t(e.cropend)&&Ft(t,U,e.cropend),_t(e.crop)&&Ft(t,G,e.crop),_t(e.zoom)&&Ft(t,it,e.zoom),Ft(n,Q,this.onCropStart),e.zoomable&&e.zoomOnWheel&&Ft(n,rt,this.onWheel,{passive:!1,capture:!0}),e.toggleDragModeOnDblclick&&Ft(n,X,this.onDblclick),Ft(t.ownerDocument,J,this.onCropMove),Ft(t.ownerDocument,tt,this.onCropEnd),e.responsive&&Ft(window,nt,this.onResize)}},ue={resize:function(){if(!this.disabled){var t,e,n=this.options,r=this.container,i=this.containerData,o=r.offsetWidth/i.width,a=r.offsetHeight/i.height,s=Math.abs(o-1)>Math.abs(a-1)?o:a;1!==s&&(n.restore&&(t=this.getCanvasData(),e=this.getCropBoxData()),this.render(),n.restore&&(this.setCanvasData(xt(t,(function(e,n){t[n]=e*s}))),this.setCropBoxData(xt(e,(function(t,n){e[n]=t*s})))))}},dblclick:function(){this.disabled||this.options.dragMode===W||this.setDragMode($t(this.dragBox,P)?H:F)},wheel:function(t){var e=this,n=Number(this.options.wheelZoomRatio)||.1,r=1;this.disabled||(t.preventDefault(),this.wheeling||(this.wheeling=!0,setTimeout((function(){e.wheeling=!1}),50),t.deltaY?r=t.deltaY>0?1:-1:t.wheelDelta?r=-t.wheelDelta/120:t.detail&&(r=t.detail>0?1:-1),this.zoom(-r*n,t)))},cropStart:function(t){var e=t.buttons,n=t.button;if(!(this.disabled||("mousedown"===t.type||"pointerdown"===t.type&&"mouse"===t.pointerType)&&(mt(e)&&1!==e||mt(n)&&0!==n||t.ctrlKey))){var r,i=this.options,o=this.pointers;t.changedTouches?xt(t.changedTouches,(function(t){o[t.identifier]=Zt(t)})):o[t.pointerId||0]=Zt(t),r=Object.keys(o).length>1&&i.zoomable&&i.zoomOnTouch?k:Rt(t.target,N),at.test(r)&&!1!==Wt(this.element,q,{originalEvent:t,action:r})&&(t.preventDefault(),this.action=r,this.cropping=!1,r===_&&(this.cropping=!0,Dt(this.dragBox,B)))}},cropMove:function(t){var e=this.action;if(!this.disabled&&e){var n=this.pointers;t.preventDefault(),!1!==Wt(this.element,Y,{originalEvent:t,action:e})&&(t.changedTouches?xt(t.changedTouches,(function(t){Ct(n[t.identifier]||{},Zt(t,!0))})):Ct(n[t.pointerId||0]||{},Zt(t,!0)),this.change(t))}},cropEnd:function(t){if(!this.disabled){var e=this.action,n=this.pointers;t.changedTouches?xt(t.changedTouches,(function(t){delete n[t.identifier]})):delete n[t.pointerId||0],e&&(t.preventDefault(),Object.keys(n).length||(this.action=""),this.cropping&&(this.cropping=!1,Tt(this.dragBox,B,this.cropped&&this.options.modal)),Wt(this.element,U,{originalEvent:t,action:e}))}}},pe={change:function(t){var e,n=this.options,r=this.canvasData,i=this.containerData,o=this.cropBoxData,a=this.pointers,s=this.action,c=n.aspectRatio,l=o.left,h=o.top,d=o.width,u=o.height,p=l+d,f=h+u,m=0,v=0,g=i.width,y=i.height,w=!0;!c&&t.shiftKey&&(c=d&&u?d/u:1),this.limited&&(m=o.minLeft,v=o.minTop,g=m+Math.min(i.width,r.width,r.left+r.width),y=v+Math.min(i.height,r.height,r.top+r.height));var P=a[Object.keys(a)[0]],T={x:P.endX-P.startX,y:P.endY-P.startY},I=function(t){switch(t){case x:p+T.x>g&&(T.x=g-p);break;case C:l+T.x<m&&(T.x=m-l);break;case S:h+T.y<v&&(T.y=v-h);break;case j:f+T.y>y&&(T.y=y-f)}};switch(s){case b:l+=T.x,h+=T.y;break;case x:if(T.x>=0&&(p>=g||c&&(h<=v||f>=y))){w=!1;break}I(x),(d+=T.x)<0&&(s=C,l-=d=-d),c&&(u=d/c,h+=(o.height-u)/2);break;case S:if(T.y<=0&&(h<=v||c&&(l<=m||p>=g))){w=!1;break}I(S),u-=T.y,h+=T.y,u<0&&(s=j,h-=u=-u),c&&(d=u*c,l+=(o.width-d)/2);break;case C:if(T.x<=0&&(l<=m||c&&(h<=v||f>=y))){w=!1;break}I(C),d-=T.x,l+=T.x,d<0&&(s=x,l-=d=-d),c&&(u=d/c,h+=(o.height-u)/2);break;case j:if(T.y>=0&&(f>=y||c&&(l<=m||p>=g))){w=!1;break}I(j),(u+=T.y)<0&&(s=S,h-=u=-u),c&&(d=u*c,l+=(o.width-d)/2);break;case E:if(c){if(T.y<=0&&(h<=v||p>=g)){w=!1;break}I(S),u-=T.y,h+=T.y,d=u*c}else I(S),I(x),T.x>=0?p<g?d+=T.x:T.y<=0&&h<=v&&(w=!1):d+=T.x,T.y<=0?h>v&&(u-=T.y,h+=T.y):(u-=T.y,h+=T.y);d<0&&u<0?(s=D,h-=u=-u,l-=d=-d):d<0?(s=O,l-=d=-d):u<0&&(s=$,h-=u=-u);break;case O:if(c){if(T.y<=0&&(h<=v||l<=m)){w=!1;break}I(S),u-=T.y,h+=T.y,d=u*c,l+=o.width-d}else I(S),I(C),T.x<=0?l>m?(d-=T.x,l+=T.x):T.y<=0&&h<=v&&(w=!1):(d-=T.x,l+=T.x),T.y<=0?h>v&&(u-=T.y,h+=T.y):(u-=T.y,h+=T.y);d<0&&u<0?(s=$,h-=u=-u,l-=d=-d):d<0?(s=E,l-=d=-d):u<0&&(s=D,h-=u=-u);break;case D:if(c){if(T.x<=0&&(l<=m||f>=y)){w=!1;break}I(C),d-=T.x,l+=T.x,u=d/c}else I(j),I(C),T.x<=0?l>m?(d-=T.x,l+=T.x):T.y>=0&&f>=y&&(w=!1):(d-=T.x,l+=T.x),T.y>=0?f<y&&(u+=T.y):u+=T.y;d<0&&u<0?(s=E,h-=u=-u,l-=d=-d):d<0?(s=$,l-=d=-d):u<0&&(s=O,h-=u=-u);break;case $:if(c){if(T.x>=0&&(p>=g||f>=y)){w=!1;break}I(x),u=(d+=T.x)/c}else I(j),I(x),T.x>=0?p<g?d+=T.x:T.y>=0&&f>=y&&(w=!1):d+=T.x,T.y>=0?f<y&&(u+=T.y):u+=T.y;d<0&&u<0?(s=O,h-=u=-u,l-=d=-d):d<0?(s=D,l-=d=-d):u<0&&(s=E,h-=u=-u);break;case M:this.move(T.x,T.y),w=!1;break;case k:this.zoom(Kt(a),t),w=!1;break;case _:if(!T.x||!T.y){w=!1;break}e=Gt(this.cropper),l=P.startX-e.left,h=P.startY-e.top,d=o.minWidth,u=o.minHeight,T.x>0?s=T.y>0?$:E:T.x<0&&(l-=d,s=T.y>0?D:O),T.y<0&&(h-=u),this.cropped||(Pt(this.cropBox,L),this.cropped=!0,this.limited&&this.limitCropBox(!0,!0))}w&&(o.width=d,o.height=u,o.left=l,o.top=h,this.action=s,this.renderCropBox()),xt(a,(function(t){t.startX=t.endX,t.startY=t.endY}))}},fe={crop:function(){return!this.ready||this.cropped||this.disabled||(this.cropped=!0,this.limitCropBox(!0,!0),this.options.modal&&Dt(this.dragBox,B),Pt(this.cropBox,L),this.setCropBoxData(this.initialCropBoxData)),this},reset:function(){return this.ready&&!this.disabled&&(this.imageData=Ct({},this.initialImageData),this.canvasData=Ct({},this.initialCanvasData),this.cropBoxData=Ct({},this.initialCropBoxData),this.renderCanvas(),this.cropped&&this.renderCropBox()),this},clear:function(){return this.cropped&&!this.disabled&&(Ct(this.cropBoxData,{left:0,top:0,width:0,height:0}),this.cropped=!1,this.renderCropBox(),this.limitCanvas(!0,!0),this.renderCanvas(),Pt(this.dragBox,B),Dt(this.cropBox,L)),this},replace:function(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return!this.disabled&&t&&(this.isImg&&(this.element.src=t),e?(this.url=t,this.image.src=t,this.ready&&(this.viewBoxImage.src=t,xt(this.previews,(function(e){e.getElementsByTagName("img")[0].src=t})))):(this.isImg&&(this.replaced=!0),this.options.data=null,this.uncreate(),this.load(t))),this},enable:function(){return this.ready&&this.disabled&&(this.disabled=!1,Pt(this.cropper,T)),this},disable:function(){return this.ready&&!this.disabled&&(this.disabled=!0,Dt(this.cropper,T)),this},destroy:function(){var t=this.element;return t[w]?(t[w]=void 0,this.isImg&&this.replaced&&(t.src=this.originalUrl),this.uncreate(),this):this},move:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:t,n=this.canvasData,r=n.left,i=n.top;return this.moveTo(gt(t)?t:r+Number(t),gt(e)?e:i+Number(e))},moveTo:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:t,n=this.canvasData,r=!1;return t=Number(t),e=Number(e),this.ready&&!this.disabled&&this.options.movable&&(mt(t)&&(n.left=t,r=!0),mt(e)&&(n.top=e,r=!0),r&&this.renderCanvas(!0)),this},zoom:function(t,e){var n=this.canvasData;return t=(t=Number(t))<0?1/(1-t):1+t,this.zoomTo(n.width*t/n.naturalWidth,null,e)},zoomTo:function(t,e,n){var r=this.options,i=this.canvasData,o=i.width,a=i.height,s=i.naturalWidth,c=i.naturalHeight;if((t=Number(t))>=0&&this.ready&&!this.disabled&&r.zoomable){var l=s*t,h=c*t;if(!1===Wt(this.element,it,{ratio:t,oldRatio:o/s,originalEvent:n}))return this;if(n){var d=this.pointers,u=Gt(this.cropper),p=d&&Object.keys(d).length?Qt(d):{pageX:n.pageX,pageY:n.pageY};i.left-=(l-o)*((p.pageX-u.left-i.left)/o),i.top-=(h-a)*((p.pageY-u.top-i.top)/a)}else bt(e)&&mt(e.x)&&mt(e.y)?(i.left-=(l-o)*((e.x-i.left)/o),i.top-=(h-a)*((e.y-i.top)/a)):(i.left-=(l-o)/2,i.top-=(h-a)/2);i.width=l,i.height=h,this.renderCanvas(!0)}return this},rotate:function(t){return this.rotateTo((this.imageData.rotate||0)+Number(t))},rotateTo:function(t){return mt(t=Number(t))&&this.ready&&!this.disabled&&this.options.rotatable&&(this.imageData.rotate=t%360,this.renderCanvas(!0,!0)),this},scaleX:function(t){var e=this.imageData.scaleY;return this.scale(t,mt(e)?e:1)},scaleY:function(t){var e=this.imageData.scaleX;return this.scale(mt(e)?e:1,t)},scale:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:t,n=this.imageData,r=!1;return t=Number(t),e=Number(e),this.ready&&!this.disabled&&this.options.scalable&&(mt(t)&&(n.scaleX=t,r=!0),mt(e)&&(n.scaleY=e,r=!0),r&&this.renderCanvas(!0,!0)),this},getData:function(){var t,e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],n=this.options,r=this.imageData,i=this.canvasData,o=this.cropBoxData;if(this.ready&&this.cropped){t={x:o.left-i.left,y:o.top-i.top,width:o.width,height:o.height};var a=r.width/r.naturalWidth;if(xt(t,(function(e,n){t[n]=e/a})),e){var s=Math.round(t.y+t.height),c=Math.round(t.x+t.width);t.x=Math.round(t.x),t.y=Math.round(t.y),t.width=c-t.x,t.height=s-t.y}}else t={x:0,y:0,width:0,height:0};return n.rotatable&&(t.rotate=r.rotate||0),n.scalable&&(t.scaleX=r.scaleX||1,t.scaleY=r.scaleY||1),t},setData:function(t){var e=this.options,n=this.imageData,r=this.canvasData,i={};if(this.ready&&!this.disabled&&bt(t)){var o=!1;e.rotatable&&mt(t.rotate)&&t.rotate!==n.rotate&&(n.rotate=t.rotate,o=!0),e.scalable&&(mt(t.scaleX)&&t.scaleX!==n.scaleX&&(n.scaleX=t.scaleX,o=!0),mt(t.scaleY)&&t.scaleY!==n.scaleY&&(n.scaleY=t.scaleY,o=!0)),o&&this.renderCanvas(!0,!0);var a=n.width/n.naturalWidth;mt(t.x)&&(i.left=t.x*a+r.left),mt(t.y)&&(i.top=t.y*a+r.top),mt(t.width)&&(i.width=t.width*a),mt(t.height)&&(i.height=t.height*a),this.setCropBoxData(i)}return this},getContainerData:function(){return this.ready?Ct({},this.containerData):{}},getImageData:function(){return this.sized?Ct({},this.imageData):{}},getCanvasData:function(){var t=this.canvasData,e={};return this.ready&&xt(["left","top","width","height","naturalWidth","naturalHeight"],(function(n){e[n]=t[n]})),e},setCanvasData:function(t){var e=this.canvasData,n=e.aspectRatio;return this.ready&&!this.disabled&&bt(t)&&(mt(t.left)&&(e.left=t.left),mt(t.top)&&(e.top=t.top),mt(t.width)?(e.width=t.width,e.height=t.width/n):mt(t.height)&&(e.height=t.height,e.width=t.height*n),this.renderCanvas(!0)),this},getCropBoxData:function(){var t,e=this.cropBoxData;return this.ready&&this.cropped&&(t={left:e.left,top:e.top,width:e.width,height:e.height}),t||{}},setCropBoxData:function(t){var e,n,r=this.cropBoxData,i=this.options.aspectRatio;return this.ready&&this.cropped&&!this.disabled&&bt(t)&&(mt(t.left)&&(r.left=t.left),mt(t.top)&&(r.top=t.top),mt(t.width)&&t.width!==r.width&&(e=!0,r.width=t.width),mt(t.height)&&t.height!==r.height&&(n=!0,r.height=t.height),i&&(e?r.height=r.width/i:n&&(r.width=r.height*i)),this.renderCropBox()),this},getCroppedCanvas:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(!this.ready||!window.HTMLCanvasElement)return null;var e=this.canvasData,n=ee(this.image,this.imageData,e,t);if(!this.cropped)return n;var r=this.getData(t.rounded),i=r.x,o=r.y,a=r.width,s=r.height,c=n.width/Math.floor(e.naturalWidth);1!==c&&(i*=c,o*=c,a*=c,s*=c);var h=a/s,d=Jt({aspectRatio:h,width:t.maxWidth||1/0,height:t.maxHeight||1/0}),u=Jt({aspectRatio:h,width:t.minWidth||0,height:t.minHeight||0},"cover"),p=Jt({aspectRatio:h,width:t.width||(1!==c?n.width:a),height:t.height||(1!==c?n.height:s)}),f=p.width,m=p.height;f=Math.min(d.width,Math.max(u.width,f)),m=Math.min(d.height,Math.max(u.height,m));var v=document.createElement("canvas"),g=v.getContext("2d");v.width=St(f),v.height=St(m),g.fillStyle=t.fillColor||"transparent",g.fillRect(0,0,f,m);var y=t.imageSmoothingEnabled,w=void 0===y||y,b=t.imageSmoothingQuality;g.imageSmoothingEnabled=w,b&&(g.imageSmoothingQuality=b);var _,M,k,x,C,j,S=n.width,E=n.height,O=i,$=o;O<=-a||O>S?(O=0,_=0,k=0,C=0):O<=0?(k=-O,O=0,C=_=Math.min(S,a+O)):O<=S&&(k=0,C=_=Math.min(a,S-O)),_<=0||$<=-s||$>E?($=0,M=0,x=0,j=0):$<=0?(x=-$,$=0,j=M=Math.min(E,s+$)):$<=E&&(x=0,j=M=Math.min(s,E-$));var D=[O,$,_,M];if(C>0&&j>0){var P=f/a;D.push(k*P,x*P,C*P,j*P)}return g.drawImage.apply(g,[n].concat(l(D.map((function(t){return Math.floor(St(t))}))))),v},setAspectRatio:function(t){var e=this.options;return this.disabled||gt(t)||(e.aspectRatio=Math.max(0,t)||NaN,this.ready&&(this.initCropBox(),this.cropped&&this.renderCropBox())),this},setDragMode:function(t){var e=this.options,n=this.dragBox,r=this.face;if(this.ready&&!this.disabled){var i=t===F,o=e.movable&&t===H;t=i||o?t:W,e.dragMode=t,Bt(n,N,t),Tt(n,P,i),Tt(n,A,o),e.cropBoxMovable||(Bt(r,N,t),Tt(r,P,i),Tt(r,A,o))}return this}},me=v.Cropper,ve=function(){function t(e){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(o(this,t),!e||!lt.test(e.tagName))throw new Error("The first argument is required and must be an <img> or <canvas> element.");this.element=e,this.options=Ct({},ut,bt(n)&&n),this.cropped=!1,this.disabled=!1,this.pointers={},this.ready=!1,this.reloading=!1,this.replaced=!1,this.sized=!1,this.sizing=!1,this.init()}return s(t,[{key:"init",value:function(){var t,e=this.element,n=e.tagName.toLowerCase();if(!e[w]){if(e[w]=this,"img"===n){if(this.isImg=!0,t=e.getAttribute("src")||"",this.originalUrl=t,!t)return;t=e.src}else"canvas"===n&&window.HTMLCanvasElement&&(t=e.toDataURL());this.load(t)}}},{key:"load",value:function(t){var e=this;if(t){this.url=t,this.imageData={};var n=this.element,r=this.options;if(r.rotatable||r.scalable||(r.checkOrientation=!1),r.checkOrientation&&window.ArrayBuffer)if(st.test(t))ct.test(t)?this.read(oe(t)):this.clone();else{var i=new XMLHttpRequest,o=this.clone.bind(this);this.reloading=!0,this.xhr=i,i.onabort=o,i.onerror=o,i.ontimeout=o,i.onprogress=function(){i.getResponseHeader("content-type")!==ot&&i.abort()},i.onload=function(){e.read(i.response)},i.onloadend=function(){e.reloading=!1,e.xhr=null},r.checkCrossOrigin&&qt(t)&&n.crossOrigin&&(t=Xt(t)),i.open("GET",t,!0),i.responseType="arraybuffer",i.withCredentials="use-credentials"===n.crossOrigin,i.send()}else this.clone()}}},{key:"read",value:function(t){var e=this.options,n=this.imageData,r=se(t),i=0,o=1,a=1;if(r>1){this.url=ae(t,ot);var s=ce(r);i=s.rotate,o=s.scaleX,a=s.scaleY}e.rotatable&&(n.rotate=i),e.scalable&&(n.scaleX=o,n.scaleY=a),this.clone()}},{key:"clone",value:function(){var t=this.element,e=this.url,n=t.crossOrigin,r=e;this.options.checkCrossOrigin&&qt(e)&&(n||(n="anonymous"),r=Xt(e)),this.crossOrigin=n,this.crossOriginUrl=r;var i=document.createElement("img");n&&(i.crossOrigin=n),i.src=r||e,i.alt=t.alt||"The image to crop",this.image=i,i.onload=this.start.bind(this),i.onerror=this.stop.bind(this),Dt(i,I),t.parentNode.insertBefore(i,t.nextSibling)}},{key:"start",value:function(){var t=this,e=this.image;e.onload=null,e.onerror=null,this.sizing=!0;var n=v.navigator&&/(?:iPad|iPhone|iPod).*?AppleWebKit/i.test(v.navigator.userAgent),r=function(e,n){Ct(t.imageData,{naturalWidth:e,naturalHeight:n,aspectRatio:e/n}),t.initialImageData=Ct({},t.imageData),t.sizing=!1,t.sized=!0,t.build()};if(!e.naturalWidth||n){var i=document.createElement("img"),o=document.body||document.documentElement;this.sizingImage=i,i.onload=function(){r(i.width,i.height),n||o.removeChild(i)},i.src=e.src,n||(i.style.cssText="left:0;max-height:none!important;max-width:none!important;min-height:0!important;min-width:0!important;opacity:0;position:absolute;top:0;z-index:-1;",o.appendChild(i))}else r(e.naturalWidth,e.naturalHeight)}},{key:"stop",value:function(){var t=this.image;t.onload=null,t.onerror=null,t.parentNode.removeChild(t),this.image=null}},{key:"build",value:function(){if(this.sized&&!this.ready){var t=this.element,e=this.options,n=this.image,r=t.parentNode,i=document.createElement("div");i.innerHTML=pt;var o=i.querySelector(".".concat(w,"-container")),a=o.querySelector(".".concat(w,"-canvas")),s=o.querySelector(".".concat(w,"-drag-box")),c=o.querySelector(".".concat(w,"-crop-box")),l=c.querySelector(".".concat(w,"-face"));this.container=r,this.cropper=o,this.canvas=a,this.dragBox=s,this.cropBox=c,this.viewBox=o.querySelector(".".concat(w,"-view-box")),this.face=l,a.appendChild(n),Dt(t,L),r.insertBefore(o,t.nextSibling),Pt(n,I),this.initPreview(),this.bind(),e.initialAspectRatio=Math.max(0,e.initialAspectRatio)||NaN,e.aspectRatio=Math.max(0,e.aspectRatio)||NaN,e.viewMode=Math.max(0,Math.min(3,Math.round(e.viewMode)))||0,Dt(c,L),e.guides||Dt(c.getElementsByClassName("".concat(w,"-dashed")),L),e.center||Dt(c.getElementsByClassName("".concat(w,"-center")),L),e.background&&Dt(o,"".concat(w,"-bg")),e.highlight||Dt(l,R),e.cropBoxMovable&&(Dt(l,A),Bt(l,N,b)),e.cropBoxResizable||(Dt(c.getElementsByClassName("".concat(w,"-line")),L),Dt(c.getElementsByClassName("".concat(w,"-point")),L)),this.render(),this.ready=!0,this.setDragMode(e.dragMode),e.autoCrop&&this.crop(),this.setData(e.data),_t(e.ready)&&Ht(t,et,e.ready,{once:!0}),Wt(t,et)}}},{key:"unbuild",value:function(){if(this.ready){this.ready=!1,this.unbind(),this.resetPreview();var t=this.cropper.parentNode;t&&t.removeChild(this.cropper),Pt(this.element,L)}}},{key:"uncreate",value:function(){this.ready?(this.unbuild(),this.ready=!1,this.cropped=!1):this.sizing?(this.sizingImage.onload=null,this.sizing=!1,this.sized=!1):this.reloading?(this.xhr.onabort=null,this.xhr.abort()):this.image&&this.stop()}}],[{key:"noConflict",value:function(){return window.Cropper=me,t}},{key:"setDefaults",value:function(t){Ct(ut,bt(t)&&t)}}])}();return Ct(ve.prototype,le,he,de,ue,pe,fe),ve}()}},e={};function n(r){var i=e[r];if(void 0!==i)return i.exports;var o=e[r]={exports:{}};return t[r].call(o.exports,o,o.exports,n),o.exports}n.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return n.d(e,{a:e}),e},n.d=(t,e)=>{for(var r in e)n.o(e,r)&&!n.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:e[r]})},n.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),(()=>{"use strict";n(2555);var t=n(6294),e=n(418),r=n(8758),i=n(27);function o(t){return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},o(t)}function a(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,s(r.key),r)}}function s(t){var e=function(t,e){if("object"!=o(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=o(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==o(e)?e:e+""}var c=function(){return t=function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.group={},this.group.list=$("#rv_media_items_list").html(),this.group.tiles=$("#rv_media_items_tiles").html(),this.item={},this.item.list=$("#rv_media_items_list_element").html(),this.item.tiles=$("#rv_media_items_tiles_element").html(),this.$groupContainer=$(".rv-media-items")},n=[{key:"renderData",value:function(t){var n,i=arguments.length>1&&void 0!==arguments[1]&&arguments[1],o=arguments.length>2&&void 0!==arguments[2]&&arguments[2],a=this,s=e.M.getConfigs(),c=a.group[e.M.getRequestParams().view_type],l=e.M.getRequestParams().view_in;switch(e.M.inArray(["all_media","public","trash","favorites","recent"],l)||(l="all_media"),l){case"all_media":n='<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">\n                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>\n                    <path d="M4 17v2a2 2 0 0 0 2 2h12a2 2 0 0 0 2 -2v-2"></path>\n                    <path d="M7 9l5 -5l5 5"></path>\n                    <path d="M12 4l0 12"></path>\n                </svg>';break;case"public":n='<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">\n                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>\n                    <path d="M20 11a8.1 8.1 0 0 0 -15.5 -2m-.5 -4v4h4"></path>\n                    <path d="M4 13a8.1 8.1 0 0 0 15.5 2m.5 4v-4h-4"></path>\n                </svg>';break;case"trash":n='<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">\n                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>\n                    <path d="M4 7l16 0"></path>\n                    <path d="M10 11l0 6"></path>\n                    <path d="M14 11l0 6"></path>\n                    <path d="M5 7l1 12a2 2 0 0 0 2 2h8a2 2 0 0 0 2 -2l1 -12"></path>\n                    <path d="M9 7v-3a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v3"></path>\n                </svg>';break;case"favorites":n='<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">\n                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>\n                    <path d="M8.243 7.34l-6.38 .925l-.113 .023a1 1 0 0 0 -.44 1.684l4.622 4.499l-1.09 6.355l-.013 .11a1 1 0 0 0 1.464 .944l5.706 -3l5.693 3l.1 .046a1 1 0 0 0 1.352 -1.1l-1.091 -6.355l4.624 -4.5l.078 -.085a1 1 0 0 0 -.633 -1.62l-6.38 -.926l-2.852 -5.78a1 1 0 0 0 -1.794 0l-2.853 5.78z" stroke-width="0" fill="currentColor"></path>\n                </svg>';break;case"recent":n='<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">\n                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>\n                    <path d="M3 12a9 9 0 1 0 18 0a9 9 0 0 0 -18 0"></path>\n                    <path d="M12 7v5l3 3"></path>\n                </svg>'}c=c.replace(/__noItemIcon__/gi,n).replace(/__noItemTitle__/gi,e.M.trans("no_item.".concat(l,".title"))||"").replace(/__noItemMessage__/gi,e.M.trans("no_item.".concat(l,".message"))||"");var h=$(c),d=h.find("ul");o&&this.$groupContainer.find(".rv-media-grid ul").length>0&&(d=this.$groupContainer.find(".rv-media-grid ul")),e.M.size(t.folders)>0||e.M.size(t.files)>0||o?$(".rv-media-items").addClass("has-items"):$(".rv-media-items").removeClass("has-items"),e.M.forEach(t.folders,(function(t){var n=a.item[e.M.getRequestParams().view_type];n=n.replace(/__type__/gi,"folder").replace(/__id__/gi,t.id).replace(/__name__/gi,t.name||"").replace(/__size__/gi,"").replace(/__date__/gi,t.created_at||"").replace(/__thumb__/gi,'<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">\n                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>\n                    <path d="M5 4h4l3 3h7a2 2 0 0 1 2 2v8a2 2 0 0 1 -2 2h-14a2 2 0 0 1 -2 -2v-11a2 2 0 0 1 2 -2"></path>\n                </svg>');var r=$(n);e.M.forEach(t,(function(t,e){r.data(e,t)})),r.data("is_folder",!0),r.data("icon",s.icons.folder),r.find(".rv-media-thumbnail").css("color",t.color),d.append(r)})),e.M.forEach(t.files,(function(t){var n=a.item[e.M.getRequestParams().view_type];n=n.replace(/__type__/gi,"file").replace(/__id__/gi,t.id).replace(/__name__/gi,t.name||"").replace(/__size__/gi,t.size||"").replace(/__date__/gi,t.created_at||""),n="list"===e.M.getRequestParams().view_type?n.replace(/__thumb__/gi,t.icon):n.replace(/__thumb__/gi,t.thumb?'<img src="'.concat(t.thumb?t.thumb:t.full_url,'" alt="').concat(t.name,'">'):t.icon);var r=$(n);r.data("is_folder",!1),e.M.forEach(t,(function(t,e){r.data(e,t)})),d.append(r)})),!1!==i&&a.$groupContainer.empty(),o&&this.$groupContainer.find(".rv-media-grid ul").length>0||a.$groupContainer.append(h),a.$groupContainer.find(".loading-spinner").remove(),r.d.handleDropdown(),$(".js-media-list-title[data-id=".concat(t.selected_file_id,"]")).trigger("click")}}],n&&a(t.prototype,n),i&&a(t,i),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,n,i}();function l(t){return l="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},l(t)}function h(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,d(r.key),r)}}function d(t){var e=function(t,e){if("object"!=l(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=l(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==l(e)?e:e+""}var u=function(){return t=function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.$detailsWrapper=$(".rv-media-main .rv-media-details"),this.descriptionItemTemplate='<div class="mb-3 rv-media-name">\n            <label class="form-label">__title__</label>\n            __url__\n        </div>',this.onlyFields=["name","alt","full_url","size","mime_type","created_at","updated_at","nothing_selected"]},(n=[{key:"renderData",value:function(t){var n=this,r=this,i="image"===t.type&&t.full_url?'<img src="'.concat(t.full_url,'" alt="').concat(t.name,'">'):t.icon,o="";e.M.forEach(t,(function(t,n){e.M.inArray(r.onlyFields,n)&&t&&(e.M.inArray(["mime_type"],n)||(o+=r.descriptionItemTemplate.replace(/__title__/gi,e.M.trans(n)).replace(/__url__/gi,t?"full_url"===n?'<div class="input-group pe-1">\n                                        <input type="text" id="file_details_url" class="form-control" value="'.concat(t,'" />\n                                        <button class="input-group-text btn btn-default js-btn-copy-to-clipboard" type="button"\n                                                data-bb-toggle="clipboard"\n                                                data-clipboard-action="copy"\n                                                data-clipboard-message="Copied"\n                                                data-clipboard-target="#file_details_url"\n                                        >\n                                            <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-clipboard me-0" data-clipboard-icon="true" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">\n                                               <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>\n                                               <path d="M9 5h-2a2 2 0 0 0 -2 2v12a2 2 0 0 0 2 2h10a2 2 0 0 0 2 -2v-12a2 2 0 0 0 -2 -2h-2"></path>\n                                               <path d="M9 3m0 2a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v0a2 2 0 0 1 -2 2h-2a2 2 0 0 1 -2 -2z"></path>\n                                            </svg>\n                                            <svg class="icon text-success me-0 d-none" data-clipboard-success-icon="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">\n                                              <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>\n                                              <path d="M5 12l5 5l10 -10"></path>\n                                            </svg>\n                                        </button>\n                                    </div>'):'<span title="'.concat(t,'">').concat(t,"</span>"):"")))})),r.$detailsWrapper.find(".rv-media-thumbnail").html(i),r.$detailsWrapper.find(".rv-media-thumbnail").css("color",t.color),r.$detailsWrapper.find(".rv-media-description").html(o);var a="";if(t.mime_type&&-1!==t.mime_type.indexOf("image")){var s=new Image;s.src=t.full_url,s.onload=function(){a+=n.descriptionItemTemplate.replace(/__title__/gi,e.M.trans("width")).replace(/__url__/gi,'<span title="'.concat(s.width,'">').concat(s.width,"px</span>")),a+=n.descriptionItemTemplate.replace(/__title__/gi,e.M.trans("height")).replace(/__url__/gi,'<span title="'.concat(s.height,'">').concat(s.height,"px</span>")),r.$detailsWrapper.find(".rv-media-description").append(a)}}}}])&&h(t.prototype,n),r&&h(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,n,r}();function p(t){return p="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},p(t)}function f(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function m(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?f(Object(n),!0).forEach((function(e){v(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):f(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function v(t,e,n){return(e=y(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function g(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,y(r.key),r)}}function y(t){var e=function(t,e){if("object"!=p(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=p(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==p(e)?e:e+""}var w=function(){function n(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,n),this.MediaList=new c,this.MediaDetails=new u,this.breadcrumbTemplate=$("#rv_media_breadcrumb_item").html()}return o=n,a=[{key:"getMedia",value:function(){var i=arguments.length>0&&void 0!==arguments[0]&&arguments[0],o=arguments.length>1&&void 0!==arguments[1]&&arguments[1],a=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if(void 0!==RV_MEDIA_CONFIG.pagination){if(RV_MEDIA_CONFIG.pagination.in_process_get_media)return;RV_MEDIA_CONFIG.pagination.in_process_get_media=!0}var s=this;s.getFileDetails({icon:'<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">\n                <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>\n                <path d="M15 8h.01"></path>\n                <path d="M3 6a3 3 0 0 1 3 -3h12a3 3 0 0 1 3 3v12a3 3 0 0 1 -3 3h-12a3 3 0 0 1 -3 -3v-12z"></path>\n                <path d="M3 16l5 -5c.928 -.893 2.072 -.893 3 0l5 5"></path>\n                <path d="M14 14l1 -1c.928 -.893 2.072 -.893 3 0l3 3"></path>\n            </svg>',nothing_selected:""});var c=e.M.getRequestParams();"recent"===c.view_in&&(c=m(m({},c),{},{recent_items:t.y})),c=m(m({},c),{},!0===o?{is_popup:!0}:{is_popup:void 0}),void 0!==(c=m(m({},c),{},{onSelectFiles:void 0})).search&&""!==c.search&&void 0!==c.selected_file_id&&(c=m(m({},c),{},{selected_file_id:void 0})),c=m(m({},c),{},{load_more_file:a}),void 0!==RV_MEDIA_CONFIG.pagination&&(c=m(m({},c),{},{paged:RV_MEDIA_CONFIG.pagination.paged,posts_per_page:RV_MEDIA_CONFIG.pagination.posts_per_page})),e.M.showAjaxLoading(),$httpClient.make().get(RV_MEDIA_URL.get_media,c).then((function(t){var e=t.data;s.MediaList.renderData(e.data,i,a),s.renderBreadcrumbs(e.data.breadcrumbs),n.refreshFilter(),r.d.renderActions(),void 0!==RV_MEDIA_CONFIG.pagination&&(void 0!==RV_MEDIA_CONFIG.pagination.paged&&(RV_MEDIA_CONFIG.pagination.paged+=1),void 0!==RV_MEDIA_CONFIG.pagination.in_process_get_media&&(RV_MEDIA_CONFIG.pagination.in_process_get_media=!1),void 0!==RV_MEDIA_CONFIG.pagination.posts_per_page&&e.data.files.length+e.data.folders.length<RV_MEDIA_CONFIG.pagination.posts_per_page&&void 0!==RV_MEDIA_CONFIG.pagination.has_more&&(RV_MEDIA_CONFIG.pagination.has_more=!1))})).finally((function(){return e.M.hideAjaxLoading()}))}},{key:"getFileDetails",value:function(t){this.MediaDetails.renderData(t)}},{key:"renderBreadcrumbs",value:function(t){var n=this,r=$(".rv-media-breadcrumb .breadcrumb");r.find("li").remove(),e.M.each(t,(function(t){var e=n.breadcrumbTemplate;e=e.replace(/__name__/gi,t.name||"").replace(/__icon__/gi,(null==t?void 0:t.icon)||'<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">\n                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>\n                    <path d="M5 4h4l3 3h7a2 2 0 0 1 2 2v8a2 2 0 0 1 -2 2h-14a2 2 0 0 1 -2 -2v-11a2 2 0 0 1 2 -2"></path>\n                </svg>').replace(/__folderId__/gi,t.id||0),r.append($(e))})),$(".rv-media-container").attr("data-breadcrumb-count",e.M.size(t))}}],s=[{key:"refreshFilter",value:function(){var t=$(".rv-media-container"),n=e.M.getRequestParams().view_in,r=$('.rv-media-actions .btn:not([data-type="refresh"]):not([data-bs-toggle="offcanvas"])');"all_media"===n||e.M.getRequestParams().folder_id?(r.removeClass("disabled"),t.attr("data-allow-upload","true")):(r.addClass("disabled"),t.attr("data-allow-upload","false")),$(".rv-media-actions .btn.js-rv-media-change-filter-group").removeClass("disabled");var o=$('.rv-media-actions .btn[data-action="empty_trash"]');o.hide(),"trash"===n&&e.M.size(e.M.getItems())>0&&o.removeClass("d-none disabled").show(),i.K.destroyContext(),i.K.initContext(),t.attr("data-view-in",n)}}],a&&g(o.prototype,a),s&&g(o,s),Object.defineProperty(o,"prototype",{writable:!1}),o;var o,a,s}(),b=n(1994);function _(t){return _="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},_(t)}function M(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,k(r.key),r)}}function k(t){var e=function(t,e){if("object"!=_(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=_(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==_(e)?e:e+""}var x=function(){function n(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,n),this.MediaService=new w,$(document).on("shown.bs.modal","#modal_add_folder",(function(t){$(t.currentTarget).find("form input[type=text]").focus()}))}return r=n,o=[{key:"closeModal",value:function(){$(document).find("#modal_add_folder").modal("hide")}}],(i=[{key:"create",value:function(t){var r=this;$httpClient.make().withButtonLoading($(document).find("#modal_add_folder button[type=submit]")).post(RV_MEDIA_URL.create_folder,{parent_id:e.M.getRequestParams().folder_id,name:t}).then((function(t){var i=t.data;b.b.showMessage("success",i.message,e.M.trans("message.success_header")),e.M.resetPagination(),r.MediaService.getMedia(!0),n.closeModal()}))}},{key:"changeFolder",value:function(n){t.T.request_params.folder_id=n,e.M.storeConfig(),this.MediaService.getMedia(!0)}}])&&M(r.prototype,i),o&&M(r,o),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,i,o}();function C(t){return C="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},C(t)}function j(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function S(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?j(Object(n),!0).forEach((function(e){E(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):j(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function E(t,e,n){return(e=D(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function O(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,D(r.key),r)}}function D(t){var e=function(t,e){if("object"!=C(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=C(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==C(e)?e:e+""}var P=function(){function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.$body=$("body"),this.dropZone=null,this.uploadUrl=RV_MEDIA_URL.upload_file,this.uploadProgressBox=$(".rv-upload-progress"),this.uploadProgressContainer=$(".rv-upload-progress .rv-upload-progress-table"),this.uploadProgressTemplate=$("#rv_media_upload_progress_item").html(),this.totalQueued=1,this.MediaService=new w,this.totalError=0}return n=t,i=[{key:"formatFileSize",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1]?1e3:1024;if(Math.abs(t)<e)return t+" B";var n=["KB","MB","GB","TB","PB","EB","ZB","YB"],r=-1;do{t/=e,++r}while(Math.abs(t)>=e&&r<n.length-1);return t.toFixed(1)+" "+n[r]}}],(r=[{key:"init",value:function(){e.M.hasPermission("files.create")&&$(".rv-media-items").length>0&&this.setupDropZone(),this.handleEvents()}},{key:"setupDropZone",value:function(){var t=this,n=this.getDropZoneConfig();t.filesUpload=0,t.dropZone&&t.dropZone.destroy(),t.dropZone=new Dropzone(document.querySelector(".rv-media-items"),S(S({},n),{},{thumbnailWidth:!1,thumbnailHeight:!1,parallelUploads:1,autoQueue:!0,clickable:".js-dropzone-upload",previewsContainer:!1,sending:function(t,n,r){r.append("_token",$('meta[name="csrf-token"]').attr("content")),r.append("folder_id",e.M.getRequestParams().folder_id),r.append("view_in",e.M.getRequestParams().view_in),r.append("path",t.fullPath)},chunksUploaded:function(e,n){t.uploadProgressContainer.find(".progress-percent").html("100%"),n()},accept:function(e,n){t.filesUpload++,t.totalError=0,n()},uploadprogress:function(e,n,r){var i=r/e.size*100;e.upload.chunked&&i>99&&(i-=1);var o=(i>100?"100":parseInt(i))+"%";t.uploadProgressContainer.find("tr").eq(e.index-1).find(".progress-percent").html(o)}})),t.dropZone.on("addedfile",(function(e){e.index=t.totalQueued,t.totalQueued++})),t.dropZone.on("sending",(function(e){t.initProgress(e.name,e.size)})),t.dropZone.on("complete",(function(e){e.accepted&&t.changeProgressStatus(e),t.filesUpload=0})),t.dropZone.on("queuecomplete",(function(){e.M.resetPagination(),t.MediaService.getMedia(!0),0===t.totalError&&setTimeout((function(){$(".rv-upload-progress .close-pane").trigger("click")}),5e3)}))}},{key:"handleEvents",value:function(){var t=this;t.$body.off("click",".rv-upload-progress .close-pane").on("click",".rv-upload-progress .close-pane",(function(e){e.preventDefault(),$(".rv-upload-progress").addClass("hide-the-pane"),t.totalError=0,setTimeout((function(){$(".rv-upload-progress tr").remove(),t.totalQueued=1}),300)}))}},{key:"initProgress",value:function(e,n){var r=this.uploadProgressTemplate.replace(/__fileName__/gi,e).replace(/__fileSize__/gi,t.formatFileSize(n)).replace(/__status__/gi,"warning").replace(/__message__/gi,"Uploading...");this.checkUploadTotalProgress()&&this.uploadProgressContainer.find("tr").length>=1||(this.uploadProgressContainer.append(r),this.uploadProgressBox.removeClass("hide-the-pane"),this.uploadProgressBox.find(".table").animate({scrollTop:this.uploadProgressContainer.height()},150))}},{key:"changeProgressStatus",value:function(t){var n=this,r=n.uploadProgressContainer.find("tr:nth-child(".concat(t.index,")")),i=r.find(".file-status"),o=e.M.jsonDecode(t.xhr.responseText||"",{});if(n.totalError=n.totalError+(!0===o.error||"error"===t.status?1:0),i.removeClass("text-success text-danger text-warning"),i.addClass(!0===o.error||"error"===t.status?"text-danger":"text-success"),i.html(!0===o.error||"error"===t.status?"Error":"Uploaded"),"error"===t.status)if(422===t.xhr.status){var a="";$.each(o.errors,(function(t,e){a+='<span class="text-danger">'.concat(e,"</span><br>")})),r.find(".file-error").html(a)}else 500===t.xhr.status&&r.find(".file-error").html('<span class="text-danger">'.concat(t.xhr.statusText,"</span>"));else o.error?r.find(".file-error").html('<span class="text-danger">'.concat(o.message,"</span>")):(e.M.addToRecent(o.data.id),e.M.setSelectedFile(o.data.id))}},{key:"getDropZoneConfig",value:function(){return{url:this.uploadUrl,uploadMultiple:!RV_MEDIA_CONFIG.chunk.enabled,chunking:RV_MEDIA_CONFIG.chunk.enabled,forceChunking:!0,parallelChunkUploads:!1,chunkSize:RV_MEDIA_CONFIG.chunk.chunk_size,retryChunks:!0,retryChunksLimit:3,timeout:0,maxFilesize:RV_MEDIA_CONFIG.chunk.max_file_size,maxFiles:null}}},{key:"checkUploadTotalProgress",value:function(){return 1===this.filesUpload}}])&&O(n.prototype,r),i&&O(n,i),Object.defineProperty(n,"prototype",{writable:!1}),n;var n,r,i}();function T(t){return T="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},T(t)}function L(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */L=function(){return e};var t,e={},n=Object.prototype,r=n.hasOwnProperty,i=Object.defineProperty||function(t,e,n){t[e]=n.value},o="function"==typeof Symbol?Symbol:{},a=o.iterator||"@@iterator",s=o.asyncIterator||"@@asyncIterator",c=o.toStringTag||"@@toStringTag";function l(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{l({},"")}catch(t){l=function(t,e,n){return t[e]=n}}function h(t,e,n,r){var o=e&&e.prototype instanceof g?e:g,a=Object.create(o.prototype),s=new $(r||[]);return i(a,"_invoke",{value:j(t,n,s)}),a}function d(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}e.wrap=h;var u="suspendedStart",p="suspendedYield",f="executing",m="completed",v={};function g(){}function y(){}function w(){}var b={};l(b,a,(function(){return this}));var _=Object.getPrototypeOf,M=_&&_(_(D([])));M&&M!==n&&r.call(M,a)&&(b=M);var k=w.prototype=g.prototype=Object.create(b);function x(t){["next","throw","return"].forEach((function(e){l(t,e,(function(t){return this._invoke(e,t)}))}))}function C(t,e){function n(i,o,a,s){var c=d(t[i],t,o);if("throw"!==c.type){var l=c.arg,h=l.value;return h&&"object"==T(h)&&r.call(h,"__await")?e.resolve(h.__await).then((function(t){n("next",t,a,s)}),(function(t){n("throw",t,a,s)})):e.resolve(h).then((function(t){l.value=t,a(l)}),(function(t){return n("throw",t,a,s)}))}s(c.arg)}var o;i(this,"_invoke",{value:function(t,r){function i(){return new e((function(e,i){n(t,r,e,i)}))}return o=o?o.then(i,i):i()}})}function j(e,n,r){var i=u;return function(o,a){if(i===f)throw Error("Generator is already running");if(i===m){if("throw"===o)throw a;return{value:t,done:!0}}for(r.method=o,r.arg=a;;){var s=r.delegate;if(s){var c=S(s,r);if(c){if(c===v)continue;return c}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(i===u)throw i=m,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);i=f;var l=d(e,n,r);if("normal"===l.type){if(i=r.done?m:p,l.arg===v)continue;return{value:l.arg,done:r.done}}"throw"===l.type&&(i=m,r.method="throw",r.arg=l.arg)}}}function S(e,n){var r=n.method,i=e.iterator[r];if(i===t)return n.delegate=null,"throw"===r&&e.iterator.return&&(n.method="return",n.arg=t,S(e,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),v;var o=d(i,e.iterator,n.arg);if("throw"===o.type)return n.method="throw",n.arg=o.arg,n.delegate=null,v;var a=o.arg;return a?a.done?(n[e.resultName]=a.value,n.next=e.nextLoc,"return"!==n.method&&(n.method="next",n.arg=t),n.delegate=null,v):a:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,v)}function E(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function O(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function $(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(E,this),this.reset(!0)}function D(e){if(e||""===e){var n=e[a];if(n)return n.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var i=-1,o=function n(){for(;++i<e.length;)if(r.call(e,i))return n.value=e[i],n.done=!1,n;return n.value=t,n.done=!0,n};return o.next=o}}throw new TypeError(T(e)+" is not iterable")}return y.prototype=w,i(k,"constructor",{value:w,configurable:!0}),i(w,"constructor",{value:y,configurable:!0}),y.displayName=l(w,c,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===y||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,w):(t.__proto__=w,l(t,c,"GeneratorFunction")),t.prototype=Object.create(k),t},e.awrap=function(t){return{__await:t}},x(C.prototype),l(C.prototype,s,(function(){return this})),e.AsyncIterator=C,e.async=function(t,n,r,i,o){void 0===o&&(o=Promise);var a=new C(h(t,n,r,i),o);return e.isGeneratorFunction(n)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},x(k),l(k,c,"Generator"),l(k,a,(function(){return this})),l(k,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),n=[];for(var r in e)n.push(r);return n.reverse(),function t(){for(;n.length;){var r=n.pop();if(r in e)return t.value=r,t.done=!1,t}return t.done=!0,t}},e.values=D,$.prototype={constructor:$,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(O),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var n=this;function i(r,i){return s.type="throw",s.arg=e,n.next=r,i&&(n.method="next",n.arg=t),!!i}for(var o=this.tryEntries.length-1;o>=0;--o){var a=this.tryEntries[o],s=a.completion;if("root"===a.tryLoc)return i("end");if(a.tryLoc<=this.prev){var c=r.call(a,"catchLoc"),l=r.call(a,"finallyLoc");if(c&&l){if(this.prev<a.catchLoc)return i(a.catchLoc,!0);if(this.prev<a.finallyLoc)return i(a.finallyLoc)}else if(c){if(this.prev<a.catchLoc)return i(a.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return i(a.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var i=this.tryEntries[n];if(i.tryLoc<=this.prev&&r.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var o=i;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=e&&e<=o.finallyLoc&&(o=null);var a=o?o.completion:{};return a.type=t,a.arg=e,o?(this.method="next",this.next=o.finallyLoc,v):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),v},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),O(n),v}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var r=n.completion;if("throw"===r.type){var i=r.arg;O(n)}return i}}throw Error("illegal catch attempt")},delegateYield:function(e,n,r){return this.delegate={iterator:D(e),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=t),v}},e}function I(t,e){var n="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!n){if(Array.isArray(t)||(n=function(t,e){if(t){if("string"==typeof t)return R(t,e);var n={}.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?R(t,e):void 0}}(t))||e&&t&&"number"==typeof t.length){n&&(t=n);var r=0,i=function(){};return{s:i,n:function(){return r>=t.length?{done:!0}:{done:!1,value:t[r++]}},e:function(t){throw t},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,a=!0,s=!1;return{s:function(){n=n.call(t)},n:function(){var t=n.next();return a=t.done,t},e:function(t){s=!0,o=t},f:function(){try{a||null==n.return||n.return()}finally{if(s)throw o}}}}function R(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}function B(t,e,n,r,i,o,a){try{var s=t[o](a),c=s.value}catch(t){return void n(t)}s.done?e(c):Promise.resolve(c).then(r,i)}function A(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,N(r.key),r)}}function N(t){var e=function(t,e){if("object"!=T(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=T(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==T(e)?e:e+""}var z=function(){function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.MediaService=new w,$(document).on("shown.bs.modal","#modal_download_url",(function(t){$(t.currentTarget).find(".form-download-url input[type=text]").focus()}))}return n=t,r=[{key:"download",value:(o=L().mark((function n(r,i,o){var a,s,c,l,h,d;return L().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:a=this,r=$.trim(r).split(/\r?\n/),s=0,c=!1,l=I(r),n.prev=5,d=L().mark((function t(){var n,a,c;return L().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:n=h.value,a="";try{a=new URL(n).pathname.split("/").pop()}catch(t){a=n}return c=i("".concat(s," / ").concat(r.length),a,n),t.next=6,new Promise((function(t,r){$httpClient.make().post(RV_MEDIA_URL.download_url,{folderId:e.M.getRequestParams().folder_id,url:n}).then((function(e){var n,r=e.data;c(!0,r.message||(null===(n=r.data)||void 0===n?void 0:n.message)),t()})).finally((function(){return o()})).catch((function(t){return r(t)}))}));case 6:s+=1;case 7:case"end":return t.stop()}}),t)})),l.s();case 8:if((h=l.n()).done){n.next=12;break}return n.delegateYield(d(),"t0",10);case 10:n.next=8;break;case 12:n.next=17;break;case 14:n.prev=14,n.t1=n.catch(5),l.e(n.t1);case 17:return n.prev=17,l.f(),n.finish(17);case 20:e.M.resetPagination(),a.MediaService.getMedia(!0),c||(t.closeModal(),b.b.showMessage("success",e.M.trans("message.success_header")));case 23:case"end":return n.stop()}}),n,this,[[5,14,17,20]])})),a=function(){var t=this,e=arguments;return new Promise((function(n,r){var i=o.apply(t,e);function a(t){B(i,n,r,a,s,"next",t)}function s(t){B(i,n,r,a,s,"throw",t)}a(void 0)}))},function(t,e,n){return a.apply(this,arguments)})}],i=[{key:"closeModal",value:function(){$(document).find("#modal_download_url").modal("hide")}}],r&&A(n.prototype,r),i&&A(n,i),Object.defineProperty(n,"prototype",{writable:!1}),n;var n,r,i,o,a}(),F=n(3702);function H(t){return H="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},H(t)}function W(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */W=function(){return e};var t,e={},n=Object.prototype,r=n.hasOwnProperty,i=Object.defineProperty||function(t,e,n){t[e]=n.value},o="function"==typeof Symbol?Symbol:{},a=o.iterator||"@@iterator",s=o.asyncIterator||"@@asyncIterator",c=o.toStringTag||"@@toStringTag";function l(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{l({},"")}catch(t){l=function(t,e,n){return t[e]=n}}function h(t,e,n,r){var o=e&&e.prototype instanceof g?e:g,a=Object.create(o.prototype),s=new $(r||[]);return i(a,"_invoke",{value:j(t,n,s)}),a}function d(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}e.wrap=h;var u="suspendedStart",p="suspendedYield",f="executing",m="completed",v={};function g(){}function y(){}function w(){}var b={};l(b,a,(function(){return this}));var _=Object.getPrototypeOf,M=_&&_(_(D([])));M&&M!==n&&r.call(M,a)&&(b=M);var k=w.prototype=g.prototype=Object.create(b);function x(t){["next","throw","return"].forEach((function(e){l(t,e,(function(t){return this._invoke(e,t)}))}))}function C(t,e){function n(i,o,a,s){var c=d(t[i],t,o);if("throw"!==c.type){var l=c.arg,h=l.value;return h&&"object"==H(h)&&r.call(h,"__await")?e.resolve(h.__await).then((function(t){n("next",t,a,s)}),(function(t){n("throw",t,a,s)})):e.resolve(h).then((function(t){l.value=t,a(l)}),(function(t){return n("throw",t,a,s)}))}s(c.arg)}var o;i(this,"_invoke",{value:function(t,r){function i(){return new e((function(e,i){n(t,r,e,i)}))}return o=o?o.then(i,i):i()}})}function j(e,n,r){var i=u;return function(o,a){if(i===f)throw Error("Generator is already running");if(i===m){if("throw"===o)throw a;return{value:t,done:!0}}for(r.method=o,r.arg=a;;){var s=r.delegate;if(s){var c=S(s,r);if(c){if(c===v)continue;return c}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(i===u)throw i=m,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);i=f;var l=d(e,n,r);if("normal"===l.type){if(i=r.done?m:p,l.arg===v)continue;return{value:l.arg,done:r.done}}"throw"===l.type&&(i=m,r.method="throw",r.arg=l.arg)}}}function S(e,n){var r=n.method,i=e.iterator[r];if(i===t)return n.delegate=null,"throw"===r&&e.iterator.return&&(n.method="return",n.arg=t,S(e,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),v;var o=d(i,e.iterator,n.arg);if("throw"===o.type)return n.method="throw",n.arg=o.arg,n.delegate=null,v;var a=o.arg;return a?a.done?(n[e.resultName]=a.value,n.next=e.nextLoc,"return"!==n.method&&(n.method="next",n.arg=t),n.delegate=null,v):a:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,v)}function E(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function O(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function $(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(E,this),this.reset(!0)}function D(e){if(e||""===e){var n=e[a];if(n)return n.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var i=-1,o=function n(){for(;++i<e.length;)if(r.call(e,i))return n.value=e[i],n.done=!1,n;return n.value=t,n.done=!0,n};return o.next=o}}throw new TypeError(H(e)+" is not iterable")}return y.prototype=w,i(k,"constructor",{value:w,configurable:!0}),i(w,"constructor",{value:y,configurable:!0}),y.displayName=l(w,c,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===y||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,w):(t.__proto__=w,l(t,c,"GeneratorFunction")),t.prototype=Object.create(k),t},e.awrap=function(t){return{__await:t}},x(C.prototype),l(C.prototype,s,(function(){return this})),e.AsyncIterator=C,e.async=function(t,n,r,i,o){void 0===o&&(o=Promise);var a=new C(h(t,n,r,i),o);return e.isGeneratorFunction(n)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},x(k),l(k,c,"Generator"),l(k,a,(function(){return this})),l(k,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),n=[];for(var r in e)n.push(r);return n.reverse(),function t(){for(;n.length;){var r=n.pop();if(r in e)return t.value=r,t.done=!1,t}return t.done=!0,t}},e.values=D,$.prototype={constructor:$,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(O),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var n=this;function i(r,i){return s.type="throw",s.arg=e,n.next=r,i&&(n.method="next",n.arg=t),!!i}for(var o=this.tryEntries.length-1;o>=0;--o){var a=this.tryEntries[o],s=a.completion;if("root"===a.tryLoc)return i("end");if(a.tryLoc<=this.prev){var c=r.call(a,"catchLoc"),l=r.call(a,"finallyLoc");if(c&&l){if(this.prev<a.catchLoc)return i(a.catchLoc,!0);if(this.prev<a.finallyLoc)return i(a.finallyLoc)}else if(c){if(this.prev<a.catchLoc)return i(a.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return i(a.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var i=this.tryEntries[n];if(i.tryLoc<=this.prev&&r.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var o=i;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=e&&e<=o.finallyLoc&&(o=null);var a=o?o.completion:{};return a.type=t,a.arg=e,o?(this.method="next",this.next=o.finallyLoc,v):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),v},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),O(n),v}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var r=n.completion;if("throw"===r.type){var i=r.arg;O(n)}return i}}throw Error("illegal catch attempt")},delegateYield:function(e,n,r){return this.delegate={iterator:D(e),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=t),v}},e}function G(t,e,n,r,i,o,a){try{var s=t[o](a),c=s.value}catch(t){return void n(t)}s.done?e(c):Promise.resolve(c).then(r,i)}function U(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,Y(r.key),r)}}function Y(t){var e=function(t,e){if("object"!=H(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=H(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==H(e)?e:e+""}var q=function(){return n=function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.MediaService=new w,this.UploadService=new P,this.FolderService=new x,this.DownloadService=new z,this.$body=$("body")},i=[{key:"init",value:function(){e.M.resetPagination(),this.setupLayout(),this.handleMediaList(),this.changeViewType(),this.changeFilter(),this.search(),this.handleActions(),this.UploadService.init(),this.handleModals(),this.scrollGetMore()}},{key:"setupLayout",value:function(){var n=$('.js-rv-media-change-filter[data-type="filter"][data-value="'.concat(e.M.getRequestParams().filter,'"]'));n.closest("button.dropdown-item").addClass("active").closest(".dropdown").find(".js-rv-media-filter-current").html("(".concat(n.html(),")"));var r=$('.js-rv-media-change-filter[data-type="view_in"][data-value="'.concat(e.M.getRequestParams().view_in,'"]'));r.closest("button.dropdown-item").addClass("active").closest(".dropdown").find(".js-rv-media-filter-current").html("(".concat(r.html(),")")),e.M.isUseInModal()&&$(".rv-media-footer").removeClass("d-none"),$('.js-rv-media-change-filter[data-type="sort_by"][data-value="'.concat(e.M.getRequestParams().sort_by,'"]')).closest("button.dropdown-item").addClass("active");var i=$("#media_details_collapse");i.prop("checked",t.T.hide_details_pane||!1),setTimeout((function(){$(".rv-media-details").show()}),300),i.on("change",(function(n){n.preventDefault(),t.T.hide_details_pane=$(n.currentTarget).is(":checked"),e.M.storeConfig()})),$(document).on("click",".js-download-action",(function(t){t.preventDefault(),$("#modal_download_url").modal("show")})),$(document).on("click",".js-create-folder-action",(function(t){t.preventDefault(),$("#modal_add_folder").modal("show")}))}},{key:"handleMediaList",value:function(){var t=this,n=!1,i=!1,o=!1;$(document).on("keyup keydown",(function(t){n=t.ctrlKey,i=t.metaKey,o=t.shiftKey})),t.$body.off("click",".js-media-list-title").on("click",".js-media-list-title",(function(a){a.preventDefault();var s=$(a.currentTarget);if(o){var c=e.M.arrayFirst(e.M.getSelectedItems());if(c){var l=c.index_key,h=s.index();$(".rv-media-items li").each((function(t,e){t>l&&t<=h&&$(e).find("input[type=checkbox]").prop("checked",!0)}))}}else n||i||s.closest(".rv-media-items").find("input[type=checkbox]").prop("checked",!1);s.find("input[type=checkbox]").prop("checked",!0),r.d.handleDropdown(),t.MediaService.getFileDetails(s.data())})).on("dblclick doubletap",".js-media-list-title",(function(n){n.preventDefault();var i=$(n.currentTarget).data();if(!0===i.is_folder)e.M.resetPagination(),t.FolderService.changeFolder(i.id);else if(e.M.isUseInModal()){if("trash"!==e.M.getConfigs().request_params.view_in){var o=e.M.getSelectedFiles();e.M.size(o)>0&&F.x.editorSelectFile(o)}}else r.d.handlePreview();return!1})).on("dblclick doubletap",".js-up-one-level",(function(t){t.preventDefault();var e=$(".rv-media-breadcrumb .breadcrumb li").length;$(".rv-media-breadcrumb .breadcrumb li:nth-child(".concat(e-1,") a")).trigger("click")})).on("contextmenu",".js-context-menu",(function(t){$(t.currentTarget).find("input[type=checkbox]").is(":checked")||$(t.currentTarget).trigger("click")})).on("click contextmenu",".rv-media-items",(function(n){e.M.size(n.target.closest(".js-context-menu"))||($('.rv-media-items input[type="checkbox"]').prop("checked",!1),r.d.handleDropdown(),t.MediaService.getFileDetails({icon:'<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">\n                            <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>\n                            <path d="M15 8h.01"></path>\n                            <path d="M3 6a3 3 0 0 1 3 -3h12a3 3 0 0 1 3 3v12a3 3 0 0 1 -3 3h-12a3 3 0 0 1 -3 -3v-12z"></path>\n                            <path d="M3 16l5 -5c.928 -.893 2.072 -.893 3 0l5 5"></path>\n                            <path d="M14 14l1 -1c.928 -.893 2.072 -.893 3 0l3 3"></path>\n                        </svg>',nothing_selected:""}))}))}},{key:"changeViewType",value:function(){var n=this;n.$body.off("click",".js-rv-media-change-view-type button").on("click",".js-rv-media-change-view-type button",(function(r){r.preventDefault();var i=$(r.currentTarget);i.hasClass("active")||(i.closest(".js-rv-media-change-view-type").find("button").removeClass("active"),i.addClass("active"),t.T.request_params.view_type=i.data("type"),"trash"===i.data("type")?$(document).find(".js-insert-to-editor").prop("disabled",!0):$(document).find(".js-insert-to-editor").prop("disabled",!1),e.M.storeConfig(),void 0!==RV_MEDIA_CONFIG.pagination&&void 0!==RV_MEDIA_CONFIG.pagination.paged&&(RV_MEDIA_CONFIG.pagination.paged=1),n.MediaService.getMedia(!0,!1))})),$('.js-rv-media-change-view-type .btn[data-type="'.concat(e.M.getRequestParams().view_type,'"]')).trigger("click"),this.bindIntegrateModalEvents()}},{key:"changeFilter",value:function(){var n=this;n.$body.off("click",".js-rv-media-change-filter").on("click",".js-rv-media-change-filter",(function(r){if(r.preventDefault(),!e.M.isOnAjaxLoading()){var i=$(r.currentTarget),o=i.data();t.T.request_params[o.type]=o.value,window.rvMedia.options&&"view_in"===o.type&&(window.rvMedia.options.view_in=o.value),"view_in"===o.type&&(t.T.request_params.folder_id=0,"trash"===o.value?$(document).find(".js-insert-to-editor").prop("disabled",!0):$(document).find(".js-insert-to-editor").prop("disabled",!1)),i.closest(".dropdown").find(".js-rv-media-filter-current").html("(".concat(i.html(),")")),e.M.storeConfig(),w.refreshFilter(),e.M.resetPagination(),n.MediaService.getMedia(!0),i.addClass("active"),i.siblings().removeClass("active")}}))}},{key:"search",value:function(){var n=this;$('.input-search-wrapper input[type="text"]').val(e.M.getRequestParams().search||""),n.$body.off("submit",".input-search-wrapper").on("submit",".input-search-wrapper",(function(r){r.preventDefault(),t.T.request_params.search=$(r.currentTarget).find('input[name="search"]').val(),e.M.storeConfig(),e.M.resetPagination(),n.MediaService.getMedia(!0)}))}},{key:"handleActions",value:function(){var t=this;t.$body.off("click",'.rv-media-actions .js-change-action[data-type="refresh"]').on("click",'.rv-media-actions .js-change-action[data-type="refresh"]',(function(n){n.preventDefault(),e.M.resetPagination();var r=void 0!==window.rvMedia.$el?window.rvMedia.$el.data("rv-media"):void 0;void 0!==r&&r.length>0&&void 0!==r[0].selected_file_id?t.MediaService.getMedia(!0,!0):t.MediaService.getMedia(!0,!1)})).off("click",".rv-media-items li.no-items").on("click",".rv-media-items li.no-items",(function(t){t.preventDefault(),$(".rv-media-header .rv-media-top-header .rv-media-actions .js-dropzone-upload").trigger("click")})).off("submit",".form-add-folder").on("submit",".form-add-folder",(function(e){e.preventDefault();var n=$(e.currentTarget).find('input[name="name"]'),r=n.val();return t.FolderService.create(r),n.val(""),!1})).off("click",".js-change-folder").on("click",".js-change-folder",(function(n){n.preventDefault();var r=$(n.currentTarget).data("folder");e.M.resetPagination(),t.FolderService.changeFolder(r)})).off("click",".js-files-action").on("click",".js-files-action",(function(n){n.preventDefault(),r.d.handleGlobalAction($(n.currentTarget).data("action"),(function(){e.M.resetPagination(),t.MediaService.getMedia(!0)}))})).off("submit",".form-download-url").on("submit",".form-download-url",function(){var e,n=(e=W().mark((function e(n){var r,i,o,a,s,c,l,h;return W().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n.preventDefault(),r=$("#modal_download_url"),i=r.find("#download-form-wrapper"),o=r.find("#modal-notice").empty(),a=r.find(".modal-title"),s=r.find('textarea[name="urls"]').prop("disabled",!0),c=r.find('[type="submit"]'),l=s.val(),h=[],Botble.showButtonLoading(c),i.slideUp(),e.next=13,t.DownloadService.download(l,(function(t,e,n){var r=$('\n                        <div class="p-2 text-primary">\n                            <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">\n                                <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>\n                                <path d="M3 12a9 9 0 1 0 18 0a9 9 0 0 0 -18 0"></path>\n                                <path d="M12 9h.01"></path>\n                                <path d="M11 12h1v4h1"></path>\n                            </svg>\n                            <span>'.concat(e,"</span>\n                        </div>\n                    "));return o.append(r).scrollTop(o[0].scrollHeight),a.html('<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">\n                            <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>\n                            <path d="M4 17v2a2 2 0 0 0 2 2h12a2 2 0 0 0 2 -2v-2"></path>\n                            <path d="M7 11l5 5l5 -5"></path>\n                            <path d="M12 4l0 12"></path>\n                        </svg>\n                        '.concat(a.data("downloading")," (").concat(t,")")),function(t){var i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";t||h.push(n),r.find("span").text("".concat(e,": ").concat(i)),r.attr("class","py-2 text-".concat(t?"success":"danger")).find("i").attr("class",t?"icon ti ti-check-circle":"icon ti ti-x-circle")}}),(function(){i.slideDown(),s.val(h.join("\n")).prop("disabled",!1),a.html('<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">\n                            <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>\n                            <path d="M4 17v2a2 2 0 0 0 2 2h12a2 2 0 0 0 2 -2v-2"></path>\n                            <path d="M7 11l5 5l5 -5"></path>\n                            <path d="M12 4l0 12"></path>\n                        </svg>\n                        '.concat(a.data("text"),"\n                    ")),Botble.hideButtonLoading(c)}));case 13:return e.abrupt("return",!1);case 14:case"end":return e.stop()}}),e)})),function(){var t=this,n=arguments;return new Promise((function(r,i){var o=e.apply(t,n);function a(t){G(o,r,i,a,s,"next",t)}function s(t){G(o,r,i,a,s,"throw",t)}a(void 0)}))});return function(t){return n.apply(this,arguments)}}())}},{key:"handleModals",value:function(){var n=this;n.$body.on("show.bs.modal","#modal_rename_items",(function(){r.d.renderRenameItems()})),n.$body.on("show.bs.modal","#modal_alt_text_items",(function(){r.d.renderAltTextItems()})),n.$body.on("show.bs.modal","#modal_share_items",(function(){r.d.renderShareItems()})),n.$body.on("change",'#modal_share_items select[data-bb-value="share-type"]',(function(){r.d.renderShareItems()})),n.$body.on("show.bs.modal","#modal_crop_image",(function(){r.d.renderCropImage()})),n.$body.on("show.bs.modal","#modal-properties",(function(t){if(1===e.M.getSelectedItems().length){var n=$(t.currentTarget),r=e.M.getSelectedItems()[0];n.find('input[name="color"][value="'.concat(r.color,'"]')).prop("checked",!0)}})),n.$body.on("hidden.bs.modal","#modal_download_url",(function(){var t=$("#modal_download_url");t.find("textarea").val(""),t.find("#modal-notice").empty()})),n.$body.off("click",'#modal-properties button[type="submit"]').on("click",'#modal-properties button[type="submit"]',(function(t){t.preventDefault();var i=$(t.currentTarget).closest(".modal");Botble.showButtonLoading(i.find('button[type="submit"]')),r.d.processAction({action:"properties",color:i.find('input[name="color"]:checked').val(),selected:e.M.getSelectedItems().map((function(t){return t.id.toString()}))},(function(){i.modal("hide"),Botble.hideButtonLoading(i.find('button[type="submit"]')),n.MediaService.getMedia(!0)}))})),n.$body.off("submit","#modal_crop_image .form-crop").on("submit","#modal_crop_image .form-crop",(function(t){t.preventDefault();var e=$(t.currentTarget);Botble.showButtonLoading(e.find('button[type="submit"]'));var i=e.find('input[name="image_id"]').val(),o=e.find('input[name="crop_data"]').val();r.d.processAction({action:e.data("action"),imageId:i,cropData:o},(function(t){t.error||(e.closest(".modal").modal("hide"),n.MediaService.getMedia(!0)),Botble.hideButtonLoading(e.find('button[type="submit"]'))}))})),n.$body.off("submit","#modal_rename_items .form-rename").on("submit","#modal_rename_items .form-rename",(function(t){t.preventDefault();var i=[],o=$(t.currentTarget);$("#modal_rename_items .form-control").each((function(t,e){var n=$(e),r=n.closest(".mb-3").data();r.name=n.val(),i.push(r)})),Botble.showButtonLoading(o.find('button[type="submit"]')),r.d.processAction({action:o.data("action"),selected:i},(function(t){t.error?$("#modal_rename_items .mb-3").each((function(n,r){var i=$(r);e.M.inArray(t.data,i.data("id"))?i.addClass("has-error"):i.removeClass("has-error")})):(o.closest(".modal").modal("hide"),n.MediaService.getMedia(!0)),Botble.hideButtonLoading(o.find('button[type="submit"]'))}))})),n.$body.off("submit","#modal_alt_text_items .form-alt-text").on("submit","#modal_alt_text_items .form-alt-text",(function(t){t.preventDefault();var i=[],o=$(t.currentTarget);$("#modal_alt_text_items .form-control").each((function(t,e){var n=$(e),r=n.closest(".mb-3").data();r.alt=n.val(),i.push(r)})),Botble.showButtonLoading(o.find('button[type="submit"]')),r.d.processAction({action:o.data("action"),selected:i},(function(t){t.error?$("#modal_alt_text_items .mb-3").each((function(n,r){var i=$(r);e.M.inArray(t.data,i.data("id"))?i.addClass("has-error"):i.removeClass("has-error")})):(o.closest(".modal").modal("hide"),n.MediaService.getMedia(!0)),Botble.hideButtonLoading(o.find('button[type="submit"]'))}))})),n.$body.off("submit","form.form-delete-items").on("submit","form.form-delete-items",(function(t){t.preventDefault();var i=[],o=$(t.currentTarget);Botble.showButtonLoading(o.find('button[type="submit"]')),e.M.each(e.M.getSelectedItems(),(function(t){i.push({id:t.id,is_folder:t.is_folder})})),r.d.processAction({action:o.data("action"),selected:i},(function(t){o.closest(".modal").modal("hide"),t.error||n.MediaService.getMedia(!0),Botble.hideButtonLoading(o.find('button[type="submit"]'))}))})),n.$body.off("submit","#modal_empty_trash .form-empty-trash").on("submit","#modal_empty_trash .form-empty-trash",(function(t){t.preventDefault();var e=$(t.currentTarget);Botble.showButtonLoading(e.find('button[type="submit"]')),r.d.processAction({action:e.data("action")},(function(){e.closest(".modal").modal("hide"),n.MediaService.getMedia(!0),Botble.hideButtonLoading(e.find('button[type="submit"]'))}))})),"trash"===t.T.request_params.view_in?$(document).find(".js-insert-to-editor").prop("disabled",!0):$(document).find(".js-insert-to-editor").prop("disabled",!1),this.bindIntegrateModalEvents()}},{key:"checkFileTypeSelect",value:function(t){if(void 0!==window.rvMedia.$el){var n=e.M.arrayFirst(t),r=window.rvMedia.$el.data("rv-media");if(void 0!==r&&void 0!==r[0]&&void 0!==r[0].file_type&&"undefined"!==n&&"undefined"!==n.type){if(!r[0].file_type.match(n.type))return!1;if(void 0!==r[0].ext_allowed&&$.isArray(r[0].ext_allowed)&&-1===$.inArray(n.mime_type,r[0].ext_allowed))return!1}}return!0}},{key:"bindIntegrateModalEvents",value:function(){var t=$("#rv_media_modal"),n=this;t.off("click",".js-insert-to-editor").on("click",".js-insert-to-editor",(function(r){r.preventDefault();var i=e.M.getSelectedFiles();e.M.size(i)>0&&(window.rvMedia.options.onSelectFiles(i,window.rvMedia.$el),n.checkFileTypeSelect(i)&&t.find(".btn-close").trigger("click"))})),t.off("dblclick doubletap",'.js-media-list-title[data-context="file"]').on("dblclick doubletap",'.js-media-list-title[data-context="file"]',(function(i){if(i.preventDefault(),"trash"!==e.M.getConfigs().request_params.view_in){var o=e.M.getSelectedFiles();e.M.size(o)>0&&(window.rvMedia.options.onSelectFiles(o,window.rvMedia.$el),n.checkFileTypeSelect(o)&&t.find(".btn-close").trigger("click"))}else r.d.handlePreview()}))}},{key:"scrollGetMore",value:function(){var t=this;$(".rv-media-main .rv-media-items").bind("DOMMouseScroll mousewheel",(function(e){(e.originalEvent.detail>0||e.originalEvent.wheelDelta<0)&&($(e.currentTarget).closest(".media-modal").length>0?$(e.currentTarget).scrollTop()+$(e.currentTarget).innerHeight()/2>=$(e.currentTarget)[0].scrollHeight-450:$(e.currentTarget).scrollTop()+$(e.currentTarget).innerHeight()>=$(e.currentTarget)[0].scrollHeight-150)&&void 0!==RV_MEDIA_CONFIG.pagination&&RV_MEDIA_CONFIG.pagination.has_more&&t.MediaService.getMedia(!1,!1,!0)}))}}],i&&U(n.prototype,i),o&&U(n,o),Object.defineProperty(n,"prototype",{writable:!1}),n;var n,i,o}();$((function(){window.rvMedia=window.rvMedia||{},(new q).init()}))})()})();