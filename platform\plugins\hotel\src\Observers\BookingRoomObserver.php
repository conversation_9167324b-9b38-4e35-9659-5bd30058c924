<?php

namespace Botble\Hotel\Observers;

use Botble\Hotel\Models\BookingRoom;
use Botble\Hotel\Models\RoomDate;
use Botble\Hotel\Models\RoomInventory;
use Illuminate\Support\Facades\Log;

class BookingRoomObserver
{
    public function created(BookingRoom $bookingRoom): void
    {
        $this->updateRoomAvailability($bookingRoom);
    }

    public function updated(BookingRoom $bookingRoom): void
    {
        $this->updateRoomAvailability($bookingRoom);
    }

    private function updateRoomAvailability(BookingRoom $bookingRoom): void
    {
        // If no room_id_code is set, there's nothing to update
        if (!$bookingRoom->room_id_code) {
            return;
        }

        try {
            // Check if the room exists in the inventory
            $roomInventory = RoomInventory::where('room_id_code', $bookingRoom->room_id_code)
                ->first();

            if (!$roomInventory) {
                // If room doesn't exist in inventory, create it
                $roomInventory = RoomInventory::create([
                    'room_id' => $bookingRoom->room_id,
                    'room_id_code' => $bookingRoom->room_id_code,
                    'is_available' => false,
                ]);

                Log::info("Created room inventory for room_id_code: {$bookingRoom->room_id_code}");
            }

            // Create or update a record in the room_dates table to mark this room as booked
            $roomDate = RoomDate::updateOrCreate(
                [
                    'room_id' => $bookingRoom->room_id,
                    'room_id_code' => $bookingRoom->room_id_code,
                    'booking_id' => $bookingRoom->booking_id,
                ],
                [
                    'start_date' => $bookingRoom->start_date,
                    'end_date' => $bookingRoom->end_date,
                    'is_booked' => true,
                    'active' => true,
                ]
            );

            // Also update the booking model with the same room_id_code
            $booking = $bookingRoom->booking;
            if ($booking && $booking->id) {
                $booking->update([
                    'room_id_code' => $bookingRoom->room_id_code,
                ]);
                Log::info("Updated booking #{$booking->id} with room_id_code: {$bookingRoom->room_id_code}");
            }

            // Also update the RoomInventory table to mark the room as unavailable
            // $roomInventory->is_available = false;
            // $roomInventory->save();

            // Log::info("Room availability updated for booking room #{$bookingRoom->id}, room: {$bookingRoom->room_id_code}, RoomInventory updated: is_available = false");
        } catch (\Exception $e) {
            Log::error("Error updating room availability: " . $e->getMessage());
        }
    }
}
