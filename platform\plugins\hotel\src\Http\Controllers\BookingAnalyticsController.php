<?php

namespace Botble\Hotel\Http\Controllers;

use Bo<PERSON>ble\Base\Facades\Assets;
use Botble\Base\Http\Controllers\BaseController;
use Bo<PERSON>ble\Base\Http\Responses\BaseHttpResponse;
use Botble\Hotel\Enums\BookingStatusEnum;
use Botble\Hotel\Facades\HotelHelper;
use Botble\Hotel\Models\Booking;
use Botble\Hotel\Models\Customer;
use Botble\Hotel\Models\Room;
use Botble\Hotel\Models\RoomCategory;
use Botble\Payment\Enums\PaymentStatusEnum;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;
use Botble\Hotel\Exports\BookingExport;

class BookingAnalyticsController extends BaseController
{
    /**
     * Display the booking analytics dashboard
     */
    public function index(Request $request)
    {
        $this->pageTitle('Booking Analytics Dashboard');

        // Add required assets
        Assets::addScriptsDirectly([
            'vendor/core/plugins/hotel/libraries/daterangepicker/daterangepicker.js',
            'vendor/core/plugins/hotel/js/report.js',
            'vendor/core/plugins/hotel/js/chart.min.js',
            'vendor/core/plugins/hotel/js/booking-analytics.js',
        ])
        ->addStylesDirectly([
            'vendor/core/plugins/hotel/libraries/daterangepicker/daterangepicker.css',
            'vendor/core/plugins/hotel/css/report.css',
            'vendor/core/plugins/hotel/css/booking-reports.css',
        ])
        ->addScripts(['moment', 'bootstrap']);

        // Get date range from request
        [$startDate, $endDate] = HotelHelper::getDateRangeInReport($request);

        // Get room categories for filter
        $roomCategories = RoomCategory::query()->pluck('name', 'id')->toArray();

        // Get booking statuses for filter
        $bookingStatuses = BookingStatusEnum::labels();

        // Get payment statuses for filter
        $paymentStatuses = is_plugin_active('payment') ? PaymentStatusEnum::labels() : [];

        // Get filter values from request
        $roomCategoryId = $request->input('room_category_id');
        $bookingStatus = $request->input('booking_status');
        $paymentStatus = $request->input('payment_status');
        $groupBy = $request->input('group_by', 'day'); // day, week, month

        // Get booking statistics
        $totalBookings = $this->getTotalBookings($startDate, $endDate, $roomCategoryId, $bookingStatus, $paymentStatus);
        $totalCustomers = $this->getTotalCustomers($startDate, $endDate);
        $totalRooms = Room::query()->count();
        $totalRevenue = $this->getTotalRevenue($startDate, $endDate, $roomCategoryId, $bookingStatus, $paymentStatus);

        // Get booking status statistics
        $bookingStatusStats = $this->getBookingStatusStats($startDate, $endDate, $roomCategoryId);

        // Get room availability data
        $roomAvailability = $this->getRoomAvailabilityData();

        // Get chart data
        $chartData = $this->getChartData($startDate, $endDate, $groupBy, $roomCategoryId, $bookingStatus, $paymentStatus);

        // Get recent bookings
        $bookings = $this->getRecentBookings($startDate, $endDate, $roomCategoryId, $bookingStatus, $paymentStatus);

        // We'll pass this data directly to the view instead of using JavaScript facade

        return view('plugins/hotel::reports.analytics', compact(
            'startDate',
            'endDate',
            'totalBookings',
            'totalCustomers',
            'totalRooms',
            'totalRevenue',
            'bookingStatusStats',
            'roomAvailability',
            'chartData',
            'bookings',
            'roomCategories',
            'bookingStatuses',
            'paymentStatuses',
            'roomCategoryId',
            'bookingStatus',
            'paymentStatus',
            'groupBy'
        ));
    }

    /**
     * Get chart data for bookings
     */
    public function getChartData(
        Carbon $startDate,
        Carbon $endDate,
        string $groupBy = 'day',
        ?int $roomCategoryId = null,
        ?string $bookingStatus = null,
        ?string $paymentStatus = null
    ) {
        $query = Booking::query()
            ->whereDate('created_at', '>=', $startDate)
            ->whereDate('created_at', '<=', $endDate);

        // Apply filters
        if ($roomCategoryId) {
            $query->whereHas('room.room', function ($q) use ($roomCategoryId) {
                $q->where('room_category_id', $roomCategoryId);
            });
        }

        if ($bookingStatus) {
            $query->where('status', $bookingStatus);
        }

        if ($paymentStatus && is_plugin_active('payment')) {
            $query->whereHas('payment', function ($q) use ($paymentStatus) {
                $q->where('status', $paymentStatus);
            });
        }

        // Group by time period
        $format = match ($groupBy) {
            'day' => 'Y-m-d',
            'week' => 'Y-W',
            'month' => 'Y-m',
            default => 'Y-m-d',
        };

        $groupByFormat = match ($groupBy) {
            'day' => 'DATE(created_at)',
            'week' => "CONCAT(YEAR(created_at), '-', WEEK(created_at))",
            'month' => "DATE_FORMAT(created_at, '%Y-%m')",
            default => 'DATE(created_at)',
        };

        $bookingData = $query
            ->select(DB::raw("$groupByFormat as period"), DB::raw('COUNT(*) as count'), DB::raw('SUM(amount) as revenue'))
            ->groupBy('period')
            ->orderBy('period')
            ->get();

        // Prepare data for chart
        $labels = [];
        $bookingCounts = [];
        $revenue = [];

        // Generate all periods in the date range
        $period = match ($groupBy) {
            'day' => Carbon::parse($startDate)->daysUntil($endDate),
            'week' => Carbon::parse($startDate)->weeksUntil($endDate),
            'month' => Carbon::parse($startDate)->monthsUntil($endDate),
            default => Carbon::parse($startDate)->daysUntil($endDate),
        };

        foreach ($period as $date) {
            $periodKey = $date->format($format);
            $labels[] = match ($groupBy) {
                'day' => $date->format('M d'),
                'week' => 'Week ' . $date->format('W'),
                'month' => $date->format('M Y'),
                default => $date->format('M d'),
            };
            $bookingCounts[] = 0;
            $revenue[] = 0;
        }

        // Fill in the actual data
        foreach ($bookingData as $data) {
            $periodDate = match ($groupBy) {
                'day' => Carbon::parse($data->period),
                'week' => Carbon::parse($data->period)->startOfWeek(),
                'month' => Carbon::parse($data->period . '-01'),
                default => Carbon::parse($data->period),
            };

            $formattedLabel = match ($groupBy) {
                'day' => $periodDate->format('M d'),
                'week' => 'Week ' . $periodDate->format('W'),
                'month' => $periodDate->format('M Y'),
                default => $periodDate->format('M d'),
            };

            $index = array_search($formattedLabel, $labels);

            if ($index !== false) {
                $bookingCounts[$index] = (int) $data->count;
                $revenue[$index] = (float) $data->revenue;
            }
        }

        // If no data, provide a fallback with sample data
        if (empty(array_filter($bookingCounts)) && empty(array_filter($revenue))) {
            // Log the empty data situation
            \Log::info('No booking data found for the selected period. Using fallback data.');

            // Create fallback data
            $fallbackLabels = ['Day 1', 'Day 2', 'Day 3', 'Day 4', 'Day 5'];
            $fallbackBookings = [0, 0, 0, 0, 0];
            $fallbackRevenue = [0, 0, 0, 0, 0];

            return [
                'labels' => $fallbackLabels,
                'bookings' => $fallbackBookings,
                'revenue' => $fallbackRevenue,
                'is_fallback' => true
            ];
        }

        return [
            'labels' => $labels,
            'bookings' => $bookingCounts,
            'revenue' => $revenue,
            'is_fallback' => false
        ];
    }

    /**
     * Get total bookings with filters
     */
    protected function getTotalBookings(
        Carbon $startDate,
        Carbon $endDate,
        ?int $roomCategoryId = null,
        ?string $bookingStatus = null,
        ?string $paymentStatus = null
    ) {
        $query = Booking::query()
            ->whereDate('created_at', '>=', $startDate)
            ->whereDate('created_at', '<=', $endDate);

        // Apply filters
        if ($roomCategoryId) {
            $query->whereHas('room.room', function ($q) use ($roomCategoryId) {
                $q->where('room_category_id', $roomCategoryId);
            });
        }

        if ($bookingStatus) {
            $query->where('status', $bookingStatus);
        }

        if ($paymentStatus && is_plugin_active('payment')) {
            $query->whereHas('payment', function ($q) use ($paymentStatus) {
                $q->where('status', $paymentStatus);
            });
        }

        return $query->count();
    }

    /**
     * Get total customers in date range
     */
    protected function getTotalCustomers(Carbon $startDate, Carbon $endDate)
    {
        return Customer::query()
            ->whereDate('created_at', '>=', $startDate)
            ->whereDate('created_at', '<=', $endDate)
            ->count();
    }

    /**
     * Get total revenue with filters
     */
    protected function getTotalRevenue(
        Carbon $startDate,
        Carbon $endDate,
        ?int $roomCategoryId = null,
        ?string $bookingStatus = null,
        ?string $paymentStatus = null
    ) {
        $query = Booking::query()
            ->whereDate('created_at', '>=', $startDate)
            ->whereDate('created_at', '<=', $endDate);

        // Apply filters
        if ($roomCategoryId) {
            $query->whereHas('room.room', function ($q) use ($roomCategoryId) {
                $q->where('room_category_id', $roomCategoryId);
            });
        }

        if ($bookingStatus) {
            $query->where('status', $bookingStatus);
        }

        if ($paymentStatus && is_plugin_active('payment')) {
            $query->whereHas('payment', function ($q) use ($paymentStatus) {
                $q->where('status', $paymentStatus);
            });
        }

        return $query->sum('amount');
    }

    /**
     * Get booking status statistics
     */
    protected function getBookingStatusStats(Carbon $startDate, Carbon $endDate, ?int $roomCategoryId = null)
    {
        $bookingStatuses = BookingStatusEnum::labels();
        $stats = [];
        $statusColors = [
            BookingStatusEnum::PENDING => '#fbbf24',    // Amber
            BookingStatusEnum::PROCESSING => '#3b82f6', // Blue
            BookingStatusEnum::COMPLETED => '#10b981',  // Green
            BookingStatusEnum::CANCELLED => '#ef4444',  // Red
        ];

        foreach ($bookingStatuses as $status => $label) {
            $query = Booking::query()
                ->where('status', $status)
                ->whereDate('created_at', '>=', $startDate)
                ->whereDate('created_at', '<=', $endDate);

            if ($roomCategoryId) {
                $query->whereHas('room.room', function ($q) use ($roomCategoryId) {
                    $q->where('room_category_id', $roomCategoryId);
                });
            }

            $count = $query->count();

            $stats[$status] = [
                'label' => $label,
                'count' => $count,
                'color' => $statusColors[$status] ?? '#6b7280', // Default gray if status not found
            ];
        }

        return $stats;
    }

    /**
     * Get room availability data
     */
    protected function getRoomAvailabilityData()
    {
        $totalRooms = Room::query()->count();
        $bookedRooms = Booking::query()
            ->where('status', BookingStatusEnum::PROCESSING)
            ->count();

        return [
            'available' => $totalRooms - $bookedRooms,
            'booked' => $bookedRooms,
            'total' => $totalRooms,
        ];
    }

    /**
     * Get recent bookings with filters
     */
    protected function getRecentBookings(
        Carbon $startDate,
        Carbon $endDate,
        ?int $roomCategoryId = null,
        ?string $bookingStatus = null,
        ?string $paymentStatus = null
    ) {
        $query = Booking::query()
            ->with(['address', 'room.room', 'customer', 'invoice.items'])
            ->whereDate('created_at', '>=', $startDate)
            ->whereDate('created_at', '<=', $endDate);

        // Apply filters
        if ($roomCategoryId) {
            $query->whereHas('room.room', function ($q) use ($roomCategoryId) {
                $q->where('room_category_id', $roomCategoryId);
            });
        }

        if ($bookingStatus) {
            $query->where('status', $bookingStatus);
        }

        if ($paymentStatus && is_plugin_active('payment')) {
            $query->whereHas('payment', function ($q) use ($paymentStatus) {
                $q->where('status', $paymentStatus);
            });
        }

        return $query->orderBy('created_at', 'desc')->paginate(10);
    }

    /**
     * Get chart data via AJAX
     */
    public function getChartDataAjax(Request $request, BaseHttpResponse $response)
    {
        try {
            // Get date range from request
            if ($request->has('start_date') && $request->has('end_date')) {
                $startDate = Carbon::parse($request->input('start_date'));
                $endDate = Carbon::parse($request->input('end_date'));
            } else {
                [$startDate, $endDate] = HotelHelper::getDateRangeInReport($request);
            }

            // Get filter values from request
            $roomCategoryId = $request->input('room_category_id');
            $bookingStatus = $request->input('booking_status');
            $paymentStatus = $request->input('payment_status');
            $groupBy = $request->input('group_by', 'day'); // day, week, month

            // Get chart data
            $chartData = $this->getChartData($startDate, $endDate, $groupBy, $roomCategoryId, $bookingStatus, $paymentStatus);

            // Get booking status distribution
            $bookingStatusStats = $this->getBookingStatusStats($startDate, $endDate, $roomCategoryId);
            $bookingStatusData = [
                'labels' => array_column($bookingStatusStats, 'label'),
                'counts' => array_column($bookingStatusStats, 'count'),
                'colors' => array_column($bookingStatusStats, 'color'),
            ];

            // Get top room categories
            $topRoomCategories = $this->getTopRoomCategoriesData($startDate, $endDate, 5);

            // Get booking metrics
            $metrics = $this->getBookingMetricsData($startDate, $endDate, $roomCategoryId);

            // Get room availability data
            $roomAvailability = $this->getRoomAvailabilityData();

            return $response->setData([
                'chartData' => $chartData,
                'bookingStatusData' => $bookingStatusData,
                'topRoomCategories' => $topRoomCategories,
                'metrics' => $metrics,
                'roomAvailability' => $roomAvailability
            ]);
        } catch (\Exception $e) {
            \Log::error('Error in getChartDataAjax: ' . $e->getMessage());
            \Log::error($e->getTraceAsString());

            return $response
                ->setError()
                ->setMessage('Error: ' . $e->getMessage());
        }
    }

    /**
     * Get top room categories data
     */
    protected function getTopRoomCategoriesData(Carbon $startDate, Carbon $endDate, int $limit = 5)
    {
        $topCategories = Booking::query()
            ->join('ht_booking_rooms', 'ht_bookings.id', '=', 'ht_booking_rooms.booking_id')
            ->join('ht_rooms', 'ht_booking_rooms.room_id', '=', 'ht_rooms.id')
            ->join('ht_room_categories', 'ht_rooms.room_category_id', '=', 'ht_room_categories.id')
            ->select('ht_room_categories.name', DB::raw('COUNT(*) as count'), DB::raw('SUM(ht_bookings.amount) as revenue'))
            ->whereDate('ht_bookings.created_at', '>=', $startDate)
            ->whereDate('ht_bookings.created_at', '<=', $endDate)
            ->groupBy('ht_room_categories.name')
            ->orderByDesc('count')
            ->limit($limit)
            ->get();

        return [
            'labels' => $topCategories->pluck('name')->toArray(),
            'counts' => $topCategories->pluck('count')->toArray(),
            'revenue' => $topCategories->pluck('revenue')->toArray(),
        ];
    }

    /**
     * Get booking metrics data
     */
    protected function getBookingMetricsData(Carbon $startDate, Carbon $endDate, ?int $roomCategoryId = null)
    {
        // Base query
        $query = Booking::query()
            ->whereDate('created_at', '>=', $startDate)
            ->whereDate('created_at', '<=', $endDate);

        // Apply room category filter if provided
        if ($roomCategoryId) {
            $query->whereHas('room.room', function ($q) use ($roomCategoryId) {
                $q->where('room_category_id', $roomCategoryId);
            });
        }

        // Total bookings
        $totalBookings = (clone $query)->count();

        // Total revenue
        $totalRevenue = (clone $query)->sum('amount');

        // Average booking value
        $avgBookingValue = $totalBookings > 0 ? $totalRevenue / $totalBookings : 0;

        // Completed bookings
        $completedBookings = (clone $query)->where('status', BookingStatusEnum::COMPLETED)->count();

        // Completion rate
        $completionRate = $totalBookings > 0 ? ($completedBookings / $totalBookings) * 100 : 0;

        // Cancelled bookings
        $cancelledBookings = (clone $query)->where('status', BookingStatusEnum::CANCELLED)->count();

        // Cancellation rate
        $cancellationRate = $totalBookings > 0 ? ($cancelledBookings / $totalBookings) * 100 : 0;

        // Compare with previous period
        $previousStartDate = (clone $startDate)->subDays($endDate->diffInDays($startDate) + 1);
        $previousEndDate = (clone $startDate)->subDay();

        $previousQuery = Booking::query()
            ->whereDate('created_at', '>=', $previousStartDate)
            ->whereDate('created_at', '<=', $previousEndDate);

        if ($roomCategoryId) {
            $previousQuery->whereHas('room.room', function ($q) use ($roomCategoryId) {
                $q->where('room_category_id', $roomCategoryId);
            });
        }

        $previousBookings = $previousQuery->count();
        $previousRevenue = $previousQuery->sum('amount');

        $bookingGrowth = $previousBookings > 0 ? (($totalBookings - $previousBookings) / $previousBookings) * 100 : 0;
        $revenueGrowth = $previousRevenue > 0 ? (($totalRevenue - $previousRevenue) / $previousRevenue) * 100 : 0;

        return [
            'totalBookings' => $totalBookings,
            'totalRevenue' => $totalRevenue,
            'avgBookingValue' => $avgBookingValue,
            'completedBookings' => $completedBookings,
            'completionRate' => $completionRate,
            'cancelledBookings' => $cancelledBookings,
            'cancellationRate' => $cancellationRate,
            'bookingGrowth' => $bookingGrowth,
            'revenueGrowth' => $revenueGrowth,
        ];
    }

    /**
     * Get booking status distribution
     */
    private function getBookingStatusDistribution(Carbon $startDate, Carbon $endDate, ?int $roomCategoryId = null)
    {
        $bookingStatuses = BookingStatusEnum::labels();
        $statusColors = [
            BookingStatusEnum::PENDING => '#fbbf24',    // Amber
            BookingStatusEnum::PROCESSING => '#3b82f6', // Blue
            BookingStatusEnum::COMPLETED => '#10b981',  // Green
            BookingStatusEnum::CANCELLED => '#ef4444',  // Red
        ];

        $labels = [];
        $counts = [];
        $colors = [];

        foreach ($bookingStatuses as $status => $label) {
            $query = Booking::query()
                ->where('status', $status)
                ->whereDate('created_at', '>=', $startDate)
                ->whereDate('created_at', '<=', $endDate);

            if ($roomCategoryId) {
                $query->whereHas('room.room', function ($q) use ($roomCategoryId) {
                    $q->where('room_category_id', $roomCategoryId);
                });
            }

            $count = $query->count();

            $labels[] = $label;
            $counts[] = $count;
            $colors[] = $statusColors[$status] ?? '#6b7280';
        }

        return [
            'labels' => $labels,
            'counts' => $counts,
            'colors' => $colors
        ];
    }

    /**
     * Get top room categories by number of bookings
     */
    private function getTopRoomCategories(Carbon $startDate, Carbon $endDate, int $limit = 5)
    {
        $topCategories = Booking::query()
            ->join('ht_booking_rooms', 'ht_bookings.id', '=', 'ht_booking_rooms.booking_id')
            ->join('ht_rooms', 'ht_booking_rooms.room_id', '=', 'ht_rooms.id')
            ->join('ht_room_categories', 'ht_rooms.room_category_id', '=', 'ht_room_categories.id')
            ->select('ht_room_categories.name', DB::raw('COUNT(*) as count'), DB::raw('SUM(ht_bookings.amount) as revenue'))
            ->whereDate('ht_bookings.created_at', '>=', $startDate)
            ->whereDate('ht_bookings.created_at', '<=', $endDate)
            ->groupBy('ht_room_categories.name')
            ->orderByDesc('count')
            ->limit($limit)
            ->get();

        return [
            'labels' => $topCategories->pluck('name')->toArray(),
            'counts' => $topCategories->pluck('count')->toArray(),
            'revenue' => $topCategories->pluck('revenue')->toArray(),
        ];
    }

    /**
     * Get booking metrics
     */
    private function getBookingMetrics(Carbon $startDate, Carbon $endDate, ?int $roomCategoryId = null)
    {
        // Base query
        $query = Booking::query()
            ->whereDate('created_at', '>=', $startDate)
            ->whereDate('created_at', '<=', $endDate);

        // Apply room category filter if provided
        if ($roomCategoryId) {
            $query->whereHas('room.room', function ($q) use ($roomCategoryId) {
                $q->where('room_category_id', $roomCategoryId);
            });
        }

        // Total bookings
        $totalBookings = (clone $query)->count();

        // Total revenue
        $totalRevenue = (clone $query)->sum('amount');

        // Average booking value
        $avgBookingValue = $totalBookings > 0 ? $totalRevenue / $totalBookings : 0;

        // Completed bookings
        $completedBookings = (clone $query)->where('status', BookingStatusEnum::COMPLETED)->count();

        // Completion rate
        $completionRate = $totalBookings > 0 ? ($completedBookings / $totalBookings) * 100 : 0;

        // Cancelled bookings
        $cancelledBookings = (clone $query)->where('status', BookingStatusEnum::CANCELLED)->count();

        // Cancellation rate
        $cancellationRate = $totalBookings > 0 ? ($cancelledBookings / $totalBookings) * 100 : 0;

        // Compare with previous period
        $previousStartDate = (clone $startDate)->subDays($endDate->diffInDays($startDate) + 1);
        $previousEndDate = (clone $startDate)->subDay();

        $previousQuery = Booking::query()
            ->whereDate('created_at', '>=', $previousStartDate)
            ->whereDate('created_at', '<=', $previousEndDate);

        if ($roomCategoryId) {
            $previousQuery->whereHas('room.room', function ($q) use ($roomCategoryId) {
                $q->where('room_category_id', $roomCategoryId);
            });
        }

        $previousBookings = $previousQuery->count();
        $previousRevenue = $previousQuery->sum('amount');

        $bookingGrowth = $previousBookings > 0 ? (($totalBookings - $previousBookings) / $previousBookings) * 100 : 0;
        $revenueGrowth = $previousRevenue > 0 ? (($totalRevenue - $previousRevenue) / $previousRevenue) * 100 : 0;

        return [
            'totalBookings' => $totalBookings,
            'totalRevenue' => $totalRevenue,
            'avgBookingValue' => $avgBookingValue,
            'completedBookings' => $completedBookings,
            'completionRate' => $completionRate,
            'cancelledBookings' => $cancelledBookings,
            'cancellationRate' => $cancellationRate,
            'bookingGrowth' => $bookingGrowth,
            'revenueGrowth' => $revenueGrowth,
        ];
    }

    /**
     * Export bookings to Excel
     */
    public function export(Request $request)
    {
        $this->pageTitle('Export Booking Analytics');

        try {
            // Get date range from request
            [$startDate, $endDate] = HotelHelper::getDateRangeInReport($request);

            // Get filter values from request
            $roomCategoryId = $request->input('room_category_id');
            $bookingStatus = $request->input('booking_status');
            $paymentStatus = $request->input('payment_status');

            // Create the export file
            $format = $request->input('format', 'xlsx');
            $filename = 'booking_analytics_' . Carbon::now()->format('Y-m-d');

            return Excel::download(
                new BookingExport($startDate, $endDate, $roomCategoryId, $bookingStatus, $paymentStatus),
                $filename . '.' . $format
            );
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Error exporting data: ' . $e->getMessage());
        }
    }

    /**
     * Display simple booking report with Chart.js
     */
    public function simpleReport()
    {
        $this->pageTitle('Simple Booking Report');

        // Get bookings for the last 7 days
        $endDate = Carbon::today();
        $startDate = Carbon::today()->subDays(6); // Last 7 days including today

        // Query to get bookings grouped by date
        $bookingData = Booking::query()
            ->select(DB::raw('DATE(created_at) as date'), DB::raw('COUNT(*) as count'))
            ->whereDate('created_at', '>=', $startDate)
            ->whereDate('created_at', '<=', $endDate)
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        // Prepare data arrays for the chart
        $dates = [];
        $bookingCounts = [];

        // Create an array with all dates in the range
        $period = Carbon::parse($startDate)->daysUntil($endDate);
        foreach ($period as $date) {
            $formattedDate = $date->format('Y-m-d');
            $dates[] = $date->format('M d'); // Format for display (e.g., "May 01")
            $bookingCounts[] = 0; // Initialize with 0
        }

        // Fill in the actual booking counts
        foreach ($bookingData as $data) {
            $index = array_search(
                Carbon::parse($data->date)->format('M d'),
                $dates
            );

            if ($index !== false) {
                $bookingCounts[$index] = $data->count;
            }
        }

        return view('plugins/hotel::reports.simple-report', compact('dates', 'bookingCounts'));
    }

    /**
     * Display advanced booking report with multiple charts and filters
     */
    public function advancedReport(Request $request)
    {
        $this->pageTitle('Advanced Booking Report');

        // Add required assets
        Assets::addScriptsDirectly([
            'https://cdn.jsdelivr.net/npm/chart.js',
            'https://cdn.jsdelivr.net/npm/chartjs-plugin-datalabels',
            'vendor/core/plugins/hotel/libraries/daterangepicker/daterangepicker.js',
        ])
        ->addStylesDirectly([
            'vendor/core/plugins/hotel/libraries/daterangepicker/daterangepicker.css',
        ])
        ->addScripts(['moment', 'bootstrap']);

        // Get date range from request or use default (last 30 days)
        $endDate = $request->input('end_date') ? Carbon::parse($request->input('end_date')) : Carbon::today();
        $startDate = $request->input('start_date') ? Carbon::parse($request->input('start_date')) : Carbon::today()->subDays(29);

        // Get room categories for filter
        $roomCategories = RoomCategory::query()->pluck('name', 'id')->toArray();

        // Get filter values
        $roomCategoryId = $request->input('room_category_id');
        $groupBy = $request->input('group_by', 'day'); // day, week, month

        // Get chart data
        $bookingData = $this->getChartData($startDate, $endDate, $groupBy, $roomCategoryId, null, null);

        // For backward compatibility
        $revenueData = [
            'labels' => $bookingData['labels'],
            'revenue' => $bookingData['revenue']
        ];

        // Get booking status distribution
        $bookingStatusData = $this->getBookingStatusDistribution($startDate, $endDate, $roomCategoryId);

        // Get top room categories by bookings
        $topRoomCategories = $this->getTopRoomCategories($startDate, $endDate, 5);

        // Get recent bookings
        $recentBookings = $this->getRecentBookings($startDate, $endDate, $roomCategoryId, null, null);

        // Get booking metrics
        $metrics = $this->getBookingMetrics($startDate, $endDate, $roomCategoryId);

        return view('plugins/hotel::reports.advanced-report', compact(
            'startDate',
            'endDate',
            'bookingData',
            'revenueData',
            'bookingStatusData',
            'topRoomCategories',
            'recentBookings',
            'metrics',
            'roomCategories',
            'roomCategoryId',
            'groupBy'
        ));
    }

    /**
     * Get filtered booking data for charts
     */
    private function getFilteredBookingData(Carbon $startDate, Carbon $endDate, ?int $roomCategoryId = null, string $groupBy = 'day')
    {
        // Determine SQL grouping based on groupBy parameter
        $sqlGroupBy = match ($groupBy) {
            'day' => 'DATE(created_at)',
            'week' => "CONCAT(YEAR(created_at), '-', WEEK(created_at))",
            'month' => "DATE_FORMAT(created_at, '%Y-%m')",
            default => 'DATE(created_at)',
        };

        // Format for display
        $displayFormat = match ($groupBy) {
            'day' => 'M d',
            'week' => '\WW w',
            'month' => 'M Y',
            default => 'M d',
        };

        // Build query
        $query = Booking::query()
            ->select(DB::raw("$sqlGroupBy as period"), DB::raw('COUNT(*) as count'))
            ->whereDate('created_at', '>=', $startDate)
            ->whereDate('created_at', '<=', $endDate);

        // Apply room category filter if provided
        if ($roomCategoryId) {
            $query->whereHas('room.room', function ($q) use ($roomCategoryId) {
                $q->where('room_category_id', $roomCategoryId);
            });
        }

        // Get data grouped by period
        $data = $query->groupBy('period')
            ->orderBy('period')
            ->get();

        // Prepare result arrays
        $labels = [];
        $counts = [];

        // Generate all periods in the date range
        $period = match ($groupBy) {
            'day' => Carbon::parse($startDate)->daysUntil($endDate),
            'week' => Carbon::parse($startDate)->weeksUntil($endDate),
            'month' => Carbon::parse($startDate)->monthsUntil($endDate),
            default => Carbon::parse($startDate)->daysUntil($endDate),
        };

        // Create arrays with all periods
        foreach ($period as $date) {
            $labels[] = $date->format($displayFormat);
            $counts[] = 0;
        }

        // Fill in actual data
        foreach ($data as $item) {
            $periodDate = match ($groupBy) {
                'day' => Carbon::parse($item->period),
                'week' => Carbon::parse($item->period)->startOfWeek(),
                'month' => Carbon::parse($item->period . '-01'),
                default => Carbon::parse($item->period),
            };

            $formattedPeriod = $periodDate->format($displayFormat);
            $index = array_search($formattedPeriod, $labels);

            if ($index !== false) {
                $counts[$index] = (int) $item->count;
            }
        }

        return [
            'labels' => $labels,
            'counts' => $counts
        ];
    }

    /**
     * Get filtered revenue data for charts
     */
    private function getFilteredRevenueData(Carbon $startDate, Carbon $endDate, ?int $roomCategoryId = null, string $groupBy = 'day')
    {
        // Determine SQL grouping based on groupBy parameter
        $sqlGroupBy = match ($groupBy) {
            'day' => 'DATE(created_at)',
            'week' => "CONCAT(YEAR(created_at), '-', WEEK(created_at))",
            'month' => "DATE_FORMAT(created_at, '%Y-%m')",
            default => 'DATE(created_at)',
        };

        // Format for display
        $displayFormat = match ($groupBy) {
            'day' => 'M d',
            'week' => '\WW w',
            'month' => 'M Y',
            default => 'M d',
        };

        // Build query
        $query = Booking::query()
            ->select(DB::raw("$sqlGroupBy as period"), DB::raw('SUM(amount) as revenue'))
            ->whereDate('created_at', '>=', $startDate)
            ->whereDate('created_at', '<=', $endDate);

        // Apply room category filter if provided
        if ($roomCategoryId) {
            $query->whereHas('room.room', function ($q) use ($roomCategoryId) {
                $q->where('room_category_id', $roomCategoryId);
            });
        }

        // Get data grouped by period
        $data = $query->groupBy('period')
            ->orderBy('period')
            ->get();

        // Prepare result arrays
        $labels = [];
        $revenue = [];

        // Generate all periods in the date range
        $period = match ($groupBy) {
            'day' => Carbon::parse($startDate)->daysUntil($endDate),
            'week' => Carbon::parse($startDate)->weeksUntil($endDate),
            'month' => Carbon::parse($startDate)->monthsUntil($endDate),
            default => Carbon::parse($startDate)->daysUntil($endDate),
        };

        // Create arrays with all periods
        foreach ($period as $date) {
            $labels[] = $date->format($displayFormat);
            $revenue[] = 0;
        }

        // Fill in actual data
        foreach ($data as $item) {
            $periodDate = match ($groupBy) {
                'day' => Carbon::parse($item->period),
                'week' => Carbon::parse($item->period)->startOfWeek(),
                'month' => Carbon::parse($item->period . '-01'),
                default => Carbon::parse($item->period),
            };

            $formattedPeriod = $periodDate->format($displayFormat);
            $index = array_search($formattedPeriod, $labels);

            if ($index !== false) {
                $revenue[$index] = (float) $item->revenue;
            }
        }

        return [
            'labels' => $labels,
            'revenue' => $revenue
        ];
    }


}
