<?php

namespace Botble\Hotel\Listeners;

use Botble\Hotel\Enums\BookingStatusEnum;
use Botble\Hotel\Events\BookingStatusChanged;
use Botble\Hotel\Models\RoomDate;
use Botble\Hotel\Models\RoomInventory;
use Illuminate\Support\Facades\Log;

class FreeRoomOnStatusChangeListener
{
    /**
     * Handle the event.
     *
     * @param BookingStatusChanged $event
     * @return void
     */
    public function handle(BookingStatusChanged $event): void
    {
        $booking = $event->booking;
        $oldStatus = $event->oldStatus;
        $newStatus = $booking->status->getValue();

        // Log the status change
        Log::info("FreeRoomOnStatusChangeListener: Booking #{$booking->id} status changed from {$oldStatus} to {$newStatus}");

        try {
            // Get the booking room
            $bookingRoom = $booking->room;
            
            // Get room ID code with proper fallback logic
            $roomIdCode = null;
            if ($bookingRoom && $bookingRoom->room_id_code && $bookingRoom->room_id_code !== 'Select Room ID') {
                $roomIdCode = $bookingRoom->room_id_code;
                $roomId = $bookingRoom->room_id;
            } elseif ($booking->room_id_code && $booking->room_id_code !== 'Select Room ID') {
                $roomIdCode = $booking->room_id_code;
                $roomId = $bookingRoom ? $bookingRoom->room_id : null;
            } else {
                Log::info("No valid room ID code found for booking #{$booking->id}, skipping room availability update");
                return;
            }

            // Handle different status transitions
            if ($newStatus === BookingStatusEnum::COMPLETED || $newStatus === BookingStatusEnum::CANCELLED) {
                // Free the room when booking is completed or cancelled
                $this->freeRoom($booking, $roomIdCode, $roomId, $newStatus);
                Log::info("Room {$roomIdCode} marked as available due to {$newStatus} status for booking #{$booking->id}");
            } elseif ($newStatus === BookingStatusEnum::PROCESSING) {
                // Mark the room as unavailable when status is changed to Processing
                $this->markRoomAsUnavailable($booking, $roomIdCode, $roomId, $newStatus);
                Log::info("Room {$roomIdCode} marked as unavailable due to Processing status for booking #{$booking->id}");
            } elseif ($oldStatus === BookingStatusEnum::PROCESSING && $newStatus === BookingStatusEnum::PENDING) {
                // If status is changed from Processing back to Pending, free the room
                $this->freeRoom($booking, $roomIdCode, $roomId, $newStatus);
                Log::info("Room {$roomIdCode} freed due to status change from Processing to Pending for booking #{$booking->id}");
            }
        } catch (\Exception $e) {
            Log::error("Error updating room availability for booking #{$booking->id}: " . $e->getMessage());
            // Try to get more detailed error information
            Log::error("Error details: " . $e->getTraceAsString());
        }
    }

    /**
     * Free a room by marking it as available
     */
    private function freeRoom($booking, $roomIdCode, $roomId, $status): void
    {
        try {
            $statusName = $status === BookingStatusEnum::COMPLETED ? 'completed' : 'cancelled';

            // Update RoomInventory first to mark room as available
            $roomInventory = RoomInventory::where('room_id_code', $roomIdCode);
            if ($roomId) {
                $roomInventory->where('room_id', $roomId);
            }
            $inventoryUpdated = $roomInventory->update(['is_available' => true]);

            // Then update room_dates to mark as not booked
            $roomDates = RoomDate::where('booking_id', $booking->id)
                ->where('room_id_code', $roomIdCode);
            if ($roomId) {
                $roomDates->where('room_id', $roomId);
            }
            $updated = $roomDates->update([
                'is_booked' => false,
                'active' => false,
            ]);

            Log::info("Room freed on {$statusName} booking: Booking #{$booking->id}, Room: {$roomIdCode}, RoomDate records updated: {$updated}, RoomInventory records updated: {$inventoryUpdated}");
        } catch (\Exception $e) {
            Log::error("Error in freeRoom for booking #{$booking->id}, room {$roomIdCode}: " . $e->getMessage());
            throw $e; // Re-throw to be caught by the main handler
        }
    }

    /**
     * Mark a room as unavailable
     */
    private function markRoomAsUnavailable($booking, $roomIdCode, $roomId, $status): void
    {
        $statusName = $status === BookingStatusEnum::PENDING ? 'pending' : 'processing';

        // Update the room_dates table to mark this room as booked
        $roomDates = RoomDate::where('booking_id', $booking->id)
            ->where('room_id_code', $roomIdCode);

        if ($roomId) {
            $roomDates->where('room_id', $roomId);
        }

        // If no records exist, create a new one
        if ($roomDates->count() === 0 && $booking->room) {
            $startDate = $booking->room->start_date ?? null;
            $endDate = $booking->room->end_date ?? null;

            if ($startDate && $endDate) {
                RoomDate::create([
                    'room_id' => $roomId,
                    'room_id_code' => $roomIdCode,
                    'booking_id' => $booking->id,
                    'start_date' => $startDate,
                    'end_date' => $endDate,
                    'is_booked' => true,
                    'active' => true,
                ]);
                $updated = 1;
            } else {
                $updated = 0;
                Log::warning("Cannot create room date record for booking #{$booking->id}: missing start/end dates");
            }
        } else {
            // Update existing records
            $updated = $roomDates->update([
                'is_booked' => true,
                'active' => true,
            ]);
        }

        // Also update the RoomInventory table to mark the room as unavailable
        $roomInventory = RoomInventory::where('room_id_code', $roomIdCode);

        if ($roomId) {
            $roomInventory->where('room_id', $roomId);
        }

        $inventoryUpdated = $roomInventory->update([
            'is_available' => false,
        ]);

        Log::info("Room marked as unavailable for {$statusName} booking: Booking #{$booking->id}, Room: {$roomIdCode}, RoomDate records updated: {$updated}, RoomInventory records updated: {$inventoryUpdated}");
    }
}
