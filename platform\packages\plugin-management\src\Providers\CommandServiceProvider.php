<?php

namespace Bo<PERSON><PERSON>\PluginManagement\Providers;

use Bo<PERSON>ble\Base\Supports\ServiceProvider;
use Bo<PERSON><PERSON>\PluginManagement\Commands\ClearCompiledCommand;
use Bo<PERSON>ble\PluginManagement\Commands\IlluminateClearCompiledCommand as OverrideIlluminateClearCompiledCommand;
use Bo<PERSON>ble\PluginManagement\Commands\PackageDiscoverCommand;
use Botble\PluginManagement\Commands\PluginActivateAllCommand;
use Botble\PluginManagement\Commands\PluginActivateCommand;
use Botble\PluginManagement\Commands\PluginAssetsPublishCommand;
use Botble\PluginManagement\Commands\PluginDeactivateAllCommand;
use Botble\PluginManagement\Commands\PluginDeactivateCommand;
use Botble\PluginManagement\Commands\PluginDiscoverCommand;
use Botble\PluginManagement\Commands\PluginInstallFromMarketplaceCommand;
use Bo<PERSON><PERSON>\PluginManagement\Commands\PluginListCommand;
use Bo<PERSON><PERSON>\PluginManagement\Commands\PluginRemoveAllCommand;
use Bo<PERSON>ble\PluginManagement\Commands\PluginRemoveCommand;
use Botble\PluginManagement\Commands\PluginUpdateVersionInfoCommand;
use Illuminate\Foundation\Console\ClearCompiledCommand as IlluminateClearCompiledCommand;
use Illuminate\Foundation\Console\PackageDiscoverCommand as IlluminatePackageDiscoverCommand;

class CommandServiceProvider extends ServiceProvider
{
    public function register(): void
    {
        $this->app->extend(IlluminatePackageDiscoverCommand::class, function () {
            return $this->app->make(PackageDiscoverCommand::class);
        });

        $this->app->extend(IlluminateClearCompiledCommand::class, function () {
            return $this->app->make(OverrideIlluminateClearCompiledCommand::class);
        });
    }

    public function boot(): void
    {
        if ($this->app->runningInConsole()) {
            $this->commands([
                PluginAssetsPublishCommand::class,
                ClearCompiledCommand::class,
                PluginDiscoverCommand::class,
                PluginInstallFromMarketplaceCommand::class,
                PluginActivateCommand::class,
                PluginActivateAllCommand::class,
                PluginDeactivateCommand::class,
                PluginDeactivateAllCommand::class,
                PluginRemoveCommand::class,
                PluginRemoveAllCommand::class,
                PluginListCommand::class,
                PluginUpdateVersionInfoCommand::class,
            ]);
        }
    }
}
