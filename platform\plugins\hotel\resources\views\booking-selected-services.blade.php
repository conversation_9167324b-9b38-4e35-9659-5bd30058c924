@php
    // Get the selected services from the session
    $selectedServices = session('selected_services_' . $booking->id);
    $serviceNames = [];
    
    if ($selectedServices) {
        // Get the service names
        $services = \Botble\Hotel\Models\Service::whereIn('id', $selectedServices)->get();
        $serviceNames = $services->pluck('name')->toArray();
    }
@endphp

@if (!empty($serviceNames))
    <div class="alert alert-info">
        <h5>{{ __('Services selected by customer:') }}</h5>
        <ul>
            @foreach ($serviceNames as $serviceName)
                <li>{{ $serviceName }}</li>
            @endforeach
        </ul>
        <p>{{ __('Please add these services manually using the "Add Additional Service" button below.') }}</p>
    </div>
@endif
