<?php

namespace Botble\Hotel\Services;

use Botble\Hotel\Models\Room;
use Botble\Hotel\Models\RoomPrice;
use Botble\Hotel\Models\PricingRule;
use Carbon\Carbon;
use Carbon\CarbonPeriod;

class DynamicPricingService
{
    /**
     * Calculate the dynamic price for a room over a date range with future pricing support
     */
    public function calculateRoomPrice(
        Room $room,
        string $startDate,
        string $endDate,
        int $rooms = 1,
        int $guests = 1,
        array $bookingData = []
    ): array {
        $startDate = Carbon::parse($startDate);
        $endDate = Carbon::parse($endDate);
        $nights = $startDate->diffInDays($endDate);

        $bookingData = array_merge([
            'check_in' => $startDate->format('Y-m-d'),
            'check_out' => $endDate->format('Y-m-d'),
            'nights' => $nights,
            'rooms' => $rooms,
            'guests' => $guests,
            'booking_date' => now()->format('Y-m-d'),
        ], $bookingData);

        $dailyPrices = [];
        $totalPrice = 0;
        $appliedRules = [];

        // Calculate price for each day
        $period = CarbonPeriod::create($startDate, $endDate->copy()->subDay());

        foreach ($period as $date) {
            $dayPrice = $this->calculateDayPrice($room, $date, $bookingData);
            $dailyPrices[$date->format('Y-m-d')] = $dayPrice;
            $totalPrice += $dayPrice['final_price'];

            if (!empty($dayPrice['applied_rules'])) {
                $appliedRules = array_merge($appliedRules, $dayPrice['applied_rules']);
            }
        }

        // Apply length-of-stay discounts
        $lengthOfStayAdjustment = $this->applyLengthOfStayRules($room, $totalPrice, $bookingData);
        if ($lengthOfStayAdjustment['applied']) {
            $totalPrice = $lengthOfStayAdjustment['adjusted_price'];
            $appliedRules[] = $lengthOfStayAdjustment['rule'];
        }

        // Apply group discounts
        $groupDiscountAdjustment = $this->applyGroupDiscountRules($room, $totalPrice, $bookingData);
        if ($groupDiscountAdjustment['applied']) {
            $totalPrice = $groupDiscountAdjustment['adjusted_price'];
            $appliedRules[] = $groupDiscountAdjustment['rule'];
        }

        return [
            'base_price' => $room->price * $nights * $rooms,
            'total_price' => $totalPrice * $rooms,
            'price_per_night' => $totalPrice / $nights,
            'nights' => $nights,
            'rooms' => $rooms,
            'daily_prices' => $dailyPrices,
            'applied_rules' => array_unique($appliedRules, SORT_REGULAR),
            'savings' => max(0, ($room->price * $nights * $rooms) - ($totalPrice * $rooms)),
        ];
    }

    /**
     * Calculate price for a specific day
     */
    protected function calculateDayPrice(Room $room, Carbon $date, array $bookingData): array
    {
        $basePrice = $room->price;
        $finalPrice = $basePrice;
        $appliedRules = [];

        // 1. Check for specific room prices (highest priority)
        $roomPrice = $this->getBestRoomPrice($room, $date, $bookingData);
        if ($roomPrice) {
            $finalPrice = $roomPrice->calculatePrice($basePrice);
            $appliedRules[] = [
                'type' => 'room_price',
                'name' => $roomPrice->name,
                'adjustment' => $finalPrice - $basePrice,
            ];
        }

        // 2. Apply pricing rules
        $pricingRules = $this->getApplicablePricingRules($room, $date, $bookingData);
        foreach ($pricingRules as $rule) {
            $adjustedPrice = $rule->calculateAdjustedPrice($finalPrice);
            $appliedRules[] = [
                'type' => 'pricing_rule',
                'name' => $rule->name,
                'rule_type' => $rule->type,
                'adjustment' => $adjustedPrice - $finalPrice,
            ];
            $finalPrice = $adjustedPrice;
        }

        return [
            'date' => $date->format('Y-m-d'),
            'base_price' => $basePrice,
            'final_price' => $finalPrice,
            'applied_rules' => $appliedRules,
        ];
    }

    /**
     * Get the best room price for a specific date with future pricing priority
     */
    protected function getBestRoomPrice(Room $room, Carbon $date, array $bookingData): ?RoomPrice
    {
        $prices = $this->getApplicablePrices($room, $date, $date);

        foreach ($prices as $price) {
            if ($price->appliesTo($date) && $price->meetsStayRequirements($bookingData['nights'])) {
                return $price;
            }
        }

        return null;
    }

    /**
     * Get applicable prices with future pricing priority and category support
     */
    protected function getApplicablePrices(Room $room, Carbon $startDate, Carbon $endDate)
    {
        // Get all applicable prices with priority order
        $query = RoomPrice::active()
            ->forDateRange($startDate, $endDate);

        // Only add approval status filter if the column exists
        if (\Schema::hasColumn('ht_room_prices', 'approval_status')) {
            $query->approved();
        }

        // Add room-specific and category-specific prices
        $query->where(function ($q) use ($room) {
            $q->where(function ($subQ) use ($room) {
                // Individual room prices
                $subQ->where('pricing_category', RoomPrice::CATEGORY_INDIVIDUAL_ROOM)
                     ->where('room_id', $room->id);
            })
            ->orWhere(function ($subQ) use ($room) {
                // Room category prices
                $subQ->where('pricing_category', RoomPrice::CATEGORY_ROOM_CATEGORY)
                     ->where('room_category_id', $room->room_category_id);
            })
            ->orWhere('pricing_category', RoomPrice::CATEGORY_ALL_ROOMS);
        });

        // Order by effective priority (override_priority first, then priority)
        return $query->orderByRaw('COALESCE(override_priority, priority) DESC')
                    ->orderBy('created_at', 'desc')
                    ->get();
    }

    /**
     * Get applicable pricing rules for a date
     */
    protected function getApplicablePricingRules(Room $room, Carbon $date, array $bookingData): array
    {
        $rules = PricingRule::active()
            ->validForDate($date)
            ->byPriority()
            ->get();

        $applicableRules = [];

        foreach ($rules as $rule) {
            if ($rule->appliesToRoom($room->id, $room->room_category_id) &&
                $rule->conditionsMet($bookingData)) {
                $applicableRules[] = $rule;
            }
        }

        return $applicableRules;
    }

    /**
     * Apply length of stay rules
     */
    protected function applyLengthOfStayRules(Room $room, float $totalPrice, array $bookingData): array
    {
        $rules = PricingRule::active()
            ->where('type', PricingRule::TYPE_LENGTH_OF_STAY)
            ->byPriority()
            ->get();

        foreach ($rules as $rule) {
            if ($rule->appliesToRoom($room->id, $room->room_category_id) &&
                $rule->conditionsMet($bookingData)) {

                return [
                    'applied' => true,
                    'adjusted_price' => $rule->calculateAdjustedPrice($totalPrice),
                    'rule' => [
                        'type' => 'length_of_stay',
                        'name' => $rule->name,
                        'adjustment' => $rule->calculateAdjustedPrice($totalPrice) - $totalPrice,
                    ],
                ];
            }
        }

        return ['applied' => false];
    }

    /**
     * Apply group discount rules
     */
    protected function applyGroupDiscountRules(Room $room, float $totalPrice, array $bookingData): array
    {
        $rules = PricingRule::active()
            ->where('type', PricingRule::TYPE_GROUP_DISCOUNT)
            ->byPriority()
            ->get();

        foreach ($rules as $rule) {
            if ($rule->appliesToRoom($room->id, $room->room_category_id) &&
                $rule->conditionsMet($bookingData)) {

                return [
                    'applied' => true,
                    'adjusted_price' => $rule->calculateAdjustedPrice($totalPrice),
                    'rule' => [
                        'type' => 'group_discount',
                        'name' => $rule->name,
                        'adjustment' => $rule->calculateAdjustedPrice($totalPrice) - $totalPrice,
                    ],
                ];
            }
        }

        return ['applied' => false];
    }

    /**
     * Get pricing suggestions for a room
     */
    public function getPricingSuggestions(Room $room, string $startDate, string $endDate): array
    {
        // This could include competitor analysis, demand forecasting, etc.
        // For now, return basic suggestions based on historical data

        return [
            'suggested_base_price' => $room->price,
            'peak_season_multiplier' => 1.3,
            'off_season_multiplier' => 0.8,
            'weekend_multiplier' => 1.2,
            'early_bird_discount' => 0.15,
            'last_minute_discount' => 0.1,
        ];
    }

    /**
     * Get future pricing calendar for a room
     */
    public function getFuturePricingCalendar(Room $room, string $startDate, string $endDate): array
    {
        $startDate = Carbon::parse($startDate);
        $endDate = Carbon::parse($endDate);
        $calendar = [];

        $period = CarbonPeriod::create($startDate, $endDate);

        foreach ($period as $date) {
            $dayPrice = $this->calculateDayPrice($room, $date, [
                'nights' => 1,
                'rooms' => 1,
                'guests' => 1,
                'booking_date' => now()->format('Y-m-d'),
            ]);

            $calendar[$date->format('Y-m-d')] = [
                'date' => $date->format('Y-m-d'),
                'day_name' => $date->format('l'),
                'base_price' => $dayPrice['base_price'],
                'final_price' => $dayPrice['final_price'],
                'has_special_pricing' => $dayPrice['base_price'] != $dayPrice['final_price'],
                'applied_rules' => $dayPrice['applied_rules'],
                'is_weekend' => $date->isWeekend(),
                'is_future' => $date->isFuture(),
            ];
        }

        return $calendar;
    }

    /**
     * Validate future pricing rules for conflicts
     */
    public function validateFuturePricing(array $pricingData): array
    {
        $errors = [];
        $warnings = [];

        // Validate date range
        $startDate = Carbon::parse($pricingData['start_date']);
        $endDate = Carbon::parse($pricingData['end_date']);

        if ($startDate->gte($endDate)) {
            $errors[] = 'End date must be after start date';
        }

        // Validate future date limit (12 months)
        if ($startDate->gt(Carbon::now()->addMonths(12))) {
            $errors[] = 'Cannot set pricing more than 12 months in advance';
        }

        // Check for significant price increases
        if (isset($pricingData['room_id'])) {
            $room = Room::find($pricingData['room_id']);
            if ($room && isset($pricingData['price'])) {
                $increase = (($pricingData['price'] - $room->price) / $room->price) * 100;
                if ($increase > 50) {
                    $warnings[] = 'Price increase of ' . round($increase, 1) . '% requires approval';
                }
            }
        }

        // Check for overlapping rules
        $overlaps = $this->checkForOverlappingRules($pricingData);
        if (!empty($overlaps)) {
            $warnings = array_merge($warnings, $overlaps);
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors,
            'warnings' => $warnings,
        ];
    }

    /**
     * Check for overlapping pricing rules
     */
    protected function checkForOverlappingRules(array $pricingData): array
    {
        $warnings = [];

        $query = RoomPrice::active()
            ->approved()
            ->forDateRange($pricingData['start_date'], $pricingData['end_date']);

        if (isset($pricingData['id'])) {
            $query->where('id', '!=', $pricingData['id']);
        }

        // Check based on pricing category
        if ($pricingData['pricing_category'] === RoomPrice::CATEGORY_INDIVIDUAL_ROOM) {
            $query->where('room_id', $pricingData['room_id']);
        } elseif ($pricingData['pricing_category'] === RoomPrice::CATEGORY_ROOM_CATEGORY) {
            $query->where('room_category_id', $pricingData['room_category_id']);
        }

        $overlapping = $query->get();

        foreach ($overlapping as $price) {
            $warnings[] = "Overlaps with existing rule '{$price->name}' from {$price->start_date->format('M j')} to {$price->end_date->format('M j')}";
        }

        return $warnings;
    }

    /**
     * Generate bulk pricing for seasonal patterns
     */
    public function generateBulkSeasonalPricing(array $seasonalData): array
    {
        $generated = [];
        $errors = [];

        foreach ($seasonalData['seasons'] as $season) {
            try {
                $startDate = Carbon::parse($season['start_date']);
                $endDate = Carbon::parse($season['end_date']);

                // Generate pricing rules for each room/category
                foreach ($seasonalData['targets'] as $target) {
                    $pricingData = [
                        'name' => $season['name'] . ' - ' . $target['name'],
                        'pricing_category' => $target['type'],
                        'room_id' => $target['type'] === RoomPrice::CATEGORY_INDIVIDUAL_ROOM ? $target['id'] : null,
                        'room_category_id' => $target['type'] === RoomPrice::CATEGORY_ROOM_CATEGORY ? $target['id'] : null,
                        'start_date' => $startDate,
                        'end_date' => $endDate,
                        'price' => $season['price'],
                        'pricing_type' => $season['pricing_type'] ?? 'fixed',
                        'adjustment_value' => $season['adjustment_value'] ?? null,
                        'priority' => $season['priority'] ?? 1,
                        'is_active' => true,
                        'approval_status' => RoomPrice::APPROVAL_APPROVED,
                        'created_by_type' => 'bulk_import',
                    ];

                    $validation = $this->validateFuturePricing($pricingData);
                    if ($validation['valid']) {
                        $generated[] = $pricingData;
                    } else {
                        $errors[] = "Failed to generate {$pricingData['name']}: " . implode(', ', $validation['errors']);
                    }
                }
            } catch (\Exception $e) {
                $errors[] = "Error processing season {$season['name']}: " . $e->getMessage();
            }
        }

        return [
            'generated' => $generated,
            'errors' => $errors,
            'count' => count($generated),
        ];
    }

    /**
     * Calculate price breakdown for display
     */
    public function getPriceBreakdown(Room $room, string $startDate, string $endDate, int $rooms = 1): array
    {
        $pricing = $this->calculateRoomPrice($room, $startDate, $endDate, $rooms);

        $breakdown = [
            'base_calculation' => [
                'base_price_per_night' => $room->price,
                'nights' => $pricing['nights'],
                'rooms' => $rooms,
                'subtotal' => $room->price * $pricing['nights'] * $rooms,
            ],
            'adjustments' => [],
            'daily_breakdown' => $pricing['daily_prices'],
            'totals' => [
                'subtotal' => $pricing['base_price'],
                'total_adjustments' => $pricing['total_price'] - $pricing['base_price'],
                'final_total' => $pricing['total_price'],
                'savings' => $pricing['savings'],
            ],
        ];

        // Group applied rules by type
        foreach ($pricing['applied_rules'] as $rule) {
            $type = $rule['type'] ?? 'other';
            if (!isset($breakdown['adjustments'][$type])) {
                $breakdown['adjustments'][$type] = [];
            }
            $breakdown['adjustments'][$type][] = $rule;
        }

        return $breakdown;
    }
}
