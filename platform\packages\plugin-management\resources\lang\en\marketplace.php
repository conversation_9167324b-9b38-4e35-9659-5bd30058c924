<?php

return [
    'showing' => 'Showing',
    'to' => 'to',
    'of' => 'of',
    'results' => 'results',
    'add_new' => 'Add new',
    'view_on_marketplace' => 'View on Marketplace',
    'author' => 'Author',
    'downloads' => 'Downloads',
    'version' => 'Version',
    'rating' => 'Rating',
    'last_update' => 'Last update',
    'license' => 'License',
    'minimum_core_version' => 'Minimum core version',
    'compatible_version' => 'Compatible with your version',
    'incompatible_version' => 'Incompatible with your version',
    'install_now' => 'Install now',
    'remove' => 'Remove',
    'detail' => 'Details',
    'activate' => 'Activate',
    'deactivate' => 'Deactivate',
    'something_went_wrong' => 'Something went wrong',
    'could_not_connect' => 'Could not connect to Marketplace server. Please try again later.',
    'search' => 'Search...',
    'clear_search' => 'Clear search',
    'all' => 'All',
    'featured' => 'Featured',
    'default' => 'Default',
    'popular' => 'Popular',
    'top_rated' => 'Top Rated',
    'install_plugin' => 'Install plugin',
    'folder_permissions' => 'Folder does not have permission to write file or the update file path could not be resolved, please contact support',
    'unzip_failed' => 'Unzip extraction failed',
    'unzip_success' => 'Download file extracted',
    'install_success' => 'Installed plugin successfully!',
    'update_success' => 'Updated plugin successfully!',
    'minimum_core_version_error' => 'Cannot install this plugin. Minimum core version is :version.',
];
