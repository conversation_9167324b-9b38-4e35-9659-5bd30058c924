<?php

namespace Botble\Hotel\Http\Requests;

use Botble\Base\Rules\OnOffRule;
use Botble\Hotel\Enums\BookingStatusEnum;
use Botble\Hotel\Facades\HotelHelper;
use Botble\Hotel\Models\Room;
use Botble\Support\Http\Requests\Request;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Validator;

class CreateOfflineBookingRequest extends Request
{
    public function rules(): array
    {
        return [
            // Customer Information
            'first_name' => ['required', 'string', 'max:255'],
            'last_name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'email', 'max:255'],
            'phone' => ['required', 'string', 'max:20'],
            'address' => ['nullable', 'string', 'max:500'],
            'city' => ['nullable', 'string', 'max:100'],
            'state' => ['nullable', 'string', 'max:100'],
            'country' => ['nullable', 'string', 'max:100'],
            'zip' => ['nullable', 'string', 'max:20'],

            // Booking Information
            'room_id' => ['required', 'exists:ht_rooms,id'],
            'start_date' => [
                'required',
                'date',
                'date_format:Y-m-d',
                'after_or_equal:today'
            ],
            'end_date' => [
                'required',
                'date',
                'date_format:Y-m-d',
                'after:start_date'
            ],
            'adults' => [
                'required',
                'integer',
                'min:1',
                'max:10',
            ],
            'children' => ['nullable', 'integer', 'min:0', 'max:10'],
            'number_of_rooms' => ['required', 'integer', 'min:1', 'max:10'],

            // Additional Information
            'requests' => ['nullable', 'string', 'max:1000'],
            'arrival_time' => ['nullable', 'string', 'max:50'],
            'status' => ['required', Rule::in(BookingStatusEnum::values())],

            // Services
            'services' => ['nullable', 'array'],
            'services.*' => ['exists:ht_services,id'],

            // Payment Information (optional for offline bookings)
            'payment_method' => ['nullable', 'string', 'max:100'],
            'payment_notes' => ['nullable', 'string', 'max:500'],
        ];
    }

    public function attributes(): array
    {
        return [
            'first_name' => trans('plugins/hotel::booking.first_name'),
            'last_name' => trans('plugins/hotel::booking.last_name'),
            'email' => trans('plugins/hotel::booking.email'),
            'phone' => trans('plugins/hotel::booking.phone'),
            'room_id' => trans('plugins/hotel::booking.room'),
            'start_date' => trans('plugins/hotel::booking.start_date'),
            'end_date' => trans('plugins/hotel::booking.end_date'),
            'adults' => trans('plugins/hotel::booking.adults'),
            'children' => trans('plugins/hotel::booking.children'),
            'number_of_rooms' => trans('plugins/hotel::booking.number_of_rooms'),
            'requests' => trans('plugins/hotel::booking.requests'),
            'arrival_time' => trans('plugins/hotel::booking.arrival_time'),
            'status' => trans('core/base::tables.status'),
        ];
    }

    public function messages(): array
    {
        return [
            'room_id.required' => 'Please select a room for the booking.',
            'room_id.exists' => 'The selected room does not exist.',
            'start_date.after_or_equal' => 'Check-in date must be today or later.',
            'start_date.date_format' => 'Check-in date must be in the correct format (YYYY-MM-DD).',
            'end_date.after' => 'Check-out date must be after check-in date.',
            'end_date.date_format' => 'Check-out date must be in the correct format (YYYY-MM-DD).',
            'adults.min' => 'At least 1 adult guest is required.',
            'adults.max' => 'Maximum 10 adult guests allowed.',
            'number_of_rooms.min' => 'At least 1 room must be selected.',
            'number_of_rooms.max' => 'Maximum 10 rooms can be booked at once.',
        ];
    }

    /**
     * Configure the validator instance with custom validation rules.
     */
    public function withValidator(Validator $validator): void
    {
        $validator->after(function (Validator $validator) {
            $this->validateRoomOccupancy($validator);
            $this->validateRoomAvailability($validator);
        });
    }

    /**
     * Validate room occupancy based on room type and capacity.
     */
    protected function validateRoomOccupancy(Validator $validator): void
    {
        $roomId = $this->input('room_id');
        $adults = (int) $this->input('adults', 0);
        $children = (int) $this->input('children', 0);
        $numberOfRooms = (int) $this->input('number_of_rooms', 1);

        if (!$roomId || !$adults) {
            return;
        }

        // Skip room occupancy validation if Room model doesn't exist
        try {
            $room = Room::find($roomId);
            if (!$room) {
                return;
            }

            // Only validate if room has capacity fields
            if (isset($room->max_adults) && isset($room->max_children)) {
                // Calculate total guests per room
                $totalGuestsPerRoom = ceil(($adults + $children) / $numberOfRooms);

                // Check if room can accommodate the guests
                $maxCapacity = $room->max_adults + $room->max_children;
                if ($totalGuestsPerRoom > $maxCapacity) {
                    $validator->errors()->add(
                        'adults',
                        "The selected room can accommodate maximum {$maxCapacity} guests per room. You have {$totalGuestsPerRoom} guests per room."
                    );
                }

                // Check adults capacity
                $adultsPerRoom = ceil($adults / $numberOfRooms);
                if ($adultsPerRoom > $room->max_adults) {
                    $validator->errors()->add(
                        'adults',
                        "The selected room can accommodate maximum {$room->max_adults} adults per room. You have {$adultsPerRoom} adults per room."
                    );
                }

                // Check children capacity
                $childrenPerRoom = ceil($children / $numberOfRooms);
                if ($childrenPerRoom > $room->max_children) {
                    $validator->errors()->add(
                        'children',
                        "The selected room can accommodate maximum {$room->max_children} children per room. You have {$childrenPerRoom} children per room."
                    );
                }
            }
        } catch (\Exception $e) {
            // Skip validation if there are any errors (plugin not fully loaded)
            return;
        }
    }

    /**
     * Validate room availability for the selected dates.
     */
    protected function validateRoomAvailability(Validator $validator): void
    {
        $roomId = $this->input('room_id');
        $startDate = $this->input('start_date');
        $endDate = $this->input('end_date');
        $numberOfRooms = (int) $this->input('number_of_rooms', 1);

        if (!$roomId || !$startDate || !$endDate) {
            return;
        }

        // Skip room availability validation if Room model doesn't exist or method doesn't exist
        try {
            $room = Room::find($roomId);
            if (!$room) {
                return;
            }

            // Only validate availability if the method exists
            if (method_exists($room, 'isAvailableAt')) {
                $isAvailable = $room->isAvailableAt([
                    'start_date' => $startDate,
                    'end_date' => $endDate,
                    'adults' => $this->input('adults'),
                    'children' => $this->input('children'),
                    'rooms' => $numberOfRooms,
                ]);

                if (!$isAvailable) {
                    $validator->errors()->add(
                        'room_id',
                        'The selected room is not available for the chosen dates. Please select different dates or another room.'
                    );
                }
            }

            // Check if enough rooms are available (basic validation)
            if (isset($room->number_of_rooms) && $numberOfRooms > $room->number_of_rooms) {
                $validator->errors()->add(
                    'number_of_rooms',
                    "Only {$room->number_of_rooms} rooms are available for this room type. You requested {$numberOfRooms} rooms."
                );
            }
        } catch (\Exception $e) {
            // Skip validation if there are any errors (plugin not fully loaded)
            return;
        }
    }
}
