<?php

namespace Botble\Hotel\Http\Requests;

use Bo<PERSON>ble\Base\Enums\BaseStatusEnum;
use Bo<PERSON>ble\Base\Rules\OnOffRule;
use Botble\Hotel\Models\RoomCategory;
use Botble\Support\Http\Requests\Request;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Validator;

class RoomRequest extends Request
{
    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'description' => ['nullable', 'string', 'max:400'],
            'status' => Rule::in(BaseStatusEnum::values()),
            'is_featured' => new OnOffRule(),
            'content' => ['nullable', 'string', 'max:100000'],
            'order' => ['nullable', 'numeric'],
            'price' => ['nullable', 'numeric'],
            'number_of_rooms' => ['nullable', 'integer', 'min:1'],
            'number_of_beds' => ['nullable', 'integer', 'min:0'],
            'max_adults' => ['nullable', 'numeric'],
            'max_children' => ['nullable', 'numeric'],
            'size' => ['nullable', 'numeric'],
            'images' => ['nullable', 'array'],
            'images.*' => ['nullable', 'string'],
            'room_category_id' => ['required', 'string', 'exists:ht_room_categories,id'],
            'tax_id' => ['required', 'string', 'exists:ht_taxes,id'],
        ];
    }

    /**
     * Configure the validator instance with custom validation rules for room occupancy.
     */
    public function withValidator(Validator $validator): void
    {
        $validator->after(function (Validator $validator) {
            $this->validateRoomOccupancy($validator);
        });
    }

    /**
     * Validate room occupancy based on room category
     */
    protected function validateRoomOccupancy(Validator $validator): void
    {
        $roomCategoryId = $this->input('room_category_id');
        $maxAdults = (int) $this->input('max_adults');
        $maxChildren = (int) $this->input('max_children');

        if (!$roomCategoryId) {
            return;
        }

        // Get the room category
        $roomCategory = RoomCategory::find($roomCategoryId);
        if (!$roomCategory) {
            return;
        }

        // For Standard Room, enforce specific occupancy rules
        if ($roomCategory->name === 'Standard Room') {
            $validCombinations = [
                ['adults' => 1, 'children' => 0], // 1 adult only
                ['adults' => 1, 'children' => 1], // 1 adult and 1 child
                ['adults' => 1, 'children' => 2], // 1 adult and 2 children
                ['adults' => 2, 'children' => 1], // 2 adults and 1 child
            ];

            $isValid = false;
            foreach ($validCombinations as $combination) {
                if ($maxAdults === $combination['adults'] && $maxChildren === $combination['children']) {
                    $isValid = true;
                    break;
                }
            }

            if (!$isValid) {
                $validator->errors()->add(
                    'max_adults',
                    'For Standard Room, only these combinations are allowed: 2 adults and 1 child, 1 adult and 2 children, 1 adult only, or 1 adult and 1 child.'
                );
                $validator->errors()->add(
                    'max_children',
                    'For Standard Room, only these combinations are allowed: 2 adults and 1 child, 1 adult and 2 children, 1 adult only, or 1 adult and 1 child.'
                );
            }
        }
    }
}
