<?php

namespace Botble\Hotel\Models;

use Botble\Base\Models\BaseModel;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class RoomInventory extends BaseModel
{
    protected $table = 'ht_room_inventory';

    protected $fillable = [
        'room_id',
        'room_id_code',
        'is_available',
    ];

    protected $casts = [
        'is_available' => 'boolean',
    ];

    public function room(): BelongsTo
    {
        return $this->belongsTo(Room::class, 'room_id');
    }

    /**
     * Check if a room is available for a specific date range
     */
    public function isAvailableForDateRange($startDate, $endDate, $excludeBookingId = null)
    {
        try {
            // If we're checking for an existing booking, consider it available
            if ($excludeBookingId) {
                return true;
            }
            
            // First check the basic availability flag
            if (!$this->is_available) {
                // Check if there are any bookings for this room that overlap with the requested dates
                $query = BookingRoom::query()
                    ->where('room_id_code', $this->room_id_code)
                    ->where(function ($query) use ($startDate, $endDate) {
                        $query->where(function ($q) use ($startDate, $endDate) {
                            // Booking start date is within our range
                            $q->whereDate('start_date', '>=', $startDate)
                              ->whereDate('start_date', '<', $endDate);
                        })->orWhere(function ($q) use ($startDate, $endDate) {
                            // Booking end date is within our range
                            $q->whereDate('end_date', '>', $startDate)
                              ->whereDate('end_date', '<=', $endDate);
                        })->orWhere(function ($q) use ($startDate, $endDate) {
                            // Booking spans our entire range
                            $q->whereDate('start_date', '<=', $startDate)
                              ->whereDate('end_date', '>=', $endDate);
                        });
                    });

                if ($excludeBookingId) {
                    $query->where('booking_id', '!=', $excludeBookingId);
                }

                $hasBookingsInRequestedRange = $query->exists();

                if (!$hasBookingsInRequestedRange) {
                    return true;
                }
                
                return false;
            }

            // Check if there are any bookings for this room in the given date range
            $bookingRoomsQuery = BookingRoom::query()
                ->where('room_id_code', $this->room_id_code)
                ->where(function ($query) use ($startDate, $endDate) {
                    $query->where(function ($q) use ($startDate, $endDate) {
                        // Booking start date is within our range
                        $q->whereDate('start_date', '>=', $startDate)
                          ->whereDate('start_date', '<', $endDate);
                    })->orWhere(function ($q) use ($startDate, $endDate) {
                        // Booking end date is within our range
                        $q->whereDate('end_date', '>', $startDate)
                          ->whereDate('end_date', '<=', $endDate);
                    })->orWhere(function ($q) use ($startDate, $endDate) {
                        // Booking spans our entire range
                        $q->whereDate('start_date', '<=', $startDate)
                          ->whereDate('end_date', '>=', $endDate);
                    });
                });

            if ($excludeBookingId) {
                $bookingRoomsQuery->where('booking_id', '!=', $excludeBookingId);
            }

            if ($bookingRoomsQuery->count() > 0) {
                return false;
            }

            // Also check the room_dates table for bookings
            $roomDatesQuery = RoomDate::query()
                ->where('room_id', $this->room_id)
                ->where('room_id_code', $this->room_id_code)
                ->where('is_booked', true)
                ->where(function ($query) use ($startDate, $endDate) {
                    $query->where(function ($q) use ($startDate, $endDate) {
                        // Booking start date is within our range
                        $q->whereDate('start_date', '>=', $startDate)
                          ->whereDate('start_date', '<', $endDate);
                    })->orWhere(function ($q) use ($startDate, $endDate) {
                        // Booking end date is within our range
                        $q->whereDate('end_date', '>', $startDate)
                          ->whereDate('end_date', '<=', $endDate);
                    })->orWhere(function ($q) use ($startDate, $endDate) {
                        // Booking spans our entire range
                        $q->whereDate('start_date', '<=', $startDate)
                          ->whereDate('end_date', '>=', $endDate);
                    });
                });
            
            if ($excludeBookingId) {
                $roomDatesQuery->where('booking_id', '!=', $excludeBookingId);
            }

            return $roomDatesQuery->count() === 0;
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Error in isAvailableForDateRange: ' . $e->getMessage());
            // If there's an error, consider the room available
            return true;
        }
    }
}

