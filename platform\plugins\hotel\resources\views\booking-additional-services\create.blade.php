@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="row">
        <div class="col-md-6">
            <x-core::card>
                <x-core::card.header>
                    <h4>{{ trans('plugins/hotel::booking.add_additional_service') }}</h4>
                </x-core::card.header>
                <x-core::card.body>
                    <x-core::form
                        :url="route('booking.additional-services.store', $booking->id)"
                        method="POST"
                    >
                        <div class="mb-3">
                            <x-core::form.label>{{ trans('plugins/hotel::booking.booking_information') }}</x-core::form.label>
                            <div class="border p-3 rounded">
                                <div><strong>{{ trans('plugins/hotel::booking.booking_number') }}:</strong> {{ $booking->booking_number }}</div>
                                <div><strong>{{ trans('plugins/hotel::booking.customer') }}:</strong> {{ $booking->address->first_name }} {{ $booking->address->last_name }}</div>
                                <div><strong>{{ trans('plugins/hotel::booking.room') }}:</strong> {{ $booking->room->room_name }}</div>
                                <div><strong>{{ trans('plugins/hotel::booking.room_id_code') }}:</strong> {{ $booking->room_id_code }}</div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <x-core::form.radio-list
                                name="service_type"
                                :options="[
                                    'predefined' => trans('plugins/hotel::booking.predefined_service'),
                                    'custom' => trans('plugins/hotel::booking.custom_service'),
                                ]"
                                :checked="old('service_type', 'predefined')"
                                class="service-type-radio"
                            />
                        </div>

                        <div class="predefined-service-section">
                            <div class="mb-3">
                                <x-core::form.label for="service_id">{{ trans('plugins/hotel::booking.select_service') }}</x-core::form.label>
                                <select name="service_id" id="service_id" class="form-select">
                                    <option value="">{{ trans('plugins/hotel::booking.select_service') }}</option>
                                    @foreach($services as $service)
                                        <option value="{{ $service->id }}" data-price="{{ $service->price }}">
                                            {{ $service->name }} ({{ format_price($service->price) }})
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                        </div>

                        <div class="custom-service-section" style="display: none;">
                            <div class="mb-3">
                                <x-core::form.label for="custom_service_name">{{ trans('plugins/hotel::booking.custom_service_name') }}</x-core::form.label>
                                <x-core::form.text-input
                                    id="custom_service_name"
                                    name="custom_service_name"
                                    :value="old('custom_service_name')"
                                    placeholder="{{ trans('plugins/hotel::booking.custom_service_name_placeholder') }}"
                                />
                            </div>

                            <div class="mb-3">
                                <x-core::form.label for="custom_service_price">{{ trans('plugins/hotel::booking.custom_service_price') }}</x-core::form.label>
                                <x-core::form.text-input
                                    id="custom_service_price"
                                    name="custom_service_price"
                                    type="number"
                                    step="0.01"
                                    :value="old('custom_service_price')"
                                    placeholder="{{ trans('plugins/hotel::booking.custom_service_price_placeholder') }}"
                                />
                            </div>
                        </div>

                        <div class="mb-3">
                            <x-core::form.label for="quantity">{{ trans('plugins/hotel::booking.quantity') }}</x-core::form.label>
                            <x-core::form.text-input
                                id="quantity"
                                name="quantity"
                                type="number"
                                min="1"
                                :value="old('quantity', 1)"
                            />
                        </div>

                        <x-core::button type="submit" color="primary">
                            {{ trans('plugins/hotel::booking.add_service') }}
                        </x-core::button>
                    </x-core::form>
                </x-core::card.body>
            </x-core::card>
        </div>
    </div>
@endsection

@push('footer')
<script>
    $(document).ready(function() {
        $('.service-type-radio').on('change', function() {
            const serviceType = $('input[name="service_type"]:checked').val();
            
            if (serviceType === 'predefined') {
                $('.predefined-service-section').show();
                $('.custom-service-section').hide();
                $('#custom_service_name').removeAttr('required');
                $('#custom_service_price').removeAttr('required');
            } else {
                $('.predefined-service-section').hide();
                $('.custom-service-section').show();
                $('#custom_service_name').attr('required', 'required');
                $('#custom_service_price').attr('required', 'required');
            }
        });

        // Trigger change event on page load
        $('.service-type-radio').trigger('change');
    });
</script>
@endpush
