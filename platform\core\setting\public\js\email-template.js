$((function(){$('[data-bb-toggle="email-status-toggle"]').on("change",(function(e){var t=$(e.currentTarget),a=t.prop("name"),o=t.data("change-url");t.closest("tr").find("td.template-name > span").toggleClass("text-muted text-decoration-line-through"),$httpClient.make().post(o,{key:a,value:t.prop("checked")?1:0}).then((function(e){var t=e.data;return Botble.showSuccess(t.message)}))})),$(document).on("click",'[data-bb-toggle="reset-default"]',(function(e){e.preventDefault(),$("#reset-template-to-default-button").data("target",$(e.currentTarget).prop("href")),$("#reset-template-to-default-modal").modal("show")})),$(document).on("click",'[data-bb-toggle="twig-variable"]',(function(e){e.preventDefault();var t=$(e.currentTarget),a=$(".CodeMirror")[0].CodeMirror,o="{{ "+t.data("key")+" }}";if(a.somethingSelected())a.replaceSelection(o);else{var n=a.getCursor(),l={line:n.line,ch:n.ch};a.replaceRange(o,l)}})),$(document).on("click",'[data-bb-toggle="twig-function"]',(function(e){e.preventDefault();var t=$(e.currentTarget);t.closest(".twig-template").find(".CodeMirror");var a=t.closest(".twig-template").find(".CodeMirror")[0].CodeMirror,o=t.data("sample");if(a.somethingSelected())a.replaceSelection(o);else{var n=a.getCursor(),l={line:n.line,ch:n.ch};a.replaceRange(o,l)}})),$(document).on("click","#reset-template-to-default-button",(function(e){e.preventDefault();var t=$(e.currentTarget);Botble.showButtonLoading(t),$httpClient.make().post(t.data("target"),{email_subject_key:$("input[name=email_subject_key]").val(),module:$("input[name=module]").val(),template_file:$("input[name=template_file]").val()}).then((function(e){var t=e.data;Botble.showSuccess(t.message),setTimeout((function(){window.location.reload()}),1e3),$("#reset-template-to-default-modal").modal("hide")})).finally((function(){Botble.hideButtonLoading(t)}))}))}));