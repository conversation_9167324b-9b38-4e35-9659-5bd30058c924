<?php

namespace Botble\Hotel\Http\Controllers;

use Botble\Base\Http\Controllers\BaseController;
use Botble\Hotel\Enums\BookingStatusEnum;
use Botble\Hotel\Models\BookingRoom;
use Botble\Hotel\Models\Room;
use Botble\Hotel\Models\RoomCategory;
use Botble\Hotel\Models\RoomDate;
use Botble\Hotel\Models\RoomInventory;
use Carbon\Carbon;
use Illuminate\Http\Request;

class RoomAvailabilityController extends BaseController
{
    public function index(Request $request)
    {
        $this->pageTitle(trans('plugins/hotel::room.room_availability'));

        try {
            // Get date range from request or use default (today to 7 days from now)
            $startDate = $request->input('start_date')
                ? Carbon::createFromFormat('Y-m-d', $request->input('start_date'))
                : Carbon::today();

            $endDate = $request->input('end_date')
                ? Carbon::createFromFormat('Y-m-d', $request->input('end_date'))
                : Carbon::today()->addDays(7);

            // Get all room categories
            $roomCategories = RoomCategory::query()->with('rooms')->get();

            // Check if the room inventory table exists
            $roomInventory = collect();
            try {
                // Get all room inventory items
                $roomInventory = RoomInventory::query()
                    ->with('room')
                    ->get()
                    ->groupBy('room_id');
            } catch (\Exception $e) {
                // Table doesn't exist, use an empty collection
                $roomInventory = collect();
            }

            // Get active bookings for the selected date range
            $activeBookings = BookingRoom::query()
                ->where(function ($query) use ($startDate, $endDate) {
                    $query->where(function ($q) use ($startDate, $endDate) {
                        // Booking start date is within our range
                        $q->whereDate('start_date', '>=', $startDate)
                          ->whereDate('start_date', '<', $endDate);
                    })->orWhere(function ($q) use ($startDate, $endDate) {
                        // Booking end date is within our range
                        $q->whereDate('end_date', '>', $startDate)
                          ->whereDate('end_date', '<=', $endDate);
                    })->orWhere(function ($q) use ($startDate, $endDate) {
                        // Booking spans our entire range
                        $q->whereDate('start_date', '<=', $startDate)
                          ->whereDate('end_date', '>=', $endDate);
                    });
                })
                ->join('ht_bookings', 'ht_bookings.id', '=', 'ht_booking_rooms.booking_id')
                ->whereNotIn('ht_bookings.status', [BookingStatusEnum::CANCELLED])
                ->select('ht_booking_rooms.*')
                ->get();

            // Also get bookings from the room_dates table
            try {
                // Check if the is_booked column exists in the room_dates table
                $hasIsBookedColumn = \Schema::hasColumn('ht_room_dates', 'is_booked');
                $hasRoomIdCodeColumn = \Schema::hasColumn('ht_room_dates', 'room_id_code');

                if ($hasIsBookedColumn && $hasRoomIdCodeColumn) {
                    $roomDateBookings = RoomDate::query()
                        ->where('is_booked', true)
                        ->where(function ($query) use ($startDate, $endDate) {
                            $query->where(function ($q) use ($startDate, $endDate) {
                                // Booking start date is within our range
                                $q->whereDate('start_date', '>=', $startDate)
                                  ->whereDate('start_date', '<', $endDate);
                            })->orWhere(function ($q) use ($startDate, $endDate) {
                                // Booking end date is within our range
                                $q->whereDate('end_date', '>', $startDate)
                                  ->whereDate('end_date', '<=', $endDate);
                            })->orWhere(function ($q) use ($startDate, $endDate) {
                                // Booking spans our entire range
                                $q->whereDate('start_date', '<=', $startDate)
                                  ->whereDate('end_date', '>=', $endDate);
                            });
                        })
                        ->get();

                    // Merge the bookings from both sources
                    foreach ($roomDateBookings as $roomDateBooking) {
                        if ($roomDateBooking->room_id_code) {
                            $activeBookings->push((object)[
                                'room_id' => $roomDateBooking->room_id,
                                'room_id_code' => $roomDateBooking->room_id_code,
                                'start_date' => $roomDateBooking->start_date,
                                'end_date' => $roomDateBooking->end_date,
                                'number_of_rooms' => 1,
                            ]);
                        }
                    }
                }
            } catch (\Exception $e) {
                // If there's an error, log it and continue
                \Log::error('Error getting room date bookings: ' . $e->getMessage());
            }

            // Create a map of booked room IDs for each date in the range
            $bookedRoomMap = [];
            $dateRange = [];

            // Create an array of all dates in the range
            for ($date = clone $startDate; $date->lt($endDate); $date->addDay()) {
                $dateString = $date->format('Y-m-d');
                $dateRange[] = $dateString;
                $bookedRoomMap[$dateString] = [];
            }

            // Fill the booked room map
            foreach ($activeBookings as $booking) {
                $bookingStartDate = Carbon::parse($booking->start_date);
                $bookingEndDate = Carbon::parse($booking->end_date);

                // For each date in the booking range that overlaps with our date range
                for ($date = max($startDate, $bookingStartDate); $date->lt(min($endDate, $bookingEndDate)); $date->addDay()) {
                    $dateString = $date->format('Y-m-d');

                    if (isset($bookedRoomMap[$dateString])) {
                        if ($booking->room_id_code) {
                            // If booking has a specific room ID, mark that room as booked
                            $bookedRoomMap[$dateString][] = $booking->room_id_code;
                        } else {
                            // If booking doesn't have a specific room ID, mark the room type as having fewer available rooms
                            $roomId = $booking->room_id;
                            $bookedRoomMap[$dateString]["room_$roomId"] = ($bookedRoomMap[$dateString]["room_$roomId"] ?? 0) + $booking->number_of_rooms;
                        }
                    }

                    // Reset date to avoid modifying the original
                    $date = clone $date;
                }
            }

            return view('plugins/hotel::room-availability.index', compact(
                'roomCategories',
                'roomInventory',
                'startDate',
                'endDate',
                'dateRange',
                'bookedRoomMap'
            ));
        } catch (\Exception $e) {
            // Log the error
            \Log::error('Error in room availability: ' . $e->getMessage());

            // Return a simple view with an error message
            return view('plugins/hotel::room-availability.error', [
                'message' => 'An error occurred while loading the room availability. Please make sure you have run the database migrations.',
                'error' => $e->getMessage()
            ]);
        }
    }

    public function updateRoomAvailability(Request $request)
    {
        try {
            $roomIdCode = $request->input('room_id_code');
            $isAvailable = $request->boolean('is_available');

            $roomInventory = RoomInventory::query()
                ->where('room_id_code', $roomIdCode)
                ->first();

            if (!$roomInventory) {
                return $this->httpResponse()
                    ->setError()
                    ->setMessage(trans('plugins/hotel::room.room_not_found'));
            }

            $roomInventory->is_available = $isAvailable;
            $roomInventory->save();

            return $this->httpResponse()
                ->setMessage(trans('plugins/hotel::room.availability_updated'));
        } catch (\Exception $e) {
            // Log the error
            \Log::error('Error updating room availability: ' . $e->getMessage());

            return $this->httpResponse()
                ->setError()
                ->setMessage('An error occurred while updating room availability. Please make sure you have run the database migrations.');
        }
    }

    public function test()
    {
        return view('plugins/hotel::room-availability.test');
    }

    
}
