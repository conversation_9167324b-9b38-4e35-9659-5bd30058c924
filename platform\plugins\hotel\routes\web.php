<?php

use Bo<PERSON>ble\Base\Facades\BaseHelper;
use Botble\Hotel\Facades\HotelHelper;
use Botble\Hotel\Http\Controllers\BookingAdditionalServicesController;
use Botble\Hotel\Http\Controllers\CouponController;
use Botble\Hotel\Http\Controllers\Front\CouponController as CouponControllerFront;
use Botble\Hotel\Http\Controllers\InvoiceController;
use Botble\Hotel\Http\Controllers\Settings\CurrencySettingController;
use Botble\Hotel\Http\Controllers\Settings\GeneralSettingController;
use Botble\Hotel\Http\Controllers\Settings\InvoiceSettingController;
use Botble\Hotel\Http\Controllers\Settings\InvoiceTemplateSettingController;
use Botble\Hotel\Http\Controllers\Settings\ReviewSettingController;
use Botble\Hotel\Models\Place;
use Botble\Hotel\Models\Room;
use Botble\Hotel\Models\Service;
use Bo<PERSON>ble\Slug\Facades\SlugHelper;
use Bo<PERSON>ble\Theme\Facades\Theme;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Route;

Route::group(['namespace' => 'Botble\Hotel\Http\Controllers', 'middleware' => ['web', 'core']], function () {
    Route::group(['prefix' => BaseHelper::getAdminPrefix() . '/hotel', 'middleware' => 'auth'], function () {
        Route::group(['prefix' => 'settings', 'as' => 'hotel.settings.', 'permission' => 'hotel.settings'], function () {
            Route::get('general', [GeneralSettingController::class, 'edit'])->name('general');
            Route::put('general', [GeneralSettingController::class, 'update'])->name('general.update');

            Route::get('review', [ReviewSettingController::class, 'edit'])->name('review');
            Route::put('review', [ReviewSettingController::class, 'update'])->name('review.update');

            Route::get('currencies', [CurrencySettingController::class, 'edit'])->name('currencies');
            Route::put('currencies', [CurrencySettingController::class, 'update'])->name('currencies.update');

            Route::get('invoice', [InvoiceSettingController::class, 'edit'])->name('invoice');
            Route::put('invoice', [InvoiceSettingController::class, 'update'])->name('invoice.update');

            Route::get('invoice-template', [InvoiceTemplateSettingController::class, 'edit'])->name('invoice-template');
            Route::put('invoice-template', [InvoiceTemplateSettingController::class, 'update'])->name('invoice-template.update');

            Route::post('invoice-template/reset', [
                'as' => 'invoice-template.reset',
                'uses' => 'Settings\InvoiceTemplateSettingController@reset',
                'permission' => 'invoice.template',
                'middleware' => 'preventDemo',
            ]);

            Route::get('invoice-template/preview', [
                'as' => 'invoice-template.preview',
                'uses' => 'Settings\InvoiceTemplateSettingController@preview',
                'permission' => 'invoice.template',
            ]);
        });

        Route::group(['prefix' => 'rooms', 'as' => 'room.'], function () {
            Route::resource('', 'RoomController')->parameters(['' => 'room']);

            Route::get('room-availability/{room}', [
                'as' => 'availability',
                'uses' => 'RoomController@getRoomAvailability',
                'permission' => 'room.availability',
            ])->wherePrimaryKey();

            Route::post('room-availability/{id}', [
                'as' => 'availability.post',
                'uses' => 'RoomController@storeRoomAvailability',
                'permission' => 'room.availability',
            ])->wherePrimaryKey();

            Route::post('update-order-by', [
                'as' => 'update-order-by',
                'uses' => 'RoomController@postUpdateOrderby',
                'permission' => 'room.edit',
            ]);
        });

        Route::group(['prefix' => 'amenities', 'as' => 'amenity.'], function () {
            Route::resource('', 'AmenityController')->parameters(['' => 'amenity']);
        });

        Route::group(['prefix' => 'foods', 'as' => 'food.'], function () {
            Route::resource('', 'FoodController')->parameters(['' => 'food']);
        });

        Route::group(['prefix' => 'food-types', 'as' => 'food-type.'], function () {
            Route::resource('', 'FoodTypeController')->parameters(['' => 'food-type']);
        });

        Route::group(['prefix' => 'bookings', 'as' => 'booking.'], function () {
            Route::resource('', 'BookingController')->parameters(['' => 'booking']);

            // Offline booking routes
            Route::get('offline/create', [
                'as' => 'offline.create',
                'uses' => 'OfflineBookingController@create',
                'permission' => 'booking.offline.create',
            ]);

            Route::post('offline/store', [
                'as' => 'offline.store',
                'uses' => 'OfflineBookingController@store',
                'permission' => 'booking.offline.create',
            ]);

            Route::get('offline/get-available-rooms', [
                'as' => 'offline.get-available-rooms',
                'uses' => 'OfflineBookingController@getAvailableRooms',
                'permission' => 'booking.offline.create',
            ]);

            // Additional services routes
            Route::get('{booking}/additional-services', [
                'as' => 'additional-services.create',
                'uses' => 'BookingAdditionalServicesController@create',
                'permission' => 'booking.additional-services',
            ])->wherePrimaryKey();

            Route::post('{booking}/additional-services', [
                'as' => 'additional-services.store',
                'uses' => 'BookingAdditionalServicesController@store',
                'permission' => 'booking.additional-services',
            ])->wherePrimaryKey();

            // Delete service from invoice
            Route::delete('{booking}/additional-services/{invoiceItemId}', [
                'as' => 'additional-services.delete',
                'uses' => 'BookingAdditionalServicesController@delete',
                'permission' => 'booking.additional-services',
            ])->wherePrimaryKey();

            // Check room availability
            Route::post('check-room-availability', [
                'as' => 'check-room-availability',
                'uses' => 'BookingController@checkRoomAvailability',
                'permission' => 'booking.edit',
            ]);
        });

        Route::get('/booking-reports/advanced', [
            'as' => 'booking.reports.advanced',
            'uses' => 'AdvancedBookingReportController@index',
            'permission' => 'booking.advance-reports.index',
        ]);

        Route::get('/booking-reports/export', [
            'uses' => 'AdvancedBookingReportController@export',
            'as' => 'booking.reports.export',
            'permission' => 'booking.reports.index',
        ]);

        Route::get('/booking-reports/advanced/data', [
            'uses' => 'AdvancedBookingReportController@getChartData',
            'as' => 'booking.reports.advanced.data',
            'permission' => 'booking.reports.index',
        ]);

        Route::get('/hotel/room-availability', [
            'uses' => 'RoomAvailabilityController@index',
            'as' => 'room.availability',
            'permission' => 'room.availability',
        ]);

        // Add an alternative route to match the URL in the error page
        Route::get('/hotel/hotel/room-availability', [
            'uses' => 'RoomAvailabilityController@index',
            'permission' => 'room.availability',
        ]);

        // Add a test route
        Route::get('/hotel/room-availability/test', [
            'uses' => 'RoomAvailabilityController@test',
            'permission' => 'booking.index',
        ]);

        // Add a room search test route
        Route::get('/room-search-test', [
            'uses' => 'RoomSearchTestController@index',
            'as' => 'room.search.test',
            'permission' => 'booking.index',
        ]);

        // Test room capacity service
        Route::get('/test-room-capacity', [
            'uses' => 'TestRoomCapacityController@testRoomCapacity',
            'as' => 'test-room-capacity',
            'permission' => 'booking.index',
        ]);

        Route::post('/room-availability/update', [
            'uses' => 'RoomAvailabilityController@updateRoomAvailability',
            'as' => 'room.availability.update',
            'permission' => 'room.availability',
        ]);

        Route::post('/room-availability/reset-all', [
            'uses' => 'RoomAvailabilityController@resetAllRooms',
            'as' => 'room.availability.reset',
            'permission' => 'room.availability',
        ]);

        Route::post('/room-availability/fix', [
            'uses' => 'RoomAvailabilityController@fixRoomAvailability',
            'as' => 'room.availability.fix',
            'permission' => 'room.availability',
        ]);

        // Development route to reset all rooms to available
        Route::get('/reset-all-rooms', [
            'uses' => 'BookingController@resetAllRooms',
            'as' => 'room.reset-all',
            'permission' => 'room.availability',
        ]);

        // Route to fix database triggers that might be causing checkout issues
        Route::get('/fix-database-triggers', function () {
            try {
                // Get all triggers in the database
                $triggers = DB::select('SHOW TRIGGERS');

                // Find triggers related to booking rooms
                foreach ($triggers as $trigger) {
                    // Check if the trigger is related to the booking_rooms table
                    if ($trigger->Table === 'ht_booking_rooms' ||
                        (isset($trigger->Statement) &&
                         (strpos($trigger->Statement, 'ht_booking_rooms') !== false ||
                          strpos($trigger->Statement, 'Cannot book an unavailable room') !== false))) {

                        // Drop the trigger
                        DB::statement("DROP TRIGGER IF EXISTS `{$trigger->Trigger}`");
                        \Illuminate\Support\Facades\Log::info("Dropped trigger: {$trigger->Trigger}");
                    }
                }

                return response()->json([
                    'success' => true,
                    'message' => 'Database triggers fixed successfully!'
                ]);
            } catch (\Exception $e) {
                \Illuminate\Support\Facades\Log::error("Error fixing database triggers: " . $e->getMessage());

                return response()->json([
                    'success' => false,
                    'message' => 'Error fixing database triggers: ' . $e->getMessage()
                ]);
            }
        })->name('fix-database-triggers');

        Route::get('/room-availability-report', [
            'uses' => 'RoomAvailabilityReportController@index',
            'as' => 'room.availability.report',
            'permission' => 'room.availability',
        ]);

        Route::get('/booking-calendar', [
            'uses' => 'BookingCalendarController@index',
            'as' => 'booking.calendar.index',
        ]);

        Route::get('/booking-reports/bookings', [
            'uses' => 'BookingReportController@index',
            'as' => 'booking.reports.index',
        ]);

        Route::post('recent-bookings', [
            'as' => 'booking.reports.recent-bookings',
            'uses' => 'BookingReportController@getRecentBookings',
            'permission' => 'booking.reports.index',
        ]);

        Route::get('/booking-reports/records', [
            'uses' => 'BookingReportRecordController@index',
            'as' => 'booking.reports.records.index',
            'permission' => 'booking.reports.index',
        ]);

        // Booking Analytics Dashboard
        Route::get('/booking-analytics', [
            'uses' => 'BookingAnalyticsController@index',
            'as' => 'booking.analytics.index',
            'permission' => 'booking.reports.index',
        ]);

        Route::get('/booking-analytics/chart-data', [
            'uses' => 'BookingAnalyticsController@getChartDataAjax',
            'as' => 'booking.analytics.chart-data',
        ]);

        Route::get('/booking-analytics/export', [
            'uses' => 'BookingAnalyticsController@export',
            'as' => 'booking.analytics.export',
            'permission' => 'booking.reports.index',
        ]);

        Route::get('/booking-simple-report', [
            'uses' => 'BookingAnalyticsController@simpleReport',
            'as' => 'booking.simple-report',
            'permission' => 'booking.reports.index',
        ]);

        Route::get('/booking-advanced-report', [
            'uses' => 'BookingAnalyticsController@advancedReport',
            'as' => 'booking.advanced-report',
            'permission' => 'booking.advance-reports.index',
        ]);

        // Simple test route to check if the controller is working
        Route::get('/booking-report-test', [
            'uses' => 'BookingAnalyticsController@simpleReport',
            'as' => 'booking.report-test',
        ]);

        // Debug route for AJAX endpoint
        Route::get('/booking-analytics/debug-chart-data', [
            'uses' => 'BookingAnalyticsController@getChartDataAjax',
            'as' => 'booking.analytics.debug-chart-data',
        ]);

        Route::group(['prefix' => 'customers', 'as' => 'customer.'], function () {
            Route::resource('', 'CustomerController')->parameters(['' => 'customer']);
        });

        Route::group(['prefix' => 'room-categories', 'as' => 'room-category.'], function () {
            Route::resource('', 'RoomCategoryController')->parameters(['' => 'room-category']);
        });

        Route::group(['prefix' => 'features', 'as' => 'feature.'], function () {
            Route::resource('', 'FeatureController')->parameters(['' => 'feature']);
        });

        Route::group(['prefix' => 'services', 'as' => 'service.'], function () {
            Route::resource('', 'ServiceController')->parameters(['' => 'service']);
        });

        Route::group(['prefix' => 'places', 'as' => 'place.'], function () {
            Route::resource('', 'PlaceController')->parameters(['' => 'place']);
        });

        Route::group(['prefix' => 'taxes', 'as' => 'tax.'], function () {
            Route::resource('', 'TaxController')->parameters(['' => 'tax']);
        });

        Route::group(['prefix' => 'invoices', 'as' => 'invoices.'], function () {
            Route::resource('', 'InvoiceController')->parameters(['' => 'invoice']);

            Route::get('{invoice}', [
                'as' => 'show',
                'uses' => 'InvoiceController@show',
                'permission' => 'booking.index',
            ])->wherePrimaryKey();

            Route::get('{invoice}/generate-invoice', [
                'as' => 'generate',
                'uses' => 'InvoiceController@getGenerateInvoice',
                'permission' => 'booking.download-invoice',
            ])->wherePrimaryKey();

            Route::get('{invoice}/view-invoice', [
                'as' => 'view',
                'uses' => 'InvoiceController@viewInvoice',
                'permission' => 'booking.view-invoice',
            ])->wherePrimaryKey();
        });

        Route::group(['prefix' => 'reviews', 'as' => 'review.'], function () {
            Route::resource('', 'ReviewController')->parameters(['' => 'review'])->only(['index', 'destroy']);
        });

        Route::group(['prefix' => 'invoices', 'as' => 'invoices.'], function () {
            Route::resource('', 'InvoiceController')->parameters(['' => 'invoice']);
            Route::get('{id}', [InvoiceController::class, 'show'])
                ->name('show')
                ->wherePrimaryKey();
            Route::get('{id}/generate-invoice', 'InvoiceController@getGenerateInvoice')
                ->name('generate')
                ->wherePrimaryKey();
        });

        Route::group(['prefix' => 'coupons', 'as' => 'coupons.'], function () {
            Route::resource('', CouponController::class)
                ->parameters(['' => 'coupon']);

            Route::post('generate-coupon', [
                'as' => 'generate-coupon',
                'uses' => 'CouponController@generateCouponCode',
                'permission' => 'coupons.index',
            ]);

            Route::delete('deletes', [
                'as' => 'deletes',
                'uses' => 'CouponController@deletes',
                'permission' => 'coupons.destroy',
            ]);
        });

        // Dynamic Pricing Routes
        Route::group(['prefix' => 'room-prices', 'as' => 'room-prices.'], function () {
            Route::get('', [
                'as' => 'index',
                'uses' => 'RoomPriceController@index',
                'permission' => 'room.edit',
            ]);

            Route::post('', [
                'as' => 'store',
                'uses' => 'RoomPriceController@store',
                'permission' => 'room.edit',
            ]);

            Route::put('{roomPrice}', [
                'as' => 'update',
                'uses' => 'RoomPriceController@update',
                'permission' => 'room.edit',
            ])->wherePrimaryKey();

            Route::delete('{roomPrice}', [
                'as' => 'destroy',
                'uses' => 'RoomPriceController@destroy',
                'permission' => 'room.edit',
            ])->wherePrimaryKey();

            Route::post('{roomPrice}/toggle-active', [
                'as' => 'toggle-active',
                'uses' => 'RoomPriceController@toggleActive',
                'permission' => 'room.edit',
            ])->wherePrimaryKey();

            Route::post('preview', [
                'as' => 'preview',
                'uses' => 'RoomPriceController@preview',
                'permission' => 'room.edit',
            ]);

            Route::post('bulk-seasonal', [
                'as' => 'bulk-seasonal',
                'uses' => 'RoomPriceController@bulkCreateSeasonal',
                'permission' => 'room.edit',
            ]);

            // Future Pricing Specific Routes
            Route::get('calendar', [
                'as' => 'calendar',
                'uses' => 'RoomPriceController@calendar',
                'permission' => 'room.edit',
            ]);

            Route::post('{roomPrice}/approve', [
                'as' => 'approve',
                'uses' => 'RoomPriceController@approve',
                'permission' => 'room.edit',
            ])->wherePrimaryKey();

            Route::get('conflicts', [
                'as' => 'conflicts',
                'uses' => 'RoomPriceController@conflicts',
                'permission' => 'room.edit',
            ]);

            Route::get('export', [
                'as' => 'export',
                'uses' => 'RoomPriceController@export',
                'permission' => 'room.edit',
            ]);

            Route::post('bulk-future', [
                'as' => 'bulk-future',
                'uses' => 'RoomPriceController@bulkFuturePricing',
                'permission' => 'room.edit',
            ]);
        });

        // Seasonal Pricing Routes
        Route::group(['prefix' => 'seasonal-pricing', 'as' => 'seasonal-pricing.'], function () {
            Route::get('', [
                'as' => 'index',
                'uses' => 'SeasonalPricingController@index',
                'permission' => 'room.edit',
            ]);

            Route::post('create-bulk', [
                'as' => 'create-bulk',
                'uses' => 'SeasonalPricingController@createBulkSeasonal',
                'permission' => 'room.edit',
            ]);

            Route::post('preview', [
                'as' => 'preview',
                'uses' => 'SeasonalPricingController@previewSeasonal',
                'permission' => 'room.edit',
            ]);

            Route::post('quick-setup', [
                'as' => 'quick-setup',
                'uses' => 'SeasonalPricingController@quickSeasonalSetup',
                'permission' => 'room.edit',
            ]);

            Route::delete('delete', [
                'as' => 'delete',
                'uses' => 'SeasonalPricingController@deleteSeasonal',
                'permission' => 'room.edit',
            ]);

            Route::get('transparency', [
                'as' => 'transparency',
                'uses' => 'SeasonalPricingController@getPricingTransparency',
                'permission' => 'room.edit',
            ]);

            Route::get('calendar', [
                'as' => 'calendar',
                'uses' => 'SeasonalPricingController@getFuturePricingCalendar',
                'permission' => 'room.edit',
            ]);
        });
    });

    if (defined('THEME_MODULE_SCREEN_NAME')) {
        Theme::registerRoutes(function () {
            Route::get(SlugHelper::getPrefix(Room::class, 'rooms'), 'PublicController@getRooms')->name('public.rooms');

            Route::get(SlugHelper::getPrefix(Room::class, 'rooms') . '/{slug}', 'PublicController@getRoom');

            Route::get(SlugHelper::getPrefix(Service::class, 'services') . '/{slug}', 'PublicController@getService');

            Route::get(SlugHelper::getPrefix(Place::class, 'places') . '/{slug}', 'PublicController@getPlace');

            Route::post('booking', 'PublicController@postBooking')->name('public.booking');
            Route::get('booking/{token}', 'PublicController@getBooking')->name('public.booking.form');

            Route::post('checkout', 'PublicController@postCheckout')->name('public.booking.checkout');

            Route::get('checkout/{transactionId}', 'PublicController@checkoutSuccess')
                ->name('public.booking.information');

            Route::prefix('coupon')->name('coupon.')->group(function () {
                Route::post('apply', [CouponControllerFront::class, 'apply'])->name('apply');
                Route::post('remove', [CouponControllerFront::class, 'remove'])->name('remove');
                Route::get('refresh', [CouponControllerFront::class, 'refresh'])->name('refresh');
            });

            Route::get('ajax/calculate-amount', 'PublicController@ajaxCalculateBookingAmount')
                ->name('public.booking.ajax.calculate-amount');

            Route::get('currency/switch/{code?}', [
                'as' => 'public.change-currency',
                'uses' => 'PublicController@changeCurrency',
            ]);

            // Route to refresh CSRF token
            Route::get('csrf-token-refresh', [
                'as' => 'public.csrf.refresh',
                'uses' => 'PublicController@refreshCsrfToken',
            ]);
        });
    }
});

if (defined('THEME_MODULE_SCREEN_NAME')) {
    Theme::registerRoutes(function () {
        Route::group([
            'namespace' => 'Botble\Hotel\Http\Controllers\Front\Customers',
            'middleware' => ['web', 'core', 'customer.guest'],
            'as' => 'customer.',
        ], function () {
            Route::get('login', 'LoginController@showLoginForm')->name('login');
            Route::post('login', 'LoginController@login')->name('login.post');

            Route::get('register', 'RegisterController@showRegistrationForm')->name('register');
            Route::post('register', 'RegisterController@register')->name('register.post');

            Route::get(
                'password/request',
                'ForgotPasswordController@showLinkRequestForm'
            )->name('password.request');
            Route::post('password/email', 'ForgotPasswordController@sendResetLinkEmail')->name('password.email');
            Route::post('password/reset', 'ResetPasswordController@reset')->name('password.reset.update');
            Route::get('password/reset/{token}', 'ResetPasswordController@showResetForm')->name('password.reset');
        });

        Route::group([
            'namespace' => 'Botble\Hotel\Http\Controllers\Front\Customers',
            'middleware' => [
                'web',
                'core',
                HotelHelper::isEnableEmailVerification() ? 'customer' : 'customer.guest',
            ],
            'as' => 'customer.',
        ], function () {
            Route::get('register/confirm/resend', 'RegisterController@resendConfirmation')
                ->name('resend_confirmation');
            Route::get('register/confirm/{user}', 'RegisterController@confirm')
                ->name('confirm');
        });

        Route::group([
            'namespace' => 'Botble\Hotel\Http\Controllers\Front\Customers',
            'middleware' => ['web', 'core', 'customer'],
            'prefix' => 'customer',
            'as' => 'customer.',
        ], function () {
            Route::get('logout', 'LoginController@logout')->name('logout');

            Route::get('overview', [
                'as' => 'overview',
                'uses' => 'PublicController@getOverview',
            ]);

            Route::get('edit-account', [
                'as' => 'edit-account',
                'uses' => 'PublicController@getEditAccount',
            ]);

            Route::post('edit-account', [
                'as' => 'edit-account.post',
                'uses' => 'PublicController@postEditAccount',
            ]);

            Route::get('change-password', [
                'as' => 'change-password',
                'uses' => 'PublicController@getChangePassword',
            ]);

            Route::post('change-password', [
                'as' => 'post.change-password',
                'uses' => 'PublicController@postChangePassword',
            ]);

            Route::post('avatar', [
                'as' => 'avatar',
                'uses' => 'PublicController@postAvatar',
            ]);

            Route::get('bookings', [
                'as' => 'bookings',
                'uses' => 'BookingController@index',
            ]);

            Route::get('bookings/{id}', [
                'as' => 'bookings.show',
                'uses' => 'BookingController@show',
            ]);

            Route::get('generate-invoice/{id}', [
                'as' => 'generate-invoice',
                'uses' => 'BookingController@getGenerateInvoice',
                'permission' => false, // Customer area doesn't need permission check
            ])->wherePrimaryKey();

            Route::get('view-invoice/{id}', [
                'as' => 'view-invoice',
                'uses' => 'BookingController@viewInvoice',
                'permission' => false, // Customer area doesn't need permission check
            ])->wherePrimaryKey();

            if (HotelHelper::isReviewEnabled()) {
                Route::get('reviews', [
                    'as' => 'reviews',
                    'uses' => 'ReviewController@index',
                ]);
            }
        });
        if (HotelHelper::isReviewEnabled()) {
            Route::group([
                'namespace' => 'Botble\Hotel\Http\Controllers\Front',
                'middleware' => ['web', 'core'],
                'prefix' => 'customer',
                'as' => 'customer.',
            ], function () {
                Route::get('ajax/review/{key}', [
                    'as' => 'ajax.review.index',
                    'uses' => 'ReviewController@index',
                ]);
                Route::post('ajax/review/{key}', [
                    'as' => 'ajax.review.store',
                    'uses' => 'ReviewController@store',
                ]);
            });
        }
    });
}

// Make sure this route exists and works
Route::get('/booking-reports/advance', [
    'as' => 'booking.advance-reports.index',
    'uses' => 'BookingReportController@advanceReport',
    'permission' => 'booking.advance-reports.index',
]);

Route::group(['prefix' => 'booking', 'as' => 'booking.'], function () {
    // Other routes...

    Route::group(['prefix' => 'advance-reports', 'as' => 'advance-reports.'], function () {
        Route::get('', [
            'as' => 'index',
            'uses' => 'BookingReportController@advancedReports',
            'permission' => 'booking.advance-reports.index',
        ]);

        // Add any AJAX endpoints needed for the reports
        Route::get('chart-data', [
            'as' => 'chart-data',
            'uses' => 'BookingReportController@getAdvancedChartData',
            'permission' => 'booking.advance-reports.index',
        ]);
    });
});

