<?php

namespace Botble\Hotel\Models;

use Botble\Base\Models\BaseModel;
use Botble\Hotel\Enums\BookingStatusEnum;
use Botble\Hotel\Facades\HotelHelper;
use Botble\Payment\Enums\PaymentStatusEnum;
use Botble\Payment\Models\Payment;
use Carbon\CarbonInterface;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Support\Facades\DB;

class Booking extends BaseModel
{
    protected $table = 'ht_bookings';

    protected $fillable = [
        'status',
        'amount',
        'sub_total',
        'coupon_amount',
        'coupon_code',
        'customer_id',
        'currency_id',
        'requests',
        'arrival_time',
        'check_in_time',
        'check_out_time',
        'number_of_guests',
        'number_of_children',
        'payment_id',
        'transaction_id',
        'tax_amount',
        'booking_number',
        'room_id_code',
        'photo',
        'proof',
        'signature',
        'meta_data',
    ];

    protected $casts = [
        'status' => BookingStatusEnum::class,
        'meta_data' => 'array',
    ];

    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class, 'customer_id')->withDefault();
    }

    public function currency(): BelongsTo
    {
        return $this->belongsTo(Currency::class, 'currency_id')->withDefault();
    }

    public function services(): BelongsToMany
    {
        return $this->belongsToMany(Service::class, 'ht_booking_services', 'booking_id', 'service_id');
    }

    public function address(): HasOne
    {
        return $this->hasOne(BookingAddress::class, 'booking_id')->withDefault();
    }

    public function room(): HasOne
    {
        return $this->hasOne(BookingRoom::class, 'booking_id')->withDefault();
    }

    public function payment(): BelongsTo
    {
        return $this->belongsTo(Payment::class, 'payment_id')->withDefault();
    }

    public function invoice(): HasOne
    {
        return $this->hasOne(Invoice::class, 'reference_id')->withDefault();
    }

    protected static function booted(): void
    {
        static::deleting(function (Booking $booking) {
            $booking->address()->delete();
            $booking->services()->detach();
            $booking->room()->delete();
        });
    }

    public static function getRevenueData(
        CarbonInterface $startDate,
        CarbonInterface $endDate,
        $select = []
    ): Collection {
        if (empty($select)) {
            $select = [
                DB::raw('DATE(payments.created_at) AS date'),
                DB::raw('SUM(COALESCE(payments.amount, 0) - COALESCE(payments.refunded_amount, 0)) as revenue'),
            ];
        }

        return self::query()
            ->join('payments', 'payments.id', '=', 'ht_bookings.payment_id')
            ->whereDate('payments.created_at', '>=', $startDate)
            ->whereDate('payments.created_at', '<=', $endDate)
            ->where('payments.status', PaymentStatusEnum::COMPLETED)
            ->groupBy('date')
            ->select($select)
            ->get();
    }

    /**
     * Calculate the total amount for this booking including services, taxes, and discounts
     */
    public function getTotalAmount(): float
    {
        try {
            // Start with room price
            $roomPrice = $this->room && $this->room->price ? (float) $this->room->price : 0;

            // Calculate service subtotal from invoice items
            $serviceSubtotal = 0;
            if ($this->invoice && $this->invoice->items) {
                $serviceSubtotal = $this->invoice->items
                    ->filter(function($item) {
                        return strpos($item->name, 'additional service') !== false ||
                               strpos($item->name, 'extra service') !== false ||
                               strpos($item->name, '(extra service)') !== false;
                    })
                    ->sum('amount');
            }

            // Calculate subtotal (room + services)
            $subtotal = $roomPrice + $serviceSubtotal;

            // Get discount and tax amounts
            $couponAmount = (float) ($this->coupon_amount ?? 0);
            $taxAmount = (float) ($this->tax_amount ?? 0);

            // Calculate final total: (room + services) - discount + tax
            $total = $subtotal - $couponAmount + $taxAmount;

            return $total;
        } catch (\Exception $e) {
            // Fallback: calculate based on room price and tax only
            $roomPrice = $this->room && $this->room->price ? (float) $this->room->price : 0;
            $taxAmount = (float) ($this->tax_amount ?? 0);
            $couponAmount = (float) ($this->coupon_amount ?? 0);

            return $roomPrice - $couponAmount + $taxAmount;
        }
    }

    public static function generateUniqueBookingNumber(): string
    {
        $nextInsertId = BaseModel::determineIfUsingUuidsForId() ?
            static::query()->count() + 1 :
            static::query()->max('id') + 1;

        do {
            $code = HotelHelper::getBookingNumber($nextInsertId);
            $nextInsertId++;
        } while (static::query()->where('booking_number', $code)->exists());

        return $code;
    }
}
