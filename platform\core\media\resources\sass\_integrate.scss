.rv-media-breadcrumb {
    .breadcrumb {
        li:first-child {
            &:before {
                display: none !important;
            }
        }
    }
}

.rv-media-container {
    height: calc(100vh - 16rem);

    .js-rv-media-filter-current {
        .icon {
            margin-inline-end: 2px;
        }

        .dropdown-item-icon {
            color: unset;
            margin-right: unset;
            opacity: unset;
        }
    }
}

@media (max-width: 991px) {
    .rv-media-container {
        .rv-media-aside {
            z-index: 10000;
        }
    }
}

.rv-media-integrate-wrapper {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.rv-media-modal {
    .modal-open {
        .context-menu-root {
            z-index: 99994 !important;
        }
    }
}
