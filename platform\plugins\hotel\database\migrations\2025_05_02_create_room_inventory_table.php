<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // This migration does nothing - it's just to mark the room inventory as handled
        // The actual table already exists, so we skip creation
        
        if (Schema::hasTable('ht_room_inventory')) {
            // Table exists, nothing to do
            return;
        }
        
        // If for some reason the table doesn't exist, create it
        Schema::create('ht_room_inventory', function (Blueprint $table) {
            $table->id();
            $table->foreignId('room_id')->references('id')->on('ht_rooms')->onDelete('cascade');
            $table->string('room_id_code', 20);
            $table->boolean('is_available')->default(true);
            $table->timestamps();
            
            $table->unique(['room_id', 'room_id_code']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Don't drop the table in down() to prevent data loss
        // Schema::dropIfExists('ht_room_inventory');
    }
};