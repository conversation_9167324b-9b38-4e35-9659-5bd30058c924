@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="card">
        <div class="card-header">
            <h4 class="card-title">{{ trans('plugins/hotel::booking.advanced_reports') }}</h4>
        </div>
        <div class="card-body">
            <div class="alert alert-danger">
                <h4 class="alert-heading">{{ __('Error Loading Report') }}</h4>
                <p>{{ $message ?? __('An error occurred while loading the advanced booking report.') }}</p>
                @if(isset($error) && app()->environment('local'))
                    <hr>
                    <p class="mb-0"><strong>{{ __('Error details:') }}</strong> {{ $error }}</p>
                @endif
            </div>
            
            <div class="mt-4">
                <a href="{{ route('booking.reports.index') }}" class="btn btn-secondary">
                    <i class="fa fa-arrow-left"></i> {{ __('Back to Basic Reports') }}
                </a>
            </div>
        </div>
    </div>
@stop
