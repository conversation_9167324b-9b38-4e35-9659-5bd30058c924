<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Only create table if it doesn't exist
        if (!Schema::hasTable('ht_room_inventory')) {
            Schema::create('ht_room_inventory', function (Blueprint $table) {
                $table->id();
                $table->foreignId('room_id')->references('id')->on('ht_rooms')->onDelete('cascade');
                $table->string('room_id_code', 20);
                $table->boolean('is_available')->default(true);
                $table->timestamps();

                $table->unique(['room_id', 'room_id_code']);
            });
        }

        // Only add column if it doesn't exist
        if (!Schema::hasColumn('ht_booking_rooms', 'room_id_code')) {
            Schema::table('ht_booking_rooms', function (Blueprint $table) {
                $table->string('room_id_code', 20)->nullable()->after('room_id');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Only drop column if it exists
        if (Schema::hasColumn('ht_booking_rooms', 'room_id_code')) {
            Schema::table('ht_booking_rooms', function (Blueprint $table) {
                $table->dropColumn('room_id_code');
            });
        }

        // Drop table if it exists
        Schema::dropIfExists('ht_room_inventory');
    }
};
