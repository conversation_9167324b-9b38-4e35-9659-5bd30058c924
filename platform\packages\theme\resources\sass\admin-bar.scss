body.show-admin-bar {
    margin-top: 40px !important;
    position: relative;
}

#admin_bar {
    height: 40px;
    background-color: #333333;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    min-width: 960px;
    font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
    z-index: 1049; // Bootstrap modal is 1050
    direction: ltr !important;

    &,
    * {
        color: #ffffff;
        box-sizing: border-box;
    }

    a {
        text-decoration: none;
        font-size: 14px;
    }

    ul {
        list-style: none;
    }

    .admin-bar-container {
        display: block;
        width: 100%;
        margin: 0;
        padding: 0 15px;

        &:before,
        &:after {
            content: '';
            display: table;
            clear: both;
        }
    }

    .admin-bar-logo {
        float: left;
        height: 40px;
        margin-right: 15px;
        padding: 5px 0;

        img {
            max-height: 30px;
            width: auto;
        }
    }

    .admin-navbar-nav {
        margin: 0;
        padding: 0 15px;
        float: left;

        li {
            position: relative;
            height: 40px;
            line-height: 40px;
        }

        > li {
            float: left;
            margin: 0 0 0 15px;

            &:first-child {
                margin-left: 0;
            }

            > a {
                color: #cccccc;

                &:hover {
                    color: #ffffff;
                }
            }
        }

        .admin-bar-dropdown {
            > a {
                &:after {
                    content: '';
                    $left-right: 8px * 0.5;
                    $top: 6px;
                    border-left: $left-right solid transparent;
                    border-right: $left-right solid transparent;
                    border-top: $top solid #ffffff;
                    display: inline-block;
                    margin-left: 5px;
                    float: none;
                }
            }

            &:hover > .admin-bar-dropdown-menu {
                display: block;
            }
        }

        .admin-bar-dropdown-menu {
            position: absolute;
            top: 100%;
            left: 0;
            white-space: nowrap;
            margin: -1px 0 0 0;
            background-color: #ffffff;
            padding: 5px 0;
            border-radius: 0;
            z-index: 1000;
            display: none;
            float: left;
            min-width: 160px;
            list-style: none;
            font-size: 14px;
            text-align: left;
            border: 1px solid #cccccc;
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
            background-clip: padding-box;

            &:before {
                content: '';
                display: inline-block;
                $left-right: 10px * 0.5;
                $bottom: 8px;
                border-left: $left-right solid transparent;
                border-right: $left-right solid transparent;
                border-bottom: $bottom solid #ffffff;
                position: absolute;
                left: 3px;
                top: -8px;
            }

            * {
                color: #333333;
            }

            li {
                line-height: 1;
                padding: 2px 0;
                height: auto;

                a {
                    display: block;
                    padding: 5px;

                    &:hover {
                        background-color: #cccccc;
                    }
                }
            }
        }
    }

    .admin-navbar-nav-right {
        float: right;

        .admin-bar-dropdown-menu {
            left: auto;
            right: 0;

            &:before {
                left: auto;
                right: 3px;
            }
        }
    }
}
