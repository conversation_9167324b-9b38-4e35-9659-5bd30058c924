<?php

namespace Botble\Hotel\Listeners;

use Botble\Hotel\Enums\BookingStatusEnum;
use Botble\Hotel\Events\BookingStatusChanged;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class UpdateCheckInOutTimeListener
{
    /**
     * Handle the event.
     *
     * @param BookingStatusChanged $event
     * @return void
     */
    public function handle(BookingStatusChanged $event): void
    {
        $booking = $event->booking;
        $oldStatus = $event->oldStatus;
        $newStatus = $booking->status->getValue();

        // Log all status changes for debugging
        Log::info("BookingStatusChanged event received for booking #{$booking->id}. Old status: {$oldStatus}, New status: {$newStatus}");

        try {
            // When status changes to "processing", update check_in_time if not already set
            if ($newStatus === BookingStatusEnum::PROCESSING && empty($booking->check_in_time)) {
                Log::info("Processing status detected for booking #{$booking->id}. Updating check-in time.");
                // Store the current system time with explicit IST timezone
                $booking->check_in_time = Carbon::now('Asia/Kolkata');
                $booking->save();

                Log::info("Updated check-in time for booking #{$booking->id} to " . $booking->check_in_time);
            }

            // When status changes to "completed", update check_out_time if not already set
            if ($newStatus === BookingStatusEnum::COMPLETED && empty($booking->check_out_time)) {
                Log::info("Completed status detected for booking #{$booking->id}. Updating check-out time.");
                // Store the current system time with explicit IST timezone
                $booking->check_out_time = Carbon::now('Asia/Kolkata');
                $booking->save();

                Log::info("Updated check-out time for booking #{$booking->id} to " . $booking->check_out_time);
            }

            // If status changed from "completed" or "cancelled" back to "pending" or "processing",
            // clear the check-out time
            if (($oldStatus === 'completed' || $oldStatus === 'cancelled') &&
                ($newStatus === BookingStatusEnum::PENDING || $newStatus === BookingStatusEnum::PROCESSING)) {
                $booking->check_out_time = null;
                $booking->save();
                Log::info("Check-out time cleared for booking #{$booking->id} due to status change from {$oldStatus} to {$newStatus}");
            }

            // Always update the invoice with the latest check-in and check-out times
            if ($booking->invoice) {
                $booking->invoice->check_in_time = $booking->check_in_time;
                $booking->invoice->check_out_time = $booking->check_out_time;
                $booking->invoice->save();

                Log::info("Updated invoice for booking #{$booking->id} with check-in/out times");
            }
        } catch (\Exception $e) {
            Log::error("Error updating check-in/out times: " . $e->getMessage());
        }
    }
}
