<?php

namespace Botble\Hotel\Http\Requests;

use Bo<PERSON>ble\Base\Enums\BaseStatusEnum;
use Bo<PERSON>ble\Base\Rules\OnOffRule;
use Botble\Hotel\Models\RoomCategory;
use Botble\Support\Http\Requests\Request;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Validator;

class RoomRequest extends Request
{
    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'description' => ['nullable', 'string', 'max:400'],
            'status' => Rule::in(BaseStatusEnum::values()),
            'is_featured' => new OnOffRule(),
            'content' => ['nullable', 'string', 'max:100000'],
            'order' => ['nullable', 'numeric'],
            'price' => ['nullable', 'numeric'],
            'number_of_rooms' => ['nullable', 'integer', 'min:1'],
            'number_of_beds' => ['nullable', 'integer', 'min:0'],
            'max_adults' => ['nullable', 'numeric'],
            'max_children' => ['nullable', 'numeric'],
            'size' => ['nullable', 'numeric'],
            'images' => ['nullable', 'array'],
            'images.*' => ['nullable', 'string'],
            'room_category_id' => ['required', 'string', 'exists:ht_room_categories,id'],
            'tax_id' => ['required', 'string', 'exists:ht_taxes,id'],
        ];
    }

    public function withValidator(Validator $validator): void
    {
        $validator->after(function (Validator $validator) {
            $data = $validator->getData();

            // Only validate if both max_adults and max_children are provided
            if (isset($data['room_category_id']) && isset($data['max_adults']) && isset($data['max_children'])) {
                $roomCategory = RoomCategory::find($data['room_category_id']);

                $adults = (int)$data['max_adults'];
                $children = (int)$data['max_children'];
                $totalOccupancy = $adults + $children;

                // Standard Room validation
                if ($roomCategory && $roomCategory->name === 'Standard Room') {
                    // Standard Room validation rules
                    $validCombinations = [
                        ['adults' => 1, 'children' => 2],
                        ['adults' => 2, 'children' => 1],
                        ['adults' => 3, 'children' => 0],
                        ['adults' => 1, 'children' => 1],
                        ['adults' => 1, 'children' => 0],
                        ['adults' => 2, 'children' => 0],
                    ];

                    $isValid = false;
                    foreach ($validCombinations as $combination) {
                        if ($combination['adults'] === $adults && $combination['children'] === $children) {
                            $isValid = true;
                            break;
                        }
                    }

                    if (!$isValid) {
                        $validator->errors()->add(
                            'max_adults',
                            'For Standard Room, only specific combinations are allowed: 2 adults and 1 child, 1 adult and 2 children, 3 adults, 1 adult and 1 child, 1 adult only, or 2 adults only.'
                        );
                        $validator->errors()->add(
                            'max_children',
                            'For Standard Room, only specific combinations are allowed: 2 adults and 1 child, 1 adult and 2 children, 3 adults, 1 adult and 1 child, 1 adult only, or 2 adults only.'
                        );
                    }
                }

                // Double Bed Room validation
                if ($roomCategory && $roomCategory->name === 'Double Bed Room') {
                    // Double Bed Room validation rules - all combinations with max 4 people except 4 adults
                    $validCombinations = [
                        ['adults' => 3, 'children' => 1],
                        ['adults' => 3, 'children' => 0],
                        ['adults' => 2, 'children' => 2],
                        ['adults' => 2, 'children' => 1],
                        ['adults' => 2, 'children' => 0],
                        ['adults' => 1, 'children' => 3],
                        ['adults' => 1, 'children' => 2],
                        ['adults' => 1, 'children' => 1],
                        ['adults' => 1, 'children' => 0],
                    ];

                    $isValid = false;
                    foreach ($validCombinations as $combination) {
                        if ($combination['adults'] === $adults && $combination['children'] === $children) {
                            $isValid = true;
                            break;
                        }
                    }

                    if (!$isValid) {
                        // Create a more descriptive error message that lists all allowed combinations
                        $allowedCombinationsText = '3 adults and 1 child, 3 adults only, 2 adults and 2 children, 2 adults and 1 child, 2 adults only, 1 adult and 3 children, 1 adult and 2 children, 1 adult and 1 child, or 1 adult only';

                        $errorMessage = 'For Double Bed Room, only these combinations are allowed: ' . $allowedCombinationsText . '. The combination of 4 adults is explicitly not allowed.';

                        $validator->errors()->add('max_adults', $errorMessage);
                        $validator->errors()->add('max_children', $errorMessage);
                    }
                }
            }
        });
    }
}
