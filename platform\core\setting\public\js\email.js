$((function(){$('[data-bb-toggle="test-email-send"]').on("click",(function(t){t.preventDefault();var e=$(t.currentTarget),n=new FormData(e.closest("form")[0]);Botble.showButtonLoading(e),$httpClient.make().postForm(e.data("url"),n).then((function(t){var e=t.data;Botble.showSuccess(e.message),$("#send-test-email-modal").modal("show")})).finally((function(){Botble.hideButtonLoading(e)}))})),$("#send-test-email-btn").on("click",(function(t){t.preventDefault();var e=$(t.currentTarget);Botble.showButtonLoading(e),$httpClient.make().post(e.data("url"),{email:e.closest(".modal-content").find("input[name=email]").val(),template:e.closest(".modal-content").find("select[name=template]").val()}).then((function(t){var n=t.data;Botble.showSuccess(n.message),e.closest(".modal").modal("hide")})).finally((function(){Botble.hideButtonLoading(e)}))}))}));