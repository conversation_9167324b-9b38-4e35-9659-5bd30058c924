@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="widget meta-boxes">
                <div class="widget-title">
                    <h4>
                        <span>{{ trans('plugins/hotel::room.room_availability') }}</span>
                    </h4>
                </div>
                <div class="widget-body">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <form action="{{ route('room.availability') }}" method="GET" class="form-inline">
                                <div class="input-group mb-3 me-2">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">{{ trans('plugins/hotel::room.start_date') }}</span>
                                    </div>
                                    <input
                                        type="date"
                                        class="form-control"
                                        name="start_date"
                                        value="{{ $startDate->format('Y-m-d') }}"
                                    >
                                </div>
                                <div class="input-group mb-3 me-2">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">{{ trans('plugins/hotel::room.end_date') }}</span>
                                    </div>
                                    <input
                                        type="date"
                                        class="form-control"
                                        name="end_date"
                                        value="{{ $endDate->format('Y-m-d') }}"
                                    >
                                </div>
                                <button type="submit" class="btn btn-primary mb-3">{{ trans('filter') }}</button>
                            </form>
                        </div>
                       
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-3">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h5 class="card-title">{{ trans('plugins/hotel::room.date_range') }}</h5>
                                    <p class="card-text">{{ $startDate->format('M d, Y') }} - {{ $endDate->format('M d, Y') }}</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body">
                                    <h5 class="card-title">{{ trans('plugins/hotel::room.total_rooms') }}</h5>
                                    <h2 class="card-text">{{ $roomInventory->flatten()->count() }}</h2>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body">
                                    <h5 class="card-title">{{ trans('plugins/hotel::room.available_rooms') }}</h5>
                                    <h2 class="card-text">{{ $roomInventory->flatten()->where('is_available', true)->count() }}</h2>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-danger text-white">
                                <div class="card-body">
                                    <h5 class="card-title">{{ trans('plugins/hotel::room.unavailable_rooms') }}</h5>
                                    <h2 class="card-text">{{ $roomInventory->flatten()->where('is_available', false)->count() }}</h2>
                                </div>
                            </div>
                        </div>
                    </div>

                    @if($roomInventory->isEmpty())
                    <div class="alert alert-warning">
                        <h4>{{ __('No room inventory found') }}</h4>
                        <p>{{ __('Please run the SQL migration file to create the room inventory table and seed it with data.') }}</p>
                        <p>{{ __('You can find the SQL migration file at:') }} <code>room_inventory_migration.sql</code></p>
                    </div>
                    @endif

                    @if(!$roomInventory->isEmpty())
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover">
                            <thead>
                                <tr>
                                    <th>{{ trans('plugins/hotel::room.room_category') }}</th>
                                    <th>{{ trans('plugins/hotel::room.room_name') }}</th>
                                    <th>{{ trans('plugins/hotel::room.room_id_code') }}</th>
                                    <th>{{ trans('plugins/hotel::room.status') }}</th>
                                    @foreach($dateRange as $date)
                                        <th>{{ \Carbon\Carbon::parse($date)->format('M d') }}</th>
                                    @endforeach
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($roomCategories as $category)
                                    @foreach($category->rooms as $room)
                                        @if(isset($roomInventory[$room->id]))
                                            @foreach($roomInventory[$room->id] as $inventoryItem)
                                                <tr>
                                                    <td>{{ $category->name }}</td>
                                                    <td>{{ $room->name }}</td>
                                                    <td>{{ $inventoryItem->room_id_code }}</td>
                                                    <td>
                                                        <div class="form-check form-switch">
                                                            <input
                                                                class="form-check-input toggle-room-availability"
                                                                type="checkbox"
                                                                id="room-{{ $inventoryItem->id }}"
                                                                data-room-id-code="{{ $inventoryItem->room_id_code }}"
                                                                {{ $inventoryItem->is_available ? 'checked' : '' }}
                                                            >
                                                            <label class="form-check-label" for="room-{{ $inventoryItem->id }}">
                                                                {{ $inventoryItem->is_available ? trans('plugins/hotel::room.available') : trans('plugins/hotel::room.unavailable') }}
                                                            </label>
                                                        </div>
                                                    </td>
                                                    @foreach($dateRange as $date)
                                                        <td class="{{ in_array($inventoryItem->room_id_code, $bookedRoomMap[$date] ?? []) ? 'bg-danger text-white' : 'bg-success text-white' }}">
                                                            {{ in_array($inventoryItem->room_id_code, $bookedRoomMap[$date] ?? []) ? trans('plugins/hotel::room.booked') : trans('plugins/hotel::room.free') }}
                                                        </td>
                                                    @endforeach
                                                </tr>
                                            @endforeach
                                        @else
                                            <tr>
                                                <td>{{ $category->name }}</td>
                                                <td>{{ $room->name }}</td>
                                                <td colspan="{{ count($dateRange) + 2 }}">{{ trans('plugins/hotel::room.no_inventory') }}</td>
                                            </tr>
                                        @endif
                                    @endforeach
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                    @else
                    <div class="alert alert-info">
                        <h4>{{ __('Room inventory table is empty') }}</h4>
                        <p>{{ __('Please run the SQL migration file to seed the room inventory table with data.') }}</p>
                        <p>{{ __('You can find the SQL migration file at:') }} <code>room_inventory_migration.sql</code></p>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
@endsection

@push('footer')
<script>
    $(document).ready(function() {
        // Handle room availability toggle
        $('.toggle-room-availability').on('change', function() {
            const roomIdCode = $(this).data('room-id-code');
            const isAvailable = $(this).prop('checked');
            const label = $(this).next('label');

            $.ajax({
                url: '{{ route('room.availability.update') }}',
                type: 'POST',
                data: {
                    room_id_code: roomIdCode,
                    is_available: isAvailable,
                    _token: '{{ csrf_token() }}'
                },
                success: function(response) {
                    if (response.error) {
                        Botble.showError(response.message);
                    } else {
                        Botble.showSuccess(response.message);
                        label.text(isAvailable ? '{{ trans('plugins/hotel::room.available') }}' : '{{ trans('plugins/hotel::room.unavailable') }}');
                    }
                },
                error: function(error) {
                    Botble.showError('{{ trans('plugins/hotel::room.update_failed') }}');
                }
            });
        });

        // Handle refresh button
        $('#refresh-page').on('click', function() {
            $(this).prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> Refreshing...');
            location.reload();
        });

        // Auto-refresh the page every 30 seconds
        const autoRefreshInterval = 300000; // 30 seconds
        let autoRefreshTimer = setTimeout(function refreshPage() {
            // Only refresh if the page is not actively being used
            if (!$(':focus').length) {
                location.reload();
            } else {
                // If the page is being used, check again after the interval
                autoRefreshTimer = setTimeout(refreshPage, autoRefreshInterval);
            }
        }, autoRefreshInterval);

        // Reset the timer when user interacts with the page
        $(document).on('click keypress', function() {
            clearTimeout(autoRefreshTimer);
            autoRefreshTimer = setTimeout(function() {
                location.reload();
            }, autoRefreshInterval);
        });

        // Handle reset all rooms button
        $('#reset-all-rooms').on('click', function() {
            if (confirm('Are you sure you want to reset ALL rooms to available? This is for development purposes only.')) {
                $(this).prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> Resetting...');

                $.ajax({
                    url: '{{ route('room.availability.reset') }}',
                    type: 'POST',
                    data: {
                        _token: '{{ csrf_token() }}'
                    },
                    success: function(response) {
                        if (response.error) {
                            Botble.showError(response.message);
                        } else {
                            Botble.showSuccess(response.message);
                            // Reload the page after a short delay
                            setTimeout(function() {
                                location.reload();
                            }, 1500);
                        }
                    },
                    error: function(error) {
                        Botble.showError('Failed to reset rooms. Please try again.');
                        $('#reset-all-rooms').prop('disabled', false).html('<i class="fa fa-refresh"></i> Reset All Rooms (Dev Only)');
                    }
                });
            }
        });
    });
</script>
@endpush
