(()=>{function t(e){return t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},t(e)}function e(t,e){for(var i=0;i<e.length;i++){var n=e[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,o(n.key),n)}}function o(e){var o=function(e,o){if("object"!=t(e)||!e)return e;var i=e[Symbol.toPrimitive];if(void 0!==i){var n=i.call(e,o||"default");if("object"!=t(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===o?String:Number)(e)}(e,"string");return"symbol"==t(o)?o:o+""}var i=function(){return t=function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t)},(o=[{key:"init",value:function(){var t=[{name:"wrap-widgets",pull:"clone",put:!1}];$.each($(".sidebar-item"),(function(){t.push({name:"wrap-widgets",pull:!0,put:!0})}));var e=function(t){if(t.length>0){var e=[];$.each(t.find("li[data-id]"),(function(t,o){e.push($(o).find("form").serialize())})),$httpClient.make().post(BWidget.routes.save_widgets_sidebar,{items:e,sidebar_id:t.data("id")}).then((function(e){var o=e.data;t.find("ul").html(o.data),Botble.callScroll($(".list-page-select-widget")),Botble.initResources(),Botble.initMediaIntegrate(),Botble.initTreeCheckboxes(),Botble.showSuccess(o.message)})).finally((function(){t.find(".widget-save i").remove()}))}};t.forEach((function(t,o){Sortable.create(document.getElementById("wrap-widget-"+(o+1)),{sort:0!==o,group:t,delay:0,disabled:!1,store:null,animation:150,handle:".card-header",ghostClass:"sortable-ghost",chosenClass:"sortable-chosen",dataIdAttr:"data-id",forceFallback:!1,fallbackClass:"sortable-fallback",fallbackOnBody:!1,scroll:!0,scrollSensitivity:30,scrollSpeed:10,onUpdate:function(t){t.from!==t.to&&e($(t.from).closest(".sidebar-item")),e($(t.item).closest(".sidebar-item"))},onAdd:function(t){t.from!==t.to&&e($(t.from).closest(".sidebar-item")),e($(t.item).closest(".sidebar-item"))}})})),$("#wrap-widgets").on("click",".widget-control-delete",(function(t){t.preventDefault();var e=$(t.currentTarget),o=e.closest("li");Botble.showButtonLoading(e),$httpClient.make().delete(BWidget.routes.delete,{widget_id:o.data("id"),position:o.data("position"),sidebar_id:e.closest(".sidebar-item").data("id")}).then((function(t){var o=t.data;Botble.showSuccess(o.message),e.closest(".sidebar-item").find("ul").html(o.data)})).finally((function(){Botble.showButtonLoading(o.find(".widget-control-delete"))}))})).on("click",".widget-item .card-header",(function(t){var e=$(t.currentTarget);e.closest(".widget-item").find(".widget-content").slideToggle(300),e.find(".ti").hasClass("ti-chevron-up")?setTimeout((function(){e.closest(".card").toggleClass("card-no-border-bottom-radius")}),300):e.closest(".card").toggleClass("card-no-border-bottom-radius"),e.find(".ti").toggleClass("ti-chevron-down").toggleClass("ti-chevron-up")})).on("click",".sidebar-item .card-header .button-sidebar.btn-action",(function(t){var e=$(t.currentTarget);e.closest(".card").find(".card-body").slideToggle(300),e.find(".ti").toggleClass("ti-chevron-down").toggleClass("ti-chevron-up")})).on("click",".widget-save",(function(t){t.preventDefault();var o=$(t.currentTarget);Botble.showButtonLoading(o),e(o.closest(".sidebar-item"))}))}}])&&e(t.prototype,o),i&&e(t,i),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,o,i}();$((function(){(new i).init()}))})();