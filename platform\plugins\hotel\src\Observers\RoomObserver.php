<?php

namespace Botble\Hotel\Observers;

use Botble\Hotel\Models\Room;
use Botble\Hotel\Models\RoomInventory;
use Illuminate\Support\Facades\Log;

class RoomObserver
{
    /**
     * Handle the Room "created" event.
     */
    public function created(Room $room): void
    {
        $this->createRoomInventoryEntries($room);
    }

    /**
     * Create room inventory entries for a new room
     */
    private function createRoomInventoryEntries(Room $room): void
    {
        try {
            // Generate room ID codes based on room name and ID
            $roomCodes = $this->generateRoomCodes($room);
            
            // Create inventory entries for each code
            foreach ($roomCodes as $code) {
                RoomInventory::create([
                    'room_id' => $room->id,
                    'room_id_code' => $code,
                    'is_available' => true,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }
            
            Log::info("Created room inventory entries for room #{$room->id}: " . implode(', ', $roomCodes));
        } catch (\Exception $e) {
            Log::error("Error creating room inventory entries: " . $e->getMessage());
        }
    }
    
    /**
     * Generate room codes based on room name and ID
     */
    private function generateRoomCodes(Room $room): array
    {
        $codes = [];
        $roomName = $room->name;
        $roomId = $room->id;
        
        // Clean the room name to use as a prefix
        $prefix = preg_replace('/[^A-Za-z0-9]/', '', $roomName);
        $prefix = strtoupper(substr($prefix, 0, 3));
        
        // If we couldn't generate a prefix, use 'MRR'
        if (empty($prefix)) {
            $prefix = 'MRR';
        }
        
        // Generate codes based on room type
        $numberOfRooms = $room->number_of_rooms ?? 1;
        
        // Generate a unique code for each room
        for ($i = 1; $i <= $numberOfRooms; $i++) {
            $codes[] = $prefix . '-' . $roomId . sprintf('%02d', $i);
        }
        
        return $codes;
    }
}
