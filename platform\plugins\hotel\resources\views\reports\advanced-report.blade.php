@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="widget meta-boxes">
                <div class="widget-title">
                    <h4>
                        <span>Advanced Booking Report</span>
                    </h4>
                </div>
                <div class="widget-body">
                    <!-- Filters Section -->
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-body">
                                    <form action="{{ route('booking.advanced-report') }}" method="GET" class="form-inline">
                                        <div class="row w-100">
                                            <div class="col-md-4 mb-3">
                                                <div class="form-group w-100">
                                                    <label for="date-range" class="mb-2">Date Range:</label>
                                                    <div class="input-group w-100">
                                                        <input type="text" name="date_range" id="date-range" class="form-control date-range-picker w-100"
                                                            value="{{ $startDate->format('Y-m-d') }} - {{ $endDate->format('Y-m-d') }}"
                                                            placeholder="Select date range">
                                                        <input type="hidden" name="start_date" value="{{ $startDate->format('Y-m-d') }}">
                                                        <input type="hidden" name="end_date" value="{{ $endDate->format('Y-m-d') }}">
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-4 mb-3">
                                                <div class="form-group w-100">
                                                    <label for="room-category" class="mb-2">Room Category:</label>
                                                    <select name="room_category_id" id="room-category" class="form-control w-100">
                                                        <option value="">All Categories</option>
                                                        @foreach($roomCategories as $id => $name)
                                                            <option value="{{ $id }}" {{ $roomCategoryId == $id ? 'selected' : '' }}>{{ $name }}</option>
                                                        @endforeach
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-md-4 mb-3">
                                                <div class="form-group w-100">
                                                    <label for="group-by" class="mb-2">Group By:</label>
                                                    <select name="group_by" id="group-by" class="form-control w-100">
                                                        <option value="day" {{ $groupBy == 'day' ? 'selected' : '' }}>Day</option>
                                                        <option value="week" {{ $groupBy == 'week' ? 'selected' : '' }}>Week</option>
                                                        <option value="month" {{ $groupBy == 'month' ? 'selected' : '' }}>Month</option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row w-100">
                                            <div class="col-md-12 text-center mt-2">
                                                <button type="submit" class="btn btn-primary px-4 py-2">
                                                    <i class="fas fa-filter"></i> Apply Filters
                                                </button>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Key Metrics Section -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white h-100">
                                <div class="card-body d-flex flex-column">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="card-title mb-0">Total Bookings</h6>
                                            <h2 class="mt-2 mb-0" id="total-bookings">{{ number_format($metrics['totalBookings']) }}</h2>
                                        </div>
                                        <div>
                                            <i class="fas fa-calendar-check fa-3x opacity-50"></i>
                                        </div>
                                    </div>
                                    <div class="mt-auto pt-3">
                                        <span id="booking-growth" class="badge {{ $metrics['bookingGrowth'] > 0 ? 'bg-success' : ($metrics['bookingGrowth'] < 0 ? 'bg-danger' : 'bg-secondary') }}">
                                            {{ $metrics['bookingGrowth'] > 0 ? '+' : '' }}{{ number_format($metrics['bookingGrowth'], 1) }}%
                                        </span>
                                        <span class="text-white-50 ml-1">vs previous period</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white h-100">
                                <div class="card-body d-flex flex-column">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="card-title mb-0">Total Revenue</h6>
                                            <h2 class="mt-2 mb-0" id="total-revenue">₹{{ number_format($metrics['totalRevenue']) }}</h2>
                                        </div>
                                        <div>
                                            <i class="fas fa-rupee-sign fa-3x opacity-50"></i>
                                        </div>
                                    </div>
                                    <div class="mt-auto pt-3">
                                        <span id="revenue-growth" class="badge {{ $metrics['revenueGrowth'] > 0 ? 'bg-success' : ($metrics['revenueGrowth'] < 0 ? 'bg-danger' : 'bg-secondary') }}">
                                            {{ $metrics['revenueGrowth'] > 0 ? '+' : '' }}{{ number_format($metrics['revenueGrowth'], 1) }}%
                                        </span>
                                        <span class="text-white-50 ml-1">vs previous period</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white h-100">
                                <div class="card-body d-flex flex-column">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="card-title mb-0">Avg. Booking Value</h6>
                                            <h2 class="mt-2 mb-0" id="avg-booking-value">₹{{ number_format($metrics['avgBookingValue']) }}</h2>
                                        </div>
                                        <div>
                                            <i class="fas fa-chart-line fa-3x opacity-50"></i>
                                        </div>
                                    </div>
                                    <div class="mt-auto pt-3">
                                        <span id="completion-rate" class="badge bg-light text-dark">{{ number_format($metrics['completionRate'], 1) }}%</span>
                                        <span class="text-white-50 ml-1">completion rate</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white h-100">
                                <div class="card-body d-flex flex-column">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="card-title mb-0">Cancellation Rate</h6>
                                            <h2 class="mt-2 mb-0" id="cancellation-rate">{{ number_format($metrics['cancellationRate'], 1) }}%</h2>
                                        </div>
                                        <div>
                                            <i class="fas fa-ban fa-3x opacity-50"></i>
                                        </div>
                                    </div>
                                    <div class="mt-auto pt-3">
                                        <span id="cancelled-bookings" class="badge bg-light text-dark">{{ number_format($metrics['cancelledBookings']) }}</span>
                                        <span class="text-white-50 ml-1">cancelled bookings</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Charts Section -->
                    <div class="row mb-4">
                        <div class="col-md-8">
                            <div class="card">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <h5 class="mb-0">Booking & Revenue Trends</h5>
                                    <div class="d-flex align-items-center">
                                        <button type="button" class="btn btn-sm btn-outline-primary me-2" id="refresh-charts" onclick="refreshChartData(); return false;">
                                            <i class="fas fa-sync-alt"></i> Refresh
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-info me-2" id="debug-chart" onclick="debugChart(); return false;">
                                            <i class="fas fa-bug"></i> Debug
                                        </button>
                                        <div class="btn-group">
                                            <button type="button" class="btn btn-sm btn-outline-secondary chart-type" data-type="bar" onclick="changeChartType('bar'); return false;">Bar</button>
                                            <button type="button" class="btn btn-sm btn-outline-secondary chart-type active" data-type="line" onclick="changeChartType('line'); return false;">Line</button>
                                            <button type="button" class="btn btn-sm btn-outline-secondary chart-type" data-type="area" onclick="changeChartType('area'); return false;">Area</button>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <div class="chart-container" style="position: relative; height: 300px;">
                                        <canvas id="bookingTrendsChart"></canvas>
                                    </div>
                                    <!-- Direct script for button functionality -->
                                    <script>
                                        // Ensure buttons work directly
                                        document.addEventListener('DOMContentLoaded', function() {
                                            // Add direct click handlers after a delay
                                            setTimeout(function() {
                                                // Refresh button
                                                document.getElementById('refresh-charts').onclick = function(e) {
                                                    e.preventDefault();
                                                    if (typeof refreshChartData === 'function') {
                                                        refreshChartData();
                                                    } else {
                                                        console.error('refreshChartData function not found');
                                                        alert('Chart refresh function not available');
                                                    }
                                                    return false;
                                                };

                                                // Debug button
                                                document.getElementById('debug-chart').onclick = function(e) {
                                                    e.preventDefault();
                                                    if (typeof debugChart === 'function') {
                                                        debugChart();
                                                    } else {
                                                        console.error('debugChart function not found');
                                                        alert('Chart debug function not available');
                                                    }
                                                    return false;
                                                };

                                                // Chart type buttons
                                                document.querySelectorAll('.chart-type').forEach(function(button) {
                                                    button.onclick = function(e) {
                                                        e.preventDefault();
                                                        const type = this.getAttribute('data-type');
                                                        if (typeof changeChartType === 'function') {
                                                            changeChartType(type);
                                                        } else {
                                                            console.error('changeChartType function not found');
                                                            alert('Chart type change function not available');
                                                        }
                                                        return false;
                                                    };
                                                });

                                                console.log('Direct button handlers added');
                                            }, 2000);
                                        });
                                    </script>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5 class="mb-0">Booking Status Distribution</h5>
                                </div>
                                <div class="card-body">
                                    <canvas id="bookingStatusChart" height="200"></canvas>
                                </div>
                            </div>
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">Top Room Categories</h5>
                                </div>
                                <div class="card-body">
                                    <canvas id="topRoomCategoriesChart" height="200"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Export Buttons -->
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <div class="btn-group">
                                <!-- Only Print Button -->
                                <button type="button" class="btn btn-secondary" id="print-report">
                                    <i class="fas fa-print"></i> Print Report
                                </button>
                            </div>

                            <!-- Print button functionality -->
                            <script>
                                document.addEventListener('DOMContentLoaded', function() {
                                    // Set up print button
                                    const printBtn = document.getElementById('print-report');
                                    if (printBtn) {
                                        printBtn.addEventListener('click', function() {
                                            window.print();
                                        });
                                    }
                                });


                            </script>
                        </div>
                    </div>

                    <!-- Recent Bookings Table -->
                    <div class="row">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">Recent Bookings</h5>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-striped table-hover">
                                            <thead>
                                                <tr>
                                                    <th>ID</th>
                                                    <th>Customer</th>
                                                    <th>Room</th>
                                                    <th>Check-in Date</th>
                                                    <th>Check-out Date</th>
                                                    <th>Amount</th>
                                                    <th>Status</th>
                                                    <th>Created At</th>
                                                    <th>Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @forelse($recentBookings as $booking)
                                                    <tr>
                                                        <td>{{ $booking->id }}</td>
                                                        <td>
                                                            @if ($booking->address)
                                                                {{ $booking->address->first_name }} {{ $booking->address->last_name }}
                                                            @elseif ($booking->customer)
                                                                {{ $booking->customer->first_name }} {{ $booking->customer->last_name }}
                                                            @else
                                                                N/A
                                                            @endif
                                                        </td>
                                                        <td>
                                                            @if ($booking->room && $booking->room->room)
                                                                {{ $booking->room->room->name }}
                                                            @else
                                                                N/A
                                                            @endif
                                                        </td>
                                                        <td>{{ $booking->room && $booking->room->start_date ? Carbon\Carbon::parse($booking->room->start_date)->format('Y-m-d') : 'N/A' }}</td>
                                                        <td>{{ $booking->room && $booking->room->end_date ? Carbon\Carbon::parse($booking->room->end_date)->format('Y-m-d') : 'N/A' }}</td>
                                                        <td>{{ format_price($booking->getTotalAmount()) }}</td>
                                                        <td>{!! $booking->status->toHtml() !!}</td>
                                                        <td>{{ $booking->created_at->format('Y-m-d H:i:s') }}</td>
                                                        <td>
                                                            <a href="{{ route('booking.edit', $booking->id) }}" class="btn btn-sm btn-icon btn-primary" data-bs-toggle="tooltip" data-bs-original-title="View Details">
                                                                <i class="fa fa-eye"></i>
                                                            </a>
                                                        </td>
                                                    </tr>
                                                @empty
                                                    <tr>
                                                        <td colspan="9" class="text-center">No bookings found</td>
                                                    </tr>
                                                @endforelse
                                            </tbody>
                                        </table>
                                    </div>

                                    <!-- Pagination -->
                                    @if ($recentBookings->hasPages())
                                        <div class="mt-3 justify-content-center pagination-wrap">
                                            {!! $recentBookings->appends(request()->query())->links() !!}
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('header')
    <!-- Include Chart.js and plugins from CDN -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-datalabels"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css">
    <script>
        // Define route for AJAX requests
        var bookingAnalyticsRoute = '{{ route('booking.analytics.chart-data') }}';
    </script>
    <style>
        /* Loading indicator for charts */
        .chart-container {
            position: relative;
        }

        .chart-loading {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(255, 255, 255, 0.7);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10;
        }

        .chart-loading-spinner {
            border: 5px solid #f3f3f3;
            border-top: 5px solid #3498db;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 2s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        /* Chart container */
        .chart-container {
            position: relative;
            height: 300px;
            width: 100%;
        }

        /* Chart type buttons */
        .chart-type {
            cursor: pointer;
        }

        .chart-type.active {
            background-color: #e9ecef;
            color: #495057;
        }

        /* Equal height cards */
        .card.h-100 {
            min-height: 180px;
        }

        .card-body {
            padding: 1.25rem;
        }

        /* Apply filters button */
        .btn-primary {
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        /* Metric cards */
        .card h2 {
            font-size: 2rem;
            font-weight: 600;
        }

        .card .fa-3x {
            font-size: 2.5rem;
        }

        /* Badge styling */
        .badge {
            padding: 0.4rem 0.6rem;
            font-weight: 500;
        }

        /* Print styles */
        @media print {
            .no-print {
                display: none !important;
            }

            .print-only {
                display: block !important;
            }

            .card {
                border: 1px solid #ddd !important;
                break-inside: avoid;
            }

            .widget-body {
                padding: 0 !important;
            }
        }
    </style>
@endpush

@push('footer')
<script src="{{ asset('vendor/core/plugins/hotel/js/chart-buttons.js') }}"></script>
<script>
    // Global chart references
    window.bookingTrendsChart = null;
    window.bookingStatusChart = null;
    window.topRoomCategoriesChart = null;
    window.roomAvailabilityChart = null;

    document.addEventListener('DOMContentLoaded', function() {

        // Initialize date range picker
        $('.date-range-picker').daterangepicker({
            locale: {
                format: 'YYYY-MM-DD',
                applyLabel: 'Apply',
                cancelLabel: 'Cancel',
            },
            ranges: {
               'Today': [moment(), moment()],
               'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
               'Last 7 Days': [moment().subtract(6, 'days'), moment()],
               'Last 30 Days': [moment().subtract(29, 'days'), moment()],
               'This Month': [moment().startOf('month'), moment().endOf('month')],
               'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
            }
        }, function(start, end) {
            $('input[name="start_date"]').val(start.format('YYYY-MM-DD'));
            $('input[name="end_date"]').val(end.format('YYYY-MM-DD'));

            // Refresh data when date range changes
            refreshData();
        });

        // Register Chart.js plugins
        Chart.register(ChartDataLabels);

        // Function to refresh all data via AJAX - make it globally accessible
        window.refreshData = function() {
            console.log('Refreshing chart data');

            // Show loading indicators
            $('.card').addClass('loading');

            // Add loading spinners to charts
            $('.chart-container').each(function() {
                if (!$(this).find('.chart-loading').length) {
                    $(this).append('<div class="chart-loading"><div class="chart-loading-spinner"></div></div>');
                } else {
                    $(this).find('.chart-loading').show();
                }
            });

            // Get filter values
            const startDate = $('input[name="start_date"]').val();
            const endDate = $('input[name="end_date"]').val();
            const roomCategoryId = $('#room-category').val();
            const groupBy = $('#group-by').val();

            // Make AJAX request
            $.ajax({
                url: '{{ route('booking.analytics.chart-data') }}',
                type: 'GET',
                dataType: 'json',
                data: {
                    start_date: startDate,
                    end_date: endDate,
                    room_category_id: roomCategoryId,
                    group_by: groupBy
                },
                success: function(response) {
                    if (response.error) {
                        Botble.showError(response.message);
                    } else {
                        // Update charts and metrics
                        updateCharts(response.data);
                        updateMetrics(response.data.metrics);
                    }
                },
                error: function(error) {
                    Botble.handleError(error);
                },
                complete: function() {
                    // Hide loading indicators
                    $('.card').removeClass('loading');
                    $('.chart-loading').hide();

                    // Log completion
                    console.log('Data refresh completed');
                }
            });
        }

        // Function to update all charts
        function updateCharts(data) {
            // Update booking trends chart
            updateBookingTrendsChart(data.chartData);

            // Update booking status chart
            updateBookingStatusChart(data.bookingStatusData);

            // Update top room categories chart
            updateTopRoomCategoriesChart(data.topRoomCategories);

            // Update room availability chart
            updateRoomAvailabilityChart(data.roomAvailability);
        }

        // Function to update metrics
        function updateMetrics(metrics) {
            if (!metrics) return;

            try {
                // Update total bookings
                $('#total-bookings').text((metrics.totalBookings || 0).toLocaleString());

                const bookingGrowth = metrics.bookingGrowth || 0;
                $('#booking-growth').text((bookingGrowth > 0 ? '+' : '') + bookingGrowth.toFixed(1) + '%');
                $('#booking-growth').removeClass('bg-success bg-danger bg-secondary')
                    .addClass(bookingGrowth > 0 ? 'bg-success' : (bookingGrowth < 0 ? 'bg-danger' : 'bg-secondary'));

                // Update total revenue
                $('#total-revenue').text('₹' + (metrics.totalRevenue || 0).toLocaleString());

                const revenueGrowth = metrics.revenueGrowth || 0;
                $('#revenue-growth').text((revenueGrowth > 0 ? '+' : '') + revenueGrowth.toFixed(1) + '%');
                $('#revenue-growth').removeClass('bg-success bg-danger bg-secondary')
                    .addClass(revenueGrowth > 0 ? 'bg-success' : (revenueGrowth < 0 ? 'bg-danger' : 'bg-secondary'));

                // Update average booking value
                $('#avg-booking-value').text('₹' + (metrics.avgBookingValue || 0).toLocaleString());
                $('#completion-rate').text((metrics.completionRate || 0).toFixed(1) + '%');

                // Update cancellation rate
                $('#cancellation-rate').text((metrics.cancellationRate || 0).toFixed(1) + '%');
                $('#cancelled-bookings').text((metrics.cancelledBookings || 0).toLocaleString());
            } catch (e) {
                console.error('Error updating metrics:', e);
            }
        }

        // Function to initialize all event handlers
        function initializeEventHandlers() {
            console.log('Initializing all event handlers');

            // Set up chart type buttons
            setupChartTypeButtons();

            // Set up refresh button
            setupRefreshButton();

            // Set up debug button
            setupDebugButton();
        }

        // Initialize charts with initial data
        initializeCharts();

        // Initialize chart buttons after charts are ready
        setTimeout(function() {
            // Initialize the chart buttons
            if (typeof initializeChartButtons === 'function') {
                initializeChartButtons();
            } else {
                console.error('initializeChartButtons function not found');
            }
        }, 1000);

        // Function to initialize all charts
        function initializeCharts() {
            // Booking Trends Chart
            const bookingTrendsCanvas = document.getElementById('bookingTrendsChart');

            if (!bookingTrendsCanvas) {
                console.error('Booking trends canvas element not found!');
                return;
            }

            const bookingTrendsCtx = bookingTrendsCanvas.getContext('2d');

            // Destroy existing chart if it exists
            if (bookingTrendsChart) {
                bookingTrendsChart.destroy();
            }

            // Log initial data for debugging
            console.log('Initial booking data:', @json($bookingData));

            // Create the chart
            window.bookingTrendsChart = new Chart(bookingTrendsCtx, {
                type: 'line',
                data: {
                    labels: @json($bookingData['labels'] ?? []),
                    datasets: [
                        {
                            label: 'Bookings',
                            data: @json($bookingData['bookings'] ?? []),
                            backgroundColor: 'rgba(54, 162, 235, 0.2)',
                            borderColor: 'rgba(54, 162, 235, 1)',
                            borderWidth: 2,
                            tension: 0.1,
                            yAxisID: 'y'
                        },
                        {
                            label: 'Revenue (₹)',
                            data: @json($bookingData['revenue'] ?? []),
                            backgroundColor: 'rgba(75, 192, 192, 0.2)',
                            borderColor: 'rgba(75, 192, 192, 1)',
                            borderWidth: 2,
                            tension: 0.1,
                            yAxisID: 'y1'
                        }
                    ]
                },
                options: {
                    responsive: true,
                    interaction: {
                        mode: 'index',
                        intersect: false,
                    },
                    plugins: {
                        datalabels: {
                            display: false
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    let label = context.dataset.label || '';
                                    if (label) {
                                        label += ': ';
                                    }
                                    if (context.datasetIndex === 1) {
                                        label += '₹' + context.parsed.y.toLocaleString();
                                    } else {
                                        label += context.parsed.y;
                                    }
                                    return label;
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'Number of Bookings'
                            },
                            ticks: {
                                precision: 0
                            }
                        },
                        y1: {
                            beginAtZero: true,
                            position: 'right',
                            title: {
                                display: true,
                                text: 'Revenue (₹)'
                            },
                            grid: {
                                drawOnChartArea: false,
                            }
                        }
                    }
                }
            });

            // Booking Status Chart
            const bookingStatusCtx = document.getElementById('bookingStatusChart').getContext('2d');
            bookingStatusChart = new Chart(bookingStatusCtx, {
                type: 'doughnut',
                data: {
                    labels: @json($bookingStatusData['labels'] ?? []),
                    datasets: [{
                        data: @json($bookingStatusData['counts'] ?? []),
                        backgroundColor: @json($bookingStatusData['colors'] ?? []),
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        datalabels: {
                            color: '#fff',
                            font: {
                                weight: 'bold'
                            },
                            formatter: function(value, context) {
                                return value > 0 ? value : '';
                            }
                        },
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });

            // Top Room Categories Chart
            const topRoomCategoriesCtx = document.getElementById('topRoomCategoriesChart').getContext('2d');
            topRoomCategoriesChart = new Chart(topRoomCategoriesCtx, {
                type: 'bar',
                data: {
                    labels: @json($topRoomCategories['labels'] ?? []),
                    datasets: [{
                        label: 'Bookings',
                        data: @json($topRoomCategories['counts'] ?? []),
                        backgroundColor: 'rgba(255, 159, 64, 0.7)',
                        borderColor: 'rgba(255, 159, 64, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    indexAxis: 'y',
                    responsive: true,
                    plugins: {
                        datalabels: {
                            anchor: 'end',
                            align: 'end',
                            formatter: function(value) {
                                return value;
                            }
                        },
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        x: {
                            beginAtZero: true,
                            ticks: {
                                precision: 0
                            }
                        }
                    }
                }
            });

            // Room Availability Chart
            const roomAvailabilityCtx = document.getElementById('roomAvailabilityChart').getContext('2d');
            roomAvailabilityChart = new Chart(roomAvailabilityCtx, {
                type: 'pie',
                data: {
                    labels: ['Available', 'Booked'],
                    datasets: [{
                        data: [
                            {{ $roomAvailability['available'] ?? 0 }},
                            {{ $roomAvailability['booked'] ?? 0 }}
                        ],
                        backgroundColor: ['#10b981', '#f59e0b'],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        datalabels: {
                            color: '#fff',
                            font: {
                                weight: 'bold'
                            },
                            formatter: function(value, context) {
                                return value > 0 ? value : '';
                            }
                        },
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }

        // Function to update booking trends chart
        function updateBookingTrendsChart(data) {
            if (!data || !bookingTrendsChart) return;

            try {
                console.log('Booking trends data:', data);

                // Make sure we have valid data
                const labels = data.labels || [];
                const bookings = data.bookings || [];
                const revenue = data.revenue || [];

                // Update chart data
                bookingTrendsChart.data.labels = labels;
                bookingTrendsChart.data.datasets[0].data = bookings;
                bookingTrendsChart.data.datasets[1].data = revenue;

                // Reset chart type if needed
                const activeChartType = document.querySelector('.chart-type.active');
                if (activeChartType) {
                    const chartType = activeChartType.getAttribute('data-type');
                    bookingTrendsChart.config.type = chartType === 'area' ? 'line' : chartType;

                    // Update fill options for area chart
                    bookingTrendsChart.data.datasets.forEach((dataset) => {
                        dataset.fill = chartType === 'area';
                    });
                }

                // Force chart update with animation
                bookingTrendsChart.update();

                // Log success
                console.log('Booking trends chart updated successfully');
            } catch (e) {
                console.error('Error updating booking trends chart:', e);
                console.error(e.stack);
            }
        }

        // Function to update booking status chart
        function updateBookingStatusChart(data) {
            if (!data || !bookingStatusChart) return;

            try {
                console.log('Booking status data:', data);

                // Make sure we have valid data
                const labels = data.labels || [];
                const counts = data.counts || [];
                const colors = data.colors || [];

                // Update chart data
                bookingStatusChart.data.labels = labels;
                bookingStatusChart.data.datasets[0].data = counts;
                bookingStatusChart.data.datasets[0].backgroundColor = colors;

                // Force chart update
                bookingStatusChart.update('none');
            } catch (e) {
                console.error('Error updating booking status chart:', e);
                console.error(e.stack);
            }
        }

        // Function to update top room categories chart
        function updateTopRoomCategoriesChart(data) {
            if (!data || !topRoomCategoriesChart) return;

            try {
                console.log('Top room categories data:', data);

                // Make sure we have valid data
                const labels = data.labels || [];
                const counts = data.counts || [];

                // Update chart data
                topRoomCategoriesChart.data.labels = labels;
                topRoomCategoriesChart.data.datasets[0].data = counts;

                // Force chart update
                topRoomCategoriesChart.update('none');
            } catch (e) {
                console.error('Error updating top room categories chart:', e);
                console.error(e.stack);
            }
        }

        // Function to update room availability chart
        function updateRoomAvailabilityChart(data) {
            if (!data || !roomAvailabilityChart) return;

            try {
                console.log('Room availability data:', data);

                // Make sure we have valid data
                const available = data.available || 0;
                const booked = data.booked || 0;

                // Update chart data
                roomAvailabilityChart.data.datasets[0].data = [available, booked];

                // Force chart update
                roomAvailabilityChart.update('none');
            } catch (e) {
                console.error('Error updating room availability chart:', e);
                console.error(e.stack);
            }
        }

        // Handle chart type switching
        function setupChartTypeButtons() {
            console.log('Setting up chart type buttons');

            $('.chart-type').off('click').on('click', function() {
                console.log('Chart type button clicked:', $(this).data('type'));

                // Update active state
                $('.chart-type').removeClass('active');
                $(this).addClass('active');

                // Get chart type
                const chartType = $(this).data('type');

                if (!bookingTrendsChart) {
                    console.error('Chart not initialized yet');
                    return;
                }

                try {
                    // Update chart type
                    if (chartType === 'area') {
                        bookingTrendsChart.config.type = 'line';

                        // Update fill options for area chart
                        bookingTrendsChart.data.datasets.forEach((dataset) => {
                            dataset.fill = true;
                        });
                    } else {
                        bookingTrendsChart.config.type = chartType;

                        // Update fill options
                        bookingTrendsChart.data.datasets.forEach((dataset) => {
                            dataset.fill = false;
                        });
                    }

                    // Update chart
                    bookingTrendsChart.update();

                    console.log('Chart updated to type:', chartType);
                } catch (e) {
                    console.error('Error updating chart type:', e);
                }
            });
        }

        // Initialize chart type buttons
        setupChartTypeButtons();

        // Handle filter changes
        $('#room-category, #group-by').on('change', function() {
            refreshData();
        });

        // Handle refresh button click
        function setupRefreshButton() {
            console.log('Setting up refresh button');

            $('#refresh-charts').off('click').on('click', function() {
                console.log('Refresh button clicked');

                // Add spinner animation
                $(this).find('i').addClass('fa-spin');

                // Refresh data
                refreshData();

                // Remove spinner after a delay
                setTimeout(() => {
                    $(this).find('i').removeClass('fa-spin');
                }, 1000);
            });
        }

        // Initialize refresh button
        setupRefreshButton();

        // Handle debug button click
        function setupDebugButton() {
            console.log('Setting up debug button');

            $('#debug-chart').off('click').on('click', function() {
                console.log('Debug button clicked');

                // Add spinner animation
                $(this).find('i').addClass('fa-spin');

                // Create sample data for testing
                const sampleData = {
                    labels: ['Jan 1', 'Jan 2', 'Jan 3', 'Jan 4', 'Jan 5'],
                    bookings: [5, 10, 7, 12, 8],
                    revenue: [5000, 10000, 7000, 12000, 8000]
                };

                // Log the current chart state
                console.group('Chart Debug');
                console.log('Chart object:', bookingTrendsChart);
                console.log('Current data:', bookingTrendsChart ? bookingTrendsChart.data : 'Chart not initialized');
                console.log('Sample data:', sampleData);
                console.groupEnd();

                if (!bookingTrendsChart) {
                    console.error('Chart not initialized yet');
                    alert('Chart not initialized yet. Check console for details.');
                    $(this).find('i').removeClass('fa-spin');
                    return;
                }

                // Try updating with sample data
                try {
                    bookingTrendsChart.data.labels = sampleData.labels;
                    bookingTrendsChart.data.datasets[0].data = sampleData.bookings;
                    bookingTrendsChart.data.datasets[1].data = sampleData.revenue;
                    bookingTrendsChart.update();

                    console.log('Debug data applied successfully');
                    alert('Debug data applied to chart. Check console for details.');
                } catch (e) {
                    console.error('Error applying debug data:', e);
                    alert('Error applying debug data. Check console for details.');
                }

                // Remove spinner after a delay
                setTimeout(() => {
                    $(this).find('i').removeClass('fa-spin');
                }, 1000);
            });
        }

        // Initialize debug button
        setupDebugButton();

        // Handle print report
        document.getElementById('print-report').addEventListener('click', function() {
            window.print();
        });

        // Set up auto-refresh every 5 minutes
        setInterval(function() {
            refreshData();
        }, 5 * 60 * 1000); // 5 minutes in milliseconds

        // Function to manually update chart type
        window.updateChartType = function(type) {
            if (!bookingTrendsChart) {
                console.error('Chart not initialized yet');
                return 'Chart not initialized yet';
            }

            try {
                console.log('Manually updating chart type to:', type);

                // Update active button state
                $('.chart-type').removeClass('active');
                $(`.chart-type[data-type="${type}"]`).addClass('active');

                // Update chart type
                if (type === 'area') {
                    bookingTrendsChart.config.type = 'line';

                    // Update fill options for area chart
                    bookingTrendsChart.data.datasets.forEach((dataset) => {
                        dataset.fill = true;
                    });
                } else {
                    bookingTrendsChart.config.type = type;

                    // Update fill options
                    bookingTrendsChart.data.datasets.forEach((dataset) => {
                        dataset.fill = false;
                    });
                }

                // Update chart
                bookingTrendsChart.update();

                return `Chart type updated to ${type}`;
            } catch (e) {
                console.error('Error updating chart type:', e);
                return `Error: ${e.message}`;
            }
        };

        // Debug function to help troubleshoot chart issues
        window.debugCharts = function() {
            console.group('Chart Debug Information');

            console.log('Booking Trends Chart:', bookingTrendsChart);
            console.log('Booking Status Chart:', bookingStatusChart);
            console.log('Top Room Categories Chart:', topRoomCategoriesChart);
            console.log('Room Availability Chart:', roomAvailabilityChart);

            // Initial data
            console.log('Initial Booking Data:', @json($bookingData));
            console.log('Initial Revenue Data:', @json($revenueData));
            console.log('Initial Booking Status Data:', @json($bookingStatusData));
            console.log('Initial Top Room Categories:', @json($topRoomCategories));

            console.groupEnd();

            // Force refresh data
            refreshData();

            // Reinitialize event handlers
            initializeEventHandlers();

            return 'Debug information logged to console. Data refresh triggered.';
        };
    });
</script>
@endpush

