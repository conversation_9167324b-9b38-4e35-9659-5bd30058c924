function array_diff(arr1) {
    //  discuss at: http://phpjs.org/functions/array_diff/
    // original by: <PERSON> (http://kevin.vanzonneveld.net)
    // improved by: <PERSON><PERSON>
    //  revised by: <PERSON> (http://brett-zamir.me)
    //   example 1: array_diff(['<PERSON>', 'van', '<PERSON><PERSON><PERSON>veld'], ['van', '<PERSON><PERSON><PERSON>vel<PERSON>']);
    //   returns 1: {0:'<PERSON>'}

    var retArr = {},
        argl = arguments.length,
        k1 = '',
        i = 1,
        k = '',
        arr = {}

    arr1keys: for (k1 in arr1) {
        for (i = 1; i < argl; i++) {
            arr = arguments[i]
            for (k in arr) {
                if (arr[k] === arr1[k1]) {
                    // If it reaches here, it was found in at least one array, so try next value
                    continue arr1keys
                }
            }
            retArr[k1] = arr1[k1]
        }
    }

    return retArr
}
