<?php

namespace Botble\Optimize\Http\Controllers\Settings;

use Botble\Base\Http\Responses\BaseHttpResponse;
use Bo<PERSON>ble\Optimize\Forms\Settings\OptimizeSettingForm;
use Bo<PERSON>ble\Optimize\Http\Requests\OptimizeSettingRequest;
use Bo<PERSON>ble\Setting\Http\Controllers\SettingController;

class OptimizeSettingController extends SettingController
{
    public function edit()
    {
        $this->pageTitle(trans('packages/optimize::optimize.settings.title'));

        return OptimizeSettingForm::create()->renderForm();
    }

    public function update(OptimizeSettingRequest $request): BaseHttpResponse
    {
        return $this->performUpdate($request->validated());
    }
}
