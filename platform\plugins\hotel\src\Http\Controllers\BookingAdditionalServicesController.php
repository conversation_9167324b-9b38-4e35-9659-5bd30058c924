<?php

namespace Botble\Hotel\Http\Controllers;

use Bo<PERSON>ble\Base\Http\Controllers\BaseController;
use Bo<PERSON>ble\Base\Http\Responses\BaseHttpResponse;
use Botble\Hotel\Models\Booking;
use Botble\Hotel\Models\Invoice;
use Botble\Hotel\Models\InvoiceItem;
use Botble\Hotel\Models\Service;
use Botble\Hotel\Http\Requests\AddBookingAdditionalServiceRequest;
use Illuminate\Http\Request;

class BookingAdditionalServicesController extends BaseController
{
    public function __construct()
    {
        $this
            ->breadcrumb()
            ->add(trans('plugins/hotel::booking.name'), route('booking.index'));
    }

    public function create(Booking $booking)
    {
        $this->pageTitle(trans('plugins/hotel::booking.add_additional_service'));

        $services = Service::query()->wherePublished()->get();

        return view('plugins/hotel::booking-additional-services.create', compact('booking', 'services'));
    }

    public function store(Booking $booking, AddBookingAdditionalServiceRequest $request, BaseHttpResponse $response)
    {
        $serviceId = $request->input('service_id');
        $customServiceName = $request->input('custom_service_name');
        $customServicePrice = $request->input('custom_service_price');
        $quantity = $request->input('quantity', 1);

        // Handle either a predefined service or a custom service
        if ($serviceId) {
            $service = Service::query()->findOrFail($serviceId);
            $serviceName = $service->name;
            $servicePrice = $service->price;

            // Check if service is already attached to the booking
            if (!$booking->services->contains($service->id)) {
                // Add the service to the booking's services relationship
                $booking->services()->attach($service->id);
            } else {
                // Service already exists, return with message
                return $response
                    ->setPreviousUrl(route('booking.edit', $booking->id))
                    ->setMessage(trans('plugins/hotel::booking.additional_service_already_added'));
            }

            // Also check if this service already exists in the invoice to prevent duplicates
            $invoice = $booking->invoice;
            if ($invoice) {
                $existingItem = InvoiceItem::where('invoice_id', $invoice->id)
                    ->where('name', $serviceName . ' (additional service)')
                    ->first();

                if ($existingItem) {
                    // If the service already exists in the invoice, detach it from the booking and return with a message
                    $booking->services()->detach($service->id);
                    return $response
                        ->setPreviousUrl(route('booking.edit', $booking->id))
                        ->setMessage(trans('plugins/hotel::booking.additional_service_already_added'));
                }
            }
        } else {
            // For custom service (like extra bed, pillow, etc.)
            $serviceName = $customServiceName;
            $servicePrice = $customServicePrice;

            // Check if a custom service with this name already exists in the invoice
            $invoice = $booking->invoice;
            if ($invoice) {
                $existingItem = InvoiceItem::where('invoice_id', $invoice->id)
                    ->where('name', $serviceName . ' (additional service)')
                    ->first();

                if ($existingItem) {
                    // If the custom service already exists, return with a message
                    return $response
                        ->setPreviousUrl(route('booking.edit', $booking->id))
                        ->setMessage(trans('plugins/hotel::booking.additional_service_already_added'));
                }
            }
        }

        // Calculate the total price for this service
        $totalPrice = $servicePrice * $quantity;

        // Update the booking amount
        $booking->amount += $totalPrice;
        $booking->sub_total += $totalPrice;
        $booking->save();

        // Add the service to the invoice
        $invoice = $booking->invoice;
        if ($invoice) {
            // Create new invoice item
            InvoiceItem::create([
                'invoice_id' => $invoice->id,
                'name' => $serviceName . ' (additional service)',
                'description' => trans('plugins/hotel::booking.additional_service_added_during_stay'),
                'qty' => $quantity,
                'sub_total' => $servicePrice,
                'amount' => $totalPrice,
                'tax_amount' => 0,
                'discount_amount' => 0,
            ]);

            // Update invoice amounts
            $invoice->amount += $totalPrice;
            $invoice->sub_total += $totalPrice;
            $invoice->save();
        }

        return $response
            ->setPreviousUrl(route('booking.edit', $booking->id))
            ->setMessage(trans('plugins/hotel::booking.additional_service_added_success'));
    }

    /**
     * Delete a service from an invoice
     */
    public function delete(Booking $booking, $invoiceItemId, BaseHttpResponse $response)
    {
        try {
            // Find the invoice item
            $invoiceItem = InvoiceItem::findOrFail($invoiceItemId);

            // Make sure the invoice item belongs to this booking's invoice
            if ($invoiceItem->invoice_id != $booking->invoice->id) {
                return $response
                    ->setError()
                    ->setMessage('This service does not belong to the current booking.');
            }

            // Get the service amount to subtract from the invoice and booking
            $serviceAmount = $invoiceItem->amount;

            // Update the invoice amounts
            $invoice = $booking->invoice;
            $invoice->amount -= $serviceAmount;
            $invoice->sub_total -= $serviceAmount;
            $invoice->save();

            // Update the booking amounts
            $booking->amount -= $serviceAmount;
            $booking->sub_total -= $serviceAmount;
            $booking->save();

            // If this is a predefined service (not custom), detach it from the booking
            // We need to find the service by name (removing the " (additional service)" suffix)
            $serviceName = str_replace(' (additional service)', '', $invoiceItem->name);
            $service = Service::where('name', $serviceName)->first();

            if ($service) {
                $booking->services()->detach($service->id);
            }

            // Delete the invoice item
            $invoiceItem->delete();

            return $response
                ->setPreviousUrl(route('booking.edit', $booking->id))
                ->setMessage('Service removed successfully from the invoice.');

        } catch (\Exception $e) {
            return $response
                ->setError()
                ->setMessage('Error removing service: ' . $e->getMessage());
        }
    }
}
