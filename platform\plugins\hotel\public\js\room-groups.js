/**
 * Room Groups JavaScript
 * Handles the 2BHK package room availability logic
 */
$(document).ready(function() {
    // Set default value for room_id_code select
    var $roomIdCodeSelect = $('select[name="room_id_code"]');

    // If no value is selected, set the placeholder
    if (!$roomIdCodeSelect.val()) {
        $roomIdCodeSelect.val('');
    }

    // Handle room ID code selection
    $roomIdCodeSelect.on('change', function() {
        const roomIdCode = $(this).val();

        if (!roomIdCode) {
            return;
        }

        // Clear any previous warnings
        $('.room-group-warning').remove();

        // Special handling for 2BHK package
        if (roomIdCode === '2BHK') {
            // Show info message about booking the entire package
            $(this).after(
                '<div class="room-group-warning alert alert-info mt-2">' +
                '<i class="fas fa-info-circle"></i> ' +
                'You are booking the entire 2BHK package (MRR-777 and MRR-999).' +
                '</div>'
            );
            return; // Skip the AJAX check for 2BHK package
        }

        // Special handling for 2BHK rooms
        if (roomIdCode === 'MRR-777' || roomIdCode === 'MRR-999') {
            // Show warning about booking a room that's part of the 2BHK package
            $(this).after(
                '<div class="room-group-warning alert alert-warning mt-2">' +
                '<i class="fas fa-exclamation-triangle"></i> ' +
                'This room is part of the 2BHK package. Booking this room will make the entire 2BHK package unavailable.' +
                '</div>'
            );
        }

        // Check if the room is available
        const path = window.location.pathname;
        const parts = path.split('/');
        const bookingId = parts.includes('edit') ? parts[parts.length - 1] : null;

        $.ajax({
            url: route('booking.check-room-availability'),
            type: 'POST',
            data: {
                room_id_code: roomIdCode,
                booking_id: bookingId,
                _token: $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                if (!response.data.available) {
                    // Show error message
                    Botble.showError(response.message || 'This room is not available.');

                    // Reset the select field
                    $('select[name="room_id_code"]').val('');
                } else if (response.data.isGroupRoom) {
                    // Show warning about booking a room that's part of a group
                    Botble.showWarning('Note: This room is part of the ' + response.data.groupCode + ' package. Booking this room will make the entire package unavailable for online booking.');
                }
            },
            error: function() {
                // If there's an error with the AJAX call, just continue without showing an error
                console.log('Error checking room availability, but continuing with booking');

                // Don't reset the select field, allow the booking to proceed
                // If there's a server-side validation error, it will be caught later
            }
        });
    });

    // Remove the automatic trigger on page load to prevent unwanted error messages
    // $('select[name="room_id_code"]').trigger('change');
});
