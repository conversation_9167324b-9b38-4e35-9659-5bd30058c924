<?php

namespace Botble\Hotel\Services;

use Botble\Hotel\Models\Room;
use Botble\Hotel\Models\RoomCategory;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;

class RoomCapacityService
{
    /**
     * Room type configurations defining maximum occupancy and allowed combinations.
     * These are the business rules for which room types can accommodate which guest compositions.
     */
    const ROOM_CONFIGURATIONS = [
        'standard' => [
            'max_total' => 3,
            'allowed_combinations' => [
                ['adults' => 1, 'children' => 0],
                ['adults' => 1, 'children' => 1],
                ['adults' => 1, 'children' => 2],
                ['adults' => 2, 'children' => 0],
                ['adults' => 2, 'children' => 1],
                ['adults' => 3, 'children' => 0],
            ]
        ],
        'double' => [
            'max_total' => 4,
            'allowed_combinations' => [
                ['adults' => 1, 'children' => 0],
                ['adults' => 1, 'children' => 1],
                ['adults' => 1, 'children' => 2],
                ['adults' => 1, 'children' => 3],
                ['adults' => 2, 'children' => 0],
                ['adults' => 2, 'children' => 1],
                ['adults' => 2, 'children' => 2],
                ['adults' => 3, 'children' => 0],
                ['adults' => 3, 'children' => 1],
                ['adults' => 4, 'children' => 0],
            ]
        ],
        'luxury' => [
            'max_total' => 2,
            'allowed_combinations' => [
                ['adults' => 1, 'children' => 0],
                ['adults' => 2, 'children' => 0], // Only couples allowed
            ]
        ],
        'family' => [
            'max_total' => 6,
            'allowed_combinations' => [
                ['adults' => 1, 'children' => 0],
                ['adults' => 1, 'children' => 1],
                ['adults' => 1, 'children' => 2],
                ['adults' => 1, 'children' => 3],
                ['adults' => 1, 'children' => 4],
                ['adults' => 1, 'children' => 5],
                ['adults' => 2, 'children' => 0],
                ['adults' => 2, 'children' => 1],
                ['adults' => 2, 'children' => 2],
                ['adults' => 2, 'children' => 3],
                ['adults' => 2, 'children' => 4],
                ['adults' => 3, 'children' => 0],
                ['adults' => 3, 'children' => 1],
                ['adults' => 3, 'children' => 2],
                ['adults' => 3, 'children' => 3],
                ['adults' => 4, 'children' => 0],
                ['adults' => 4, 'children' => 1],
                ['adults' => 4, 'children' => 2],
                ['adults' => 5, 'children' => 0],
                ['adults' => 5, 'children' => 1],
                ['adults' => 6, 'children' => 0],
            ]
        ],
    ];

    /**
     * Find the room configuration by matching the category name case-insensitively
     * against the keys in ROOM_CONFIGURATIONS.
     */
    protected function findRoomConfig(string $categoryName): ?array
    {
        // Normalize the category name for comparison (lowercase, trim spaces)
        $normalizedCategoryName = strtolower(trim($categoryName));
        
        // Log the actual category name we're trying to match
        Log::debug('findRoomConfig: Looking for configuration match for category', [
            'category' => $categoryName,
            'normalized' => $normalizedCategoryName
        ]);
        
        // Map common room category names to our configuration keys
        $categoryMappings = [
            'standard' => ['standard', 'standard room', 'std'],
            'double' => ['double', 'double bed', 'double room', 'double bed room'],
            'luxury' => ['luxury', 'luxury room', 'luxury suite'],
            'family' => ['family', 'family room', '2bhk family room', 'family suite']
        ];
        
        // Check if the normalized category name matches any of our mappings
        foreach ($categoryMappings as $configKey => $possibleNames) {
            foreach ($possibleNames as $possibleName) {
                if ($normalizedCategoryName === $possibleName || 
                    str_contains($normalizedCategoryName, $possibleName)) {
                    Log::debug('findRoomConfig: Match found via mapping', [
                        'category' => $categoryName,
                        'matched_to' => $possibleName,
                        'config_key' => $configKey
                    ]);
                    return self::ROOM_CONFIGURATIONS[$configKey];
                }
            }
        }
        
        // Direct check against configuration keys as a fallback
        if (isset(self::ROOM_CONFIGURATIONS[$normalizedCategoryName])) {
            Log::debug('findRoomConfig: Direct match found', [
                'category' => $categoryName,
                'config_key' => $normalizedCategoryName
            ]);
            return self::ROOM_CONFIGURATIONS[$normalizedCategoryName];
        }
        
        // No match found
        Log::debug('findRoomConfig: No match found for category', [
            'category' => $categoryName
        ]);
        return null;
    }

    /**
     * Get available room types based on the number of adults and children
     * Returns a collection of Room models where the guest composition fits allowed rules.
     */
    public function getAvailableRooms(int $adults, int $children): Collection
    {
        try {
            $totalOccupants = $adults + $children;

            Log::info('getAvailableRooms: Searching for available rooms', [
                'adults' => $adults,
                'children' => $children,
                'total' => $totalOccupants
            ]);

            // Basic validation: A booking must have at least one adult
            if ($adults < 1) {
                Log::info('getAvailableRooms: No adults specified, returning empty collection');
                return collect();
            }

            // Get all rooms and filter them based on occupancy rules
            return Room::with('category')->get()->filter(function ($room) use ($adults, $children) {
                return $this->roomMatchesOccupancyRules($room, $adults, $children);
            });
        } catch (\Exception $e) {
            Log::error('Error in getAvailableRooms: ' . $e->getMessage(), [
                'exception' => $e
            ]);
            return collect(); // Return empty collection on error
        }
    }

    /**
     * Get room types (from CONFIGURATIONS keys) that match the given adults and children count.
     * This method does NOT query the database for available rooms, only available *types*.
     */
    public function getMatchingRoomTypes(int $adults, int $children): array
    {
        $matchingTypes = [];
        $totalOccupancy = $adults + $children;

        Log::info('getMatchingRoomTypes: Searching for matching room types based on occupancy', [
            'adults' => $adults,
            'children' => $children,
            'total' => $totalOccupancy
        ]);

         // Basic validation: A booking must have at least one adult.
        if ($adults < 1) {
             Log::warning('getMatchingRoomTypes: No adults specified, returning no matching types.');
             return [];
        }


        // Check all room types defined in CONFIGURATIONS
        foreach (self::ROOM_CONFIGURATIONS as $roomType => $config) {
            Log::debug('getMatchingRoomTypes: Checking room type config', [
                'roomType' => $roomType,
                'max_total_config' => $config['max_total']
            ]);

            // Rule 1: Check total occupancy limit from config
            if ($totalOccupancy > $config['max_total']) {
                Log::debug('getMatchingRoomTypes: Room type excluded: Total occupancy exceeds configured maximum', [
                    'roomType' => $roomType,
                    'total' => $totalOccupancy,
                    'max_total' => $config['max_total']
                ]);
                continue;
            }

             // Rule 2: Check if the specific adults/children combination is allowed by config
            $isCombinationAllowed = false;
            foreach ($config['allowed_combinations'] as $combination) {
                if ($combination['adults'] === $adults && $combination['children'] === $children) {
                    $isCombinationAllowed = true;
                    break;
                }
            }

            if ($isCombinationAllowed) {
                $matchingTypes[] = $roomType;
                 Log::debug('getMatchingRoomTypes: Added matching room type', ['roomType' => $roomType]);
            } else {
                 Log::debug('getMatchingRoomTypes: Room type excluded: Combination not explicitly allowed by configuration', [
                    'roomType' => $roomType,
                    'adults' => $adults,
                    'children' => $children
                ]);
            }
        }

        Log::info('getMatchingRoomTypes: Finished searching for matching room types', ['types' => $matchingTypes]);
        return $matchingTypes;
    }

     /**
     * Get category IDs from the database for room types specified in CATEGORY_TO_ROOM_TYPE_MAP.
     * This method is primarily for fetching initial data and may not perfectly match
     * the strict configuration keys used for validation.
     */
    protected function getCategoryIdsForRoomTypes(array $roomTypes): array
    {
        // We only need the values (category names) from the CATEGORY_TO_ROOM_TYPE_MAP
        // for the provided room types (which are the keys).
        // Note: This mapping relies on the CATEGORY_TO_ROOM_TYPE_MAP being correctly set up.
        $categoryNames = collect($roomTypes)
            ->map(function ($roomType) {
                 // Find the config key (case-insensitive) to get the canonical category name
                 foreach (self::ROOM_CONFIGURATIONS as $configKey => $configValue) {
                    if (strcasecmp($configKey, $roomType) === 0) {
                        return $configKey; // Use the exact key from config
                    }
                 }
                 return null; // Room type not found in configurations
            })
            ->filter() // Remove nulls
            ->unique()
            ->values()
            ->toArray();


        Log::info('getCategoryIdsForRoomTypes: Attempting to find category IDs for configured names', [
            'inputRoomTypes' => $roomTypes,
            'configuredCategoryNames' => $categoryNames
        ]);

        if (empty($categoryNames)) {
            return [];
        }

        // Fetch category IDs from the database using the configured names.
         $categoryIds = RoomCategory::query()
            ->whereIn('name', $categoryNames) // Use exact match with configured names
            ->pluck('id')
            ->toArray();

        Log::info('getCategoryIdsForRoomTypes: Found category IDs for configured names', ['categoryIds' => $categoryIds]);

        return $categoryIds;
    }

    /**
     * Check if the combination of adults and children is valid for a specific room type
     * based on the CONFIGURATIONS array.
     */
    public function isValidCombination(string $roomType, int $adults, int $children): bool
    {
        $totalOccupancy = $adults + $children;

        Log::info('isValidCombination: Checking combination validity for room type', [
            'roomType' => $roomType,
            'adults' => $adults,
            'children' => $children,
            'total' => $totalOccupancy
        ]);

         // Basic validation: A booking must have at least one adult.
        if ($adults < 1) {
            Log::debug('isValidCombination: REJECTED: No adults in combination', [
                'roomType' => $roomType,
                'adults' => $adults
            ]);
            return false;
        }

        // Find the configuration for the given room type (exact key match expected here as input is a config key)
        $config = self::ROOM_CONFIGURATIONS[$roomType] ?? null;

        // If room type is not in our configurations, it's not considered valid for this check
        // (This method is specifically for *configured* types)
        if (!$config) {
            Log::debug('isValidCombination: REJECTED: Room type not found in CONFIGURATIONS', [
                'roomType' => $roomType
            ]);
            return false;
        }

        Log::debug('isValidCombination: Found config for room type', [
            'roomType' => $roomType,
            'max_total_config' => $config['max_total']
        ]);

        // Rule 1: Check total occupancy limit from config
        if ($totalOccupancy > $config['max_total']) {
            Log::debug('isValidCombination: REJECTED: Total occupancy exceeds configured maximum', [
                'roomType' => $roomType,
                'total' => $totalOccupancy,
                'max_total' => $config['max_total']
            ]);
            return false;
        }

        // Rule 2: Check if this specific combination is allowed by config
        foreach ($config['allowed_combinations'] as $combination) {
            if ($combination['adults'] === $adults && $combination['children'] === $children) {
                Log::debug('isValidCombination: ACCEPTED: Valid combination found in config', [
                    'roomType' => $roomType,
                    'adults' => $adults,
                    'children' => $children
                ]);
                return true; // Combination is allowed
            }
        }

        // If no exact combination match found
        Log::debug('isValidCombination: REJECTED: Combination not explicitly allowed by configuration', [
            'roomType' => $roomType,
            'adults' => $adults,
            'children' => $children
        ]);
        return false; // Combination is not allowed
    }

    /**
     * Check if a specific Room model instance matches the occupancy requirements
     * for a given number of adults and children.
     */
    public function roomMatchesOccupancyRules(Room $room, int $adults, int $children): bool
    {
        // CRITICAL FIX: Always allow Luxury rooms for 1 adult
        $categoryName = $room->category ? strtolower($room->category->name) : '';
        if ($adults === 1 && stripos($categoryName, 'luxury') !== false) {
            \Illuminate\Support\Facades\Log::info('OVERRIDE: Luxury room explicitly allowed for 1 adult', [
                'room_id' => $room->id,
                'room_name' => $room->name,
                'category' => $categoryName
            ]);
            return true;
        }

        // Rest of the method remains unchanged
        $totalOccupants = $adults + $children;
        
        // Basic validation: A booking must have at least one adult
        if ($adults < 1) {
            return false;
        }

        // Use the flexible room configuration from Room model
        $roomConfigurations = [
            'standard' => [
                'max_total' => 3,
                'allowed_combinations' => [
                    ['adults' => 1, 'children' => 0],
                    ['adults' => 1, 'children' => 1],
                    ['adults' => 1, 'children' => 2],
                    ['adults' => 2, 'children' => 0],
                    ['adults' => 2, 'children' => 1],
                    ['adults' => 3, 'children' => 0],
                ]
            ],
            'double bed' => [
                'max_total' => 4,
                'allowed_combinations' => [
                    ['adults' => 3, 'children' => 1],
                    ['adults' => 3, 'children' => 0],
                    ['adults' => 2, 'children' => 2],
                    ['adults' => 2, 'children' => 1],
                    ['adults' => 2, 'children' => 0],
                    ['adults' => 1, 'children' => 3],
                    ['adults' => 1, 'children' => 2],
                    ['adults' => 1, 'children' => 1],
                    ['adults' => 1, 'children' => 0],
                ]
            ],
            'luxury' => [
                'max_total' => 3,
                'allowed_combinations' => [
                    ['adults' => 2, 'children' => 0], // Couples allowed
                    ['adults' => 1, 'children' => 1], // 1 adult, 1 child allowed
                    ['adults' => 1, 'children' => 0], // Single adult allowed
                ]
            ],
            '2bhk' => [
                'max_total' => 7,
                'allowed_combinations' => [
                    ['adults' => 7, 'children' => 0], // 7 adults
                    ['adults' => 6, 'children' => 1], // 6 adults, 1 child
                    ['adults' => 5, 'children' => 2], // 5 adults, 2 children
                    ['adults' => 4, 'children' => 3], // 4 adults, 3 children
                    ['adults' => 3, 'children' => 4], // 3 adults, 4 children
                    ['adults' => 2, 'children' => 5], // 2 adults, 5 children
                    ['adults' => 1, 'children' => 6], // 1 adult, 6 children
                    // Common family combinations
                    ['adults' => 4, 'children' => 0], // 4 adults
                    ['adults' => 3, 'children' => 0], // 3 adults
                    ['adults' => 2, 'children' => 0], // 2 adults
                    ['adults' => 2, 'children' => 2], // 2 adults, 2 children
                    ['adults' => 2, 'children' => 3], // 2 adults, 3 children
                    ['adults' => 3, 'children' => 2], // 3 adults, 2 children
                    ['adults' => 3, 'children' => 1], // 3 adults, 1 child
                    ['adults' => 1, 'children' => 0], // 1 adult
                    ['adults' => 1, 'children' => 1], // 1 adult, 1 child
                    ['adults' => 1, 'children' => 2], // 1 adult, 2 children
                    ['adults' => 1, 'children' => 3], // 1 adult, 3 children
                    ['adults' => 1, 'children' => 4], // 1 adult, 4 children
                    ['adults' => 1, 'children' => 5]  // 1 adult, 5 children
                ]
            ],
        ];

        // Determine room type from category name
        $roomType = null;
        if (stripos($categoryName, '2bhk') !== false || stripos($categoryName, 'family') !== false) {
            $roomType = '2bhk';
        } elseif (stripos($categoryName, 'luxury') !== false) {
            $roomType = 'luxury';
        } elseif (stripos($categoryName, 'double') !== false) {
            $roomType = 'double bed';
        } elseif (stripos($categoryName, 'standard') !== false) {
            $roomType = 'standard';
        }

        // If we have a specific configuration for this room type, use it
        if ($roomType && isset($roomConfigurations[$roomType])) {
            $config = $roomConfigurations[$roomType];

            // Check total occupancy limit
            if ($totalOccupants > $config['max_total']) {
                \Illuminate\Support\Facades\Log::debug('Room rejected: Total occupancy exceeds limit', [
                    'room_id' => $room->id,
                    'room_type' => $roomType,
                    'total_occupants' => $totalOccupants,
                    'max_total' => $config['max_total']
                ]);
                return false;
            }

            // Check if this specific combination is allowed
            foreach ($config['allowed_combinations'] as $combination) {
                if ($combination['adults'] === $adults && $combination['children'] === $children) {
                    \Illuminate\Support\Facades\Log::debug('Room accepted: Valid combination found', [
                        'room_id' => $room->id,
                        'room_type' => $roomType,
                        'adults' => $adults,
                        'children' => $children
                    ]);
                    return true;
                }
            }

            \Illuminate\Support\Facades\Log::debug('Room rejected: Combination not allowed', [
                'room_id' => $room->id,
                'room_type' => $roomType,
                'adults' => $adults,
                'children' => $children
            ]);
            return false;
        }

        // Fallback to basic capacity checks for unknown room types
        if ($adults > $room->max_adults || $children > $room->max_children) {
            return false;
        }

        // Check total occupancy
        $maxOccupants = $room->max_adults + $room->max_children;

        if ($totalOccupants > $maxOccupants) {
            return false;
        }
        
        return true;
    }

    /**
     * Get all category IDs from the database. Used to initially fetch all rooms.
     * (Method remains for potential use, not directly used in the main getAvailableRooms query now).
     */
    protected function getAllCategoryIds(): array
    {
        return RoomCategory::query()
            ->pluck('id')
            ->toArray();
    }
}




