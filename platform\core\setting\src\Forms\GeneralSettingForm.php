<?php

namespace Bo<PERSON><PERSON>\Setting\Forms;

use Bo<PERSON>ble\Base\Facades\Assets;
use Bo<PERSON>ble\Base\Forms\FieldOptions\SelectFieldOption;
use Bo<PERSON>ble\Base\Forms\Fields\HtmlField;
use Bo<PERSON>ble\Base\Forms\Fields\SelectField;
use Bo<PERSON><PERSON>\Setting\Http\Requests\GeneralSettingRequest;
use DateTimeZone;

class GeneralSettingForm extends SettingForm
{
    public function setup(): void
    {
        parent::setup();

        Assets::usingVueJS()
            ->addStylesDirectly('vendor/core/core/setting/css/admin-email.css')
            ->addScriptsDirectly([
                'vendor/core/core/setting/js/admin-email.js',
                'vendor/core/core/setting/js/license-component.js',
            ]);

        $this
            ->setSectionTitle(trans('core/setting::setting.general.title'))
            ->setSectionDescription(trans('core/setting::setting.general.description'))
            ->contentOnly()
            ->setValidatorClass(GeneralSettingRequest::class)
            ->add('admin_email', 'html', [
                'html' => view('core/setting::partials.admin-email-field')->render(),
            ])
            ->add(
                'time_zone',
                SelectField::class,
                SelectFieldOption::make()
                    ->label(trans('core/setting::setting.general.time_zone'))
                    ->choices(array_combine(DateTimeZone::listIdentifiers(), DateTimeZone::listIdentifiers()))
                    ->selected(setting('time_zone', 'UTC'))
                    ->searchable()
                    ->toArray()
            )
            ->add('enable_send_error_reporting_via_email', 'onOffCheckbox', [
                'label' => trans('core/setting::setting.general.enable_send_error_reporting_via_email'),
                'value' => setting('enable_send_error_reporting_via_email'),
            ])
            ->when(
                apply_filters(BASE_FILTER_AFTER_SETTING_CONTENT, null),
                function (GeneralSettingForm $form, $settingContent) {
                    $form
                        ->add('html', HtmlField::class, [
                            'html' => '</div></div><div class="card mt-3 overflow-hidden"><div class="card-body">' . $settingContent,
                        ]);
                }
            );
    }
}
