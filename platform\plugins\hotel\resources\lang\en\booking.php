<?php

return [
    'name' => 'Bookings',
    'create' => 'New booking',
    'statuses' => [
        'pending' => 'Pending',
        'processing' => 'Processing',
        'completed' => 'Completed',
        'cancelled' => 'Cancelled',
    ],
    'amount' => 'Amount',
    'customer' => 'Customer',
    'room' => 'Room',
    'booking_information' => 'Booking Information',
    'time' => 'Time',
    'full_name' => 'Full Name',
    'email' => 'Email',
    'phone' => 'Phone',
    'address' => 'Address',
    'arrival_time' => 'Arrival Time',
    'start_date' => 'Check-in Date',
    'end_date' => 'Check-out Date',
    'settings' => [
        'email' => [
            'title' => 'Booking',
            'description' => 'Booking email configuration',
            'templates' => [
                'notice_title' => 'Send notice to administrator',
                'notice_description' => 'Email template to send notice to administrator when system get new booking',
                'booking_success_title' => 'Send email to guest',
                'booking_success_description' => 'Email template to send email to guest to confirm booking',
                'booking_status_changed_title' => 'Send email to customer when booking status changed',
                'booking_status_changed_description' => 'Email template to send email to customer when booking status changed',
            ],
        ],
    ],
    'new_booking_notice' => 'You have <span class="bold">:count</span> new booking(s)',
    'view_all' => 'View all',
    'payment_method' => 'Payment method',
    'payment_status_label' => 'Payment status',
    'booking_period' => 'Booking period',
    'reports' => 'Booking Reports',
    'advanced_reports' => 'Advanced Booking Reports',
    'export_report' => 'Export Report',
    'calendar' => 'Booking Calendar',
    'calendar_item_title' => ':room (:number_of_rooms room(s), :number_of_guests adults(s), :number_of_children children(s))',
    'additional_services' => 'Additional Services',
    'add_additional_service' => 'Add Additional Service',
    'predefined_service' => 'Predefined Service',
    'custom_service' => 'Custom Service',
    'select_service' => 'Select Service',
    'custom_service_name' => 'Custom Service Name',
    'custom_service_name_placeholder' => 'e.g. Extra Bed, Extra Pillow',
    'custom_service_price' => 'Price',
    'custom_service_price_placeholder' => 'Enter price',
    'quantity' => 'Quantity',
    'add_service' => 'Add Service',
    'additional_service_added_success' => 'Additional service added successfully',
    'additional_service_added_during_stay' => 'Additional service added during stay',
    'booking_number' => 'Booking Number',
    'room_id_code' => 'Room ID Code',
    'booking_trend' => 'Booking Trend',
    'recent_bookings' => 'Recent Bookings',
    'room_availability' => 'Room Availability',
    'booking_status_distribution' => 'Booking Status Distribution',
    'revenue_by_room_type' => 'Revenue by Room Type',
    'basic_reports' => 'Basic Reports',
    'this_year' => 'This Year',
    'last_6_months' => 'Last 6 Months',
    'last_3_months' => 'Last 3 Months',
    'area_chart' => 'Area Chart',
    'bar_chart' => 'Bar Chart',
    'line_chart' => 'Line Chart',
    'view_details' => 'View Details',
    'refresh' => 'Refresh',
    'revenue_trend' => 'Revenue Trend',
    'booking_status' => 'Booking Status',
    'view_all_bookings' => 'View All Bookings',
    'date_range_format_value' => 'From :from To :to',

    // Offline Booking
    'create_offline_booking' => 'Create Offline Booking',
    'offline_booking_description' => 'Create a booking manually for walk-in customers or phone reservations',
    'customer_information' => 'Customer Information',
    'first_name' => 'First Name',
    'last_name' => 'Last Name',
    'city' => 'City',
    'state' => 'State',
    'country' => 'Country',
    'zip' => 'ZIP Code',
    'adults' => 'Adults',
    'children' => 'Children',
    'number_of_rooms' => 'Number of Rooms',
    'search_available_rooms' => 'Search Available Rooms',
    'available_rooms' => 'Available Rooms',
    'selected_room' => 'Selected Room',
    'additional_information' => 'Additional Information',
    'requests' => 'Special Requests',
    'special_requests_placeholder' => 'Any special requests or notes...',
    'additional_services' => 'Additional Services',
    'payment_information' => 'Payment Information',
    'select_payment_method' => 'Select Payment Method',
    'cash' => 'Cash',
    'card' => 'Credit/Debit Card',
    'bank_transfer' => 'Bank Transfer',
    'other' => 'Other',
    'payment_notes' => 'Payment Notes',
    'payment_notes_placeholder' => 'Payment reference, transaction ID, or other notes...',
    'create_booking' => 'Create Booking',
    'price_per_night' => 'Price per Night',
    'total_price' => 'Total Price',
    'max_occupancy' => 'Max Occupancy',
    'select_room' => 'Select Room',
    'selected' => 'Selected',
    'please_select_dates' => 'Please select check-in and check-out dates',
    'end_date_must_be_after_start_date' => 'Check-out date must be after check-in date',
    'searching' => 'Searching',
    'error_searching_rooms' => 'Error occurred while searching for rooms',
    'no_rooms_available' => 'No rooms available for the selected dates and criteria',
    'please_select_room' => 'Please select a room before creating the booking',
    'creating_booking' => 'Creating Booking',
    'offline_booking_created_successfully' => 'Offline booking created successfully! Booking Number: :booking_number',
];
