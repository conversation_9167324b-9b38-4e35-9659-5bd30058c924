/**
 * Get rooms for booking
 */
public function getRooms(Request $request)
{
    [$startDate, $endDate, $adults, $children] = HotelHelper::getRoomBookingParams();
    
    // Log search parameters
    \Illuminate\Support\Facades\Log::info('HotelController::getRooms', [
        'start_date' => $startDate,
        'end_date' => $endDate,
        'adults' => $adults,
        'children' => $children
    ]);
    
    $roomSearchService = app(RoomSearchService::class);
    $rooms = $roomSearchService->getAvailableRooms($adults, $children);
    
    // CRITICAL FIX: If searching for 1 adult, manually add Luxury rooms if not already included
    if ($adults === 1) {
        $luxuryRoomIds = $rooms->pluck('id')->toArray();
        
        $missingLuxuryRooms = Room::with(['category', 'currency'])
            ->where('status', 'published')
            ->whereHas('category', function ($query) {
                $query->where('name', 'like', '%luxury%');
            })
            ->whereNotIn('id', $luxuryRoomIds)
            ->get();
            
        if ($missingLuxuryRooms->count() > 0) {
            \Illuminate\Support\Facades\Log::info('Manually adding missing Luxury rooms for 1 adult', [
                'count' => $missingLuxuryRooms->count(),
                'room_ids' => $missingLuxuryRooms->pluck('id')->toArray()
            ]);
            
            foreach ($missingLuxuryRooms as $luxuryRoom) {
                $rooms->push($luxuryRoom);
            }
        }
    }
    
    return response()->json([
        'error' => false,
        'data' => RoomResource::collection($rooms),
    ]);
}