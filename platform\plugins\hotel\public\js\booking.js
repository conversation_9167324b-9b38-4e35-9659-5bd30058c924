/**
 * Booking JavaScript
 * Handles the booking form functionality
 */
$(document).ready(function() {
    // Set default value for room_id_code select
    var $roomIdCodeSelect = $('select[name="room_id_code"]');

    // If no value is selected, set the placeholder
    if (!$roomIdCodeSelect.val()) {
        $roomIdCodeSelect.val('');
    }

    // Add a class to style the placeholder
    $roomIdCodeSelect.find('option[value=""]').addClass('text-muted');

    // Handle form submission
    $('form').on('submit', function() {
        // Validate that a room ID is selected
        if (!$roomIdCodeSelect.val()) {
            // Show error message
            if (typeof Botble !== 'undefined' && Botble.showError) {
                Botble.showError('Please select a room ID.');
            } else {
                alert('Please select a room ID.');
            }

            // Prevent form submission
            return false;
        }

        // Skip validation for completed or cancelled bookings
        var status = $('input[name="status"]').val();
        if (status === 'completed' || status === 'cancelled') {
            return true;
        }

        return true;
    });

    // Handle errors gracefully
    window.onerror = function(message, source, lineno, colno, error) {
        console.log('Error caught: ' + message);
        // Return true to prevent the default browser error handling
        return true;
    };
});

