<?php

namespace Botble\Hotel\Http\Requests;

use Botble\Support\Http\Requests\Request;
use Illuminate\Validation\Rule;

class AddBookingAdditionalServiceRequest extends Request
{
    public function rules(): array
    {
        return [
            'service_id' => [
                'nullable',
                'required_without:custom_service_name',
                'exists:ht_services,id',
            ],
            'custom_service_name' => [
                'nullable',
                'required_without:service_id',
                'string',
                'max:120',
            ],
            'custom_service_price' => [
                'nullable',
                'required_with:custom_service_name',
                'numeric',
                'min:0',
            ],
            'quantity' => [
                'required',
                'integer',
                'min:1',
            ],
        ];
    }
}
