<?php

namespace Botble\Hotel\Commands;

use Botble\Hotel\Models\Room;
use Botble\Hotel\Models\RoomInventory;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class GenerateRoomInventoryCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'hotel:generate-room-inventory';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate room inventory entries for all rooms';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Generating room inventory entries...');

        // Get all rooms
        $rooms = Room::all();
        $count = 0;

        foreach ($rooms as $room) {
            // Generate room codes
            $roomCodes = $this->generateRoomCodes($room);
            
            foreach ($roomCodes as $code) {
                // Check if entry already exists
                $exists = RoomInventory::where('room_id', $room->id)
                    ->where('room_id_code', $code)
                    ->exists();
                    
                if (!$exists) {
                    // Create new entry
                    RoomInventory::create([
                        'room_id' => $room->id,
                        'room_id_code' => $code,
                        'is_available' => true,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);
                    
                    $count++;
                }
            }
        }

        $this->info("Generated $count new room inventory entries.");
        
        return 0;
    }
    
    /**
     * Generate room codes based on room name and ID
     */
    private function generateRoomCodes(Room $room): array
    {
        $codes = [];
        $roomName = $room->name;
        $roomId = $room->id;
        
        // Clean the room name to use as a prefix
        $prefix = preg_replace('/[^A-Za-z0-9]/', '', $roomName);
        $prefix = strtoupper(substr($prefix, 0, 3));
        
        // If we couldn't generate a prefix, use 'MRR'
        if (empty($prefix)) {
            $prefix = 'MRR';
        }
        
        // Generate codes based on room type
        $numberOfRooms = $room->number_of_rooms ?? 1;
        
        // Generate a unique code for each room
        for ($i = 1; $i <= $numberOfRooms; $i++) {
            $codes[] = $prefix . '-' . $roomId . sprintf('%02d', $i);
        }
        
        return $codes;
    }
}
