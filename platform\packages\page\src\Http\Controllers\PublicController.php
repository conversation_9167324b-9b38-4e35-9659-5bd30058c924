<?php

namespace Bo<PERSON>ble\Page\Http\Controllers;

use Bo<PERSON>ble\Base\Http\Controllers\BaseController;
use <PERSON><PERSON>ble\Page\Models\Page;
use <PERSON><PERSON>ble\Page\Services\PageService;
use <PERSON><PERSON>ble\Slug\Facades\SlugHelper;
use <PERSON><PERSON>ble\Theme\Events\RenderingSingleEvent;
use Bo<PERSON>ble\Theme\Facades\Theme;

class PublicController extends BaseController
{
    public function getPage(string $slug, PageService $pageService)
    {
        $slug = SlugHelper::getSlug($slug, SlugHelper::getPrefix(Page::class));

        if (! $slug) {
            abort(404);
        }

        $data = $pageService->handleFrontRoutes($slug);

        if (isset($data['slug']) && $data['slug'] !== $slug->key) {
            return redirect()->to(url(SlugHelper::getPrefix(Page::class) . '/' . $data['slug']));
        }

        event(new RenderingSingleEvent($slug));

        return Theme::scope($data['view'], $data['data'], $data['default_view'])->render();
    }
}
