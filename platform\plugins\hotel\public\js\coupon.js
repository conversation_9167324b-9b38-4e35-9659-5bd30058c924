$((function(){var e=$(document).find("form.coupon-form");e.on("click",'[data-bb-toggle="coupon-generate-code"]',(function(n){n.preventDefault();var t=$(n.currentTarget);$httpClient.make().withButtonLoading(t).post(t.data("url")).then((function(n){var t=n.data;e.find('input[name="code"]').val(t.data)}))})).on("change",'select[name="type"]',(function(n){var t;t="percentage"===n.currentTarget.value?"%":window.coupon.currency||"$",e.find("span.icon-type").text(t)})).on("change",'input[name="never_expired"]',(function(n){n.currentTarget.checked?(e.find('input[name="expires_date"]').prop("disabled",!0),e.find('input[name="expires_time"]').prop("disabled",!0)):(e.find('input[name="expires_date"]').prop("disabled",!1),e.find('input[name="expires_time"]').prop("disabled",!1))})).on("change",'input[name="is_unlimited"]',(function(n){var t=e.find('input[name="quantity"]').closest(".mb-3.position-relative");n.currentTarget.checked?t.addClass("d-none"):t.removeClass("d-none")})),e.find('input[name="never_expired"]').trigger("change")}));