function is_numeric(mixed_var) {
    //  discuss at: http://phpjs.org/functions/is_numeric/
    // original by: <PERSON> (http://kevin.vanzonneveld.net)
    // improved by: David
    // improved by: taith
    // bugfixed by: <PERSON>
    // bugfixed by: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (http://webdevhobo.blogspot.com/)
    // bugfixed by: <PERSON> (http://brett-zamir.me)
    // bugfixed by: <PERSON> (http://shnoulle.net)
    //   example 1: is_numeric(186.31);
    //   returns 1: true
    //   example 2: is_numeric('<PERSON>');
    //   returns 2: false
    //   example 3: is_numeric(' +186.31e2');
    //   returns 3: true
    //   example 4: is_numeric('');
    //   returns 4: false
    //   example 5: is_nume<PERSON>([]);
    //   returns 5: false
    //   example 6: is_numeric('1 ');
    //   returns 6: false

    var whitespace =
        ' \n\r\t\f\x0b\xa0\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u200b\u2028\u2029\u3000'
    return (
        (typeof mixed_var === 'number' ||
            (typeof mixed_var === 'string' && whitespace.indexOf(mixed_var.slice(-1)) === -1)) &&
        mixed_var !== '' &&
        !isNaN(mixed_var)
    )
}
