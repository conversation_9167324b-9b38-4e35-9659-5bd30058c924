$(document).ready(function() {
    // Initialize date inputs with minimum date as today
    const today = new Date().toISOString().split('T')[0];
    $('#start_date, #end_date').attr('min', today);

    // Add modern form styling and animations
    $('.form-control').on('focus', function() {
        $(this).css({
            'border-color': '#667eea',
            'box-shadow': '0 0 0 0.2rem rgba(102, 126, 234, 0.25)',
            'transform': 'scale(1.02)'
        });
    }).on('blur', function() {
        $(this).css({
            'border-color': '#e2e8f0',
            'box-shadow': 'none',
            'transform': 'scale(1)'
        });
    });

    // Handle pricing scope change with animations
    $('#pricing_scope').change(function() {
        const scope = $(this).val();

        // Hide all groups with animation
        $('#room_category_group, #room_group').slideUp(300);

        setTimeout(() => {
            if (scope === 'room_category') {
                $('#room_category_group').slideDown(300);
                $('#room_category_id').attr('required', true);
                $('#room_id').removeAttr('required');
            } else if (scope === 'specific_room') {
                $('#room_group').slideDown(300);
                $('#room_id').attr('required', true);
                $('#room_category_id').removeAttr('required');
            } else {
                $('#room_category_id, #room_id').removeAttr('required');
            }
        }, 300);
    });

    // Handle adjustment type change with enhanced help text
    $('#adjustment_type').change(function() {
        const type = $(this).val();
        const helpText = $('#adjustment_help');
        const valueInput = $('#adjustment_value');

        // Add animation to help text
        helpText.fadeOut(200, function() {
            switch(type) {
                case 'percentage':
                    helpText.text('Enter percentage (e.g., 20 for 20% increase)');
                    valueInput.attr('placeholder', '20');
                    break;
                case 'fixed_amount':
                    helpText.text('Enter amount to add (e.g., 500 for ₹500 increase)');
                    valueInput.attr('placeholder', '500');
                    break;
                case 'fixed_price':
                    helpText.text('Enter the exact price (e.g., 3000 for ₹3000)');
                    valueInput.attr('placeholder', '3000');
                    break;
            }
            helpText.fadeIn(200);
        });
    });

    // Handle bulk seasonal form submission with enhanced UI feedback
    $('#bulkSeasonalForm').submit(function(e) {
        e.preventDefault();

        const formData = $(this).serialize();
        const submitBtn = $(this).find('button[type="submit"]');
        const originalText = submitBtn.html();

        // Enhanced loading state
        submitBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-1"></i>Creating...');
        submitBtn.css('opacity', '0.7');

        // Add loading overlay to modal
        const loadingOverlay = $('<div class="loading-overlay" style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: rgba(255, 255, 255, 0.8); display: flex; align-items: center; justify-content: center; z-index: 1050;"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div></div>');
        $('#bulkSeasonalModal .modal-content').css('position', 'relative').append(loadingOverlay);

        $.ajax({
            url: '/admin/hotel/seasonal-pricing/create-bulk',
            method: 'POST',
            data: formData,
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                if (response.error) {
                    if (response.data && response.data.conflicts) {
                        showConflictDialog(response.data.conflicts, formData);
                    } else {
                        showError(response.message);
                    }
                } else {
                    showSuccess(response.message);
                    $('#bulkSeasonalModal').modal('hide');

                    // Smooth page reload with fade effect
                    $('body').fadeOut(300, function() {
                        location.reload();
                    });
                }
            },
            error: function(xhr) {
                const response = xhr.responseJSON;
                if (response && response.data && response.data.errors) {
                    showValidationErrors(response.data.errors);
                } else {
                    showError('An error occurred while creating seasonal pricing');
                }
            },
            complete: function() {
                // Remove loading overlay
                $('.loading-overlay').remove();
                submitBtn.prop('disabled', false).html(originalText);
                submitBtn.css('opacity', '1');
            }
        });
    });

    // Handle quick setup form submission with enhanced UI feedback
    $('#quickSetupForm').submit(function(e) {
        e.preventDefault();

        const formData = $(this).serialize();
        const submitBtn = $(this).find('button[type="submit"]');
        const originalText = submitBtn.html();

        // Enhanced loading state
        submitBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-1"></i>Setting up...');
        submitBtn.css('opacity', '0.7');

        // Add loading overlay to modal
        const loadingOverlay = $('<div class="loading-overlay" style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: rgba(255, 255, 255, 0.8); display: flex; align-items: center; justify-content: center; z-index: 1050;"><div class="spinner-border text-success" role="status"><span class="visually-hidden">Loading...</span></div></div>');
        $('#quickSetupModal .modal-content').css('position', 'relative').append(loadingOverlay);

        $.ajax({
            url: '/admin/hotel/seasonal-pricing/quick-setup',
            method: 'POST',
            data: formData,
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                if (response.error) {
                    showError(response.message);
                } else {
                    showSuccess(response.message);
                    $('#quickSetupModal').modal('hide');

                    // Smooth page reload with fade effect
                    $('body').fadeOut(300, function() {
                        location.reload();
                    });
                }
            },
            error: function(xhr) {
                showError('An error occurred while setting up seasonal pricing');
            },
            complete: function() {
                // Remove loading overlay
                $('.loading-overlay').remove();
                submitBtn.prop('disabled', false).html(originalText);
                submitBtn.css('opacity', '1');
            }
        });
    });

    // Fix Bootstrap 5 modal compatibility
    $('[data-toggle="modal"]').attr('data-bs-toggle', 'modal');
    $('[data-target]').each(function() {
        $(this).attr('data-bs-target', $(this).attr('data-target'));
    });
    $('[data-dismiss="modal"]').attr('data-bs-dismiss', 'modal');
});

// Enhanced preview pricing function
function previewPricing() {
    const formData = $('#bulkSeasonalForm').serialize();
    const previewBtn = $('button[onclick="previewPricing()"]');
    const originalText = previewBtn.html();

    // Show loading state
    previewBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-1"></i>Loading Preview...');

    $.ajax({
        url: '/admin/hotel/seasonal-pricing/preview',
        method: 'POST',
        data: formData,
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        success: function(response) {
            if (response.error) {
                showError(response.message);
            } else {
                displayPreview(response.data.preview);
            }
        },
        error: function() {
            showError('Failed to generate preview');
        },
        complete: function() {
            previewBtn.prop('disabled', false).html(originalText);
        }
    });
}

// Enhanced display preview results with modern styling
function displayPreview(preview) {
    let html = '<div class="table-responsive" style="max-height: 400px; overflow-y: auto;">';
    html += '<table class="table table-sm table-hover" style="margin-bottom: 0;">';
    html += '<thead style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; position: sticky; top: 0; z-index: 10;">';
    html += '<tr><th style="border: none; padding: 12px;">Room</th><th style="border: none; padding: 12px;">Current Price</th><th style="border: none; padding: 12px;">New Price</th><th style="border: none; padding: 12px;">Change</th></tr>';
    html += '</thead><tbody>';

    preview.forEach(function(item, index) {
        const increaseClass = item.price_increase > 0 ? 'text-success' : 'text-danger';
        const bgClass = index % 2 === 0 ? 'bg-light' : '';
        html += `<tr class="${bgClass}" style="transition: all 0.3s ease;">
            <td style="padding: 12px; font-weight: 600;">${item.room_name}</td>
            <td style="padding: 12px;">₹${item.base_price.toFixed(2)}</td>
            <td style="padding: 12px; font-weight: 600; color: #2d3748;">₹${item.seasonal_price.toFixed(2)}</td>
            <td style="padding: 12px;" class="${increaseClass}">
                <strong>₹${item.price_increase.toFixed(2)}</strong>
                <br><small>(${item.percentage_increase}%)</small>
            </td>
        </tr>`;
    });

    html += '</tbody></table></div>';

    // Add summary statistics
    const totalRooms = preview.length;
    const avgIncrease = preview.reduce((sum, item) => sum + item.price_increase, 0) / totalRooms;
    const maxIncrease = Math.max(...preview.map(item => item.price_increase));
    const minIncrease = Math.min(...preview.map(item => item.price_increase));

    html += `<div class="mt-3 p-3" style="background: #f7fafc; border-radius: 10px; border: 1px solid #e2e8f0;">
        <h6 style="color: #2d3748; margin-bottom: 15px;"><i class="fas fa-chart-bar me-2"></i>Preview Summary</h6>
        <div class="row text-center">
            <div class="col-3">
                <div style="color: #667eea; font-weight: 600; font-size: 1.2rem;">${totalRooms}</div>
                <small style="color: #718096;">Rooms Affected</small>
            </div>
            <div class="col-3">
                <div style="color: #48bb78; font-weight: 600; font-size: 1.2rem;">₹${avgIncrease.toFixed(0)}</div>
                <small style="color: #718096;">Avg. Increase</small>
            </div>
            <div class="col-3">
                <div style="color: #ed8936; font-weight: 600; font-size: 1.2rem;">₹${maxIncrease.toFixed(0)}</div>
                <small style="color: #718096;">Max Increase</small>
            </div>
            <div class="col-3">
                <div style="color: #4299e1; font-weight: 600; font-size: 1.2rem;">₹${minIncrease.toFixed(0)}</div>
                <small style="color: #718096;">Min Increase</small>
            </div>
        </div>
    </div>`;

    $('#previewContent').html(html);
    $('#previewSection').slideDown(300);
}

// Show conflict dialog
function showConflictDialog(conflicts, formData) {
    let conflictHtml = '<div class="alert alert-warning">';
    conflictHtml += '<h6>Pricing Conflicts Detected:</h6><ul>';
    
    conflicts.forEach(function(conflict) {
        conflictHtml += `<li><strong>${conflict.room_name}</strong>: `;
        conflict.conflicting_prices.forEach(function(price) {
            conflictHtml += `${price.name} (${price.start_date} to ${price.end_date}) `;
        });
        conflictHtml += '</li>';
    });
    
    conflictHtml += '</ul>';
    conflictHtml += '<p>Do you want to proceed anyway? This will create overlapping pricing rules.</p>';
    conflictHtml += '<button type="button" class="btn btn-warning" onclick="forceCreatePricing()">Proceed Anyway</button>';
    conflictHtml += '</div>';
    
    $('#previewContent').html(conflictHtml);
    $('#previewSection').show();
}

// Force create pricing despite conflicts
function forceCreatePricing() {
    const formData = $('#bulkSeasonalForm').serialize() + '&force_create=true';
    
    $.ajax({
        url: '/admin/hotel/seasonal-pricing/create-bulk',
        method: 'POST',
        data: formData,
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        success: function(response) {
            if (response.error) {
                showError(response.message);
            } else {
                showSuccess(response.message);
                $('#bulkSeasonalModal').modal('hide');
                location.reload();
            }
        },
        error: function() {
            showError('Failed to create seasonal pricing');
        }
    });
}

// Edit pricing function
function editPricing(id) {
    // This would open an edit modal - implementation depends on your existing edit functionality
    window.location.href = `/admin/hotel/room-prices/${id}/edit`;
}

// Enhanced delete pricing function with modern confirmation
function deletePricing(id) {
    // Create modern confirmation dialog
    const confirmHtml = `
        <div class="modal fade" id="deleteConfirmModal" tabindex="-1">
            <div class="modal-dialog modal-sm">
                <div class="modal-content" style="border-radius: 20px; border: none;">
                    <div class="modal-header" style="background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%); color: white; border-radius: 20px 20px 0 0; border: none;">
                        <h5 class="modal-title"><i class="fas fa-trash me-2"></i>Confirm Delete</h5>
                    </div>
                    <div class="modal-body" style="padding: 30px; text-align: center;">
                        <i class="fas fa-exclamation-triangle" style="font-size: 3rem; color: #f56565; margin-bottom: 20px;"></i>
                        <p style="color: #2d3748; font-size: 1.1rem; margin-bottom: 0;">Are you sure you want to delete this pricing rule?</p>
                        <small style="color: #718096;">This action cannot be undone.</small>
                    </div>
                    <div class="modal-footer" style="border: none; padding: 20px 30px;">
                        <button type="button" class="btn btn-outline-secondary me-2" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-modern" style="background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%); color: white;" onclick="confirmDelete(${id})">
                            <i class="fas fa-trash me-1"></i>Delete
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Remove existing modal if any
    $('#deleteConfirmModal').remove();

    // Add modal to body and show
    $('body').append(confirmHtml);
    $('#deleteConfirmModal').modal('show');
}

// Confirm delete function
function confirmDelete(id) {
    const deleteBtn = $('button[onclick="confirmDelete(' + id + ')"]');
    const originalText = deleteBtn.html();

    deleteBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-1"></i>Deleting...');

    $.ajax({
        url: `/admin/hotel/room-prices/${id}`,
        method: 'DELETE',
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        success: function(response) {
            if (response.error) {
                showError(response.message);
            } else {
                showSuccess('Pricing rule deleted successfully');
                $('#deleteConfirmModal').modal('hide');
                $('body').fadeOut(300, function() {
                    location.reload();
                });
            }
        },
        error: function() {
            showError('Failed to delete pricing rule');
        },
        complete: function() {
            deleteBtn.prop('disabled', false).html(originalText);
        }
    });
}

// Enhanced utility functions for notifications
function showSuccess(message) {
    if (typeof toastr !== 'undefined') {
        toastr.success(message, 'Success!', {
            timeOut: 5000,
            progressBar: true,
            positionClass: 'toast-top-right',
            showMethod: 'slideDown',
            hideMethod: 'slideUp'
        });
    } else if (typeof Botble !== 'undefined' && Botble.showSuccess) {
        Botble.showSuccess(message);
    } else {
        alert('Success: ' + message);
    }
}

function showError(message) {
    if (typeof toastr !== 'undefined') {
        toastr.error(message, 'Error!', {
            timeOut: 7000,
            progressBar: true,
            positionClass: 'toast-top-right',
            showMethod: 'slideDown',
            hideMethod: 'slideUp'
        });
    } else if (typeof Botble !== 'undefined' && Botble.showError) {
        Botble.showError(message);
    } else {
        alert('Error: ' + message);
    }
}

function showValidationErrors(errors) {
    let errorMessage = 'Please fix the following validation errors:\n\n';
    Object.keys(errors).forEach(function(field) {
        errorMessage += `• ${field.replace('_', ' ').toUpperCase()}: ${errors[field].join(', ')}\n`;
    });
    showError(errorMessage);
}
