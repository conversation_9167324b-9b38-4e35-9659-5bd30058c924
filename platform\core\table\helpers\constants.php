<?php

if (! defined('BASE_FILTER_TABLE_HEADINGS')) {
    define('BASE_FILTER_TABLE_HEADINGS', 'table_headings');
}

if (! defined('BASE_FILTER_GET_LIST_DATA')) {
    define('BASE_FILTER_GET_LIST_DATA', 'get_list_data');
}

if (! defined('BASE_FILTER_GET_LIST_DATA_FOR_QUERY_TABLE')) {
    define('BASE_FILTER_GET_LIST_DATA_FOR_QUERY_TABLE', 'get_list_data_for_query_table');
}

if (! defined('BASE_FILTER_TABLE_BUTTONS')) {
    define('BASE_FILTER_TABLE_BUTTONS', 'base_filter_table_buttons');
}

if (! defined('BASE_FILTER_TABLE_QUERY')) {
    define('BASE_FILTER_TABLE_QUERY', 'base_filter_table_query');
}

if (! defined('BASE_FILTER_TABLE_BEFORE_RENDER')) {
    define('BASE_FILTER_TABLE_BEFORE_RENDER', 'base_filter_table_before_render');
}

if (! defined('BASE_FILTER_TABLE_AFTER_RENDER')) {
    define('BASE_FILTER_TABLE_AFTER_RENDER', 'base_filter_table_after_render');
}

if (! defined('BASE_FILTER_TABLE_FOOTER_RENDER')) {
    define('BASE_FILTER_TABLE_FOOTER_RENDER', 'base_filter_table_footer_render');
}
