<?php

namespace Botble\Hotel\Console\Commands;

use Botble\Hotel\Models\Room;
use Botble\Hotel\Models\RoomPrice;
use Botble\Hotel\Services\DynamicPricingService;
use Botble\Hotel\Services\SeasonalPricingService;
use Carbon\Carbon;
use Illuminate\Console\Command;

class TestSeasonalPricingCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'hotel:test-seasonal-pricing {room_id?}';

    /**
     * The console command description.
     */
    protected $description = 'Test the seasonal pricing functionality';

    protected $dynamicPricingService;
    protected $seasonalPricingService;

    public function __construct(DynamicPricingService $dynamicPricingService, SeasonalPricingService $seasonalPricingService)
    {
        parent::__construct();
        $this->dynamicPricingService = $dynamicPricingService;
        $this->seasonalPricingService = $seasonalPricingService;
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('🏨 Testing Seasonal Pricing System');
        $this->info('=====================================');

        $roomId = $this->argument('room_id');
        
        if ($roomId) {
            $room = Room::find($roomId);
            if (!$room) {
                $this->error("Room with ID {$roomId} not found!");
                return 1;
            }
            $rooms = collect([$room]);
        } else {
            $rooms = Room::where('status', 'published')->take(3)->get();
        }

        if ($rooms->isEmpty()) {
            $this->error('No rooms found! Please create some rooms first.');
            return 1;
        }

        foreach ($rooms as $room) {
            $this->testRoomPricing($room);
            $this->newLine();
        }

        $this->testSeasonalPricingStats();
        $this->testBulkSeasonalCreation();

        $this->info('✅ Seasonal pricing test completed!');
        return 0;
    }

    protected function testRoomPricing(Room $room): void
    {
        $this->info("🛏️  Testing Room: {$room->name} (Base Price: ₹{$room->price})");
        $this->line('---------------------------------------------------');

        // Test current week pricing
        $startDate = Carbon::now()->format('Y-m-d');
        $endDate = Carbon::now()->addDays(7)->format('Y-m-d');
        
        $this->info("📅 Current Week ({$startDate} to {$endDate}):");
        $currentWeekPricing = $this->dynamicPricingService->calculateRoomPrice($room, $startDate, $endDate);
        $this->displayPricingResult($currentWeekPricing);

        // Test next week pricing (where function season might apply)
        $nextWeekStart = Carbon::now()->addWeek()->format('Y-m-d');
        $nextWeekEnd = Carbon::now()->addWeek()->addDays(7)->format('Y-m-d');
        
        $this->info("📅 Next Week ({$nextWeekStart} to {$nextWeekEnd}):");
        $nextWeekPricing = $this->dynamicPricingService->calculateRoomPrice($room, $nextWeekStart, $nextWeekEnd);
        $this->displayPricingResult($nextWeekPricing);

        // Test future pricing (3 months ahead)
        $futureStart = Carbon::now()->addMonths(3)->format('Y-m-d');
        $futureEnd = Carbon::now()->addMonths(3)->addDays(7)->format('Y-m-d');
        
        $this->info("📅 Future Pricing ({$futureStart} to {$futureEnd}):");
        $futurePricing = $this->dynamicPricingService->calculateRoomPrice($room, $futureStart, $futureEnd);
        $this->displayPricingResult($futurePricing);

        // Test pricing transparency
        $transparency = $this->dynamicPricingService->getPricingTransparency($room, $startDate, $endDate);
        $this->displayTransparencyInfo($transparency);
    }

    protected function displayPricingResult(array $pricing): void
    {
        $this->line("  Base Price: ₹" . number_format($pricing['base_price'], 2));
        $this->line("  Final Price: ₹" . number_format($pricing['total_price'], 2));
        $this->line("  Nights: {$pricing['nights']}");
        
        if ($pricing['total_price'] != $pricing['base_price']) {
            $difference = $pricing['total_price'] - $pricing['base_price'];
            $percentage = round(($difference / $pricing['base_price']) * 100, 1);
            $this->line("  Adjustment: ₹" . number_format($difference, 2) . " ({$percentage}%)");
        }

        if (!empty($pricing['applied_rules'])) {
            $this->line("  Applied Rules:");
            foreach ($pricing['applied_rules'] as $rule) {
                $this->line("    - {$rule['name']} (₹" . number_format($rule['adjustment'], 2) . ")");
            }
        }

        // Show daily breakdown if there are price variations
        $hasVariations = false;
        foreach ($pricing['daily_prices'] as $dayPrice) {
            if ($dayPrice['base_price'] != $dayPrice['final_price']) {
                $hasVariations = true;
                break;
            }
        }

        if ($hasVariations) {
            $this->line("  Daily Breakdown:");
            foreach ($pricing['daily_prices'] as $dayPrice) {
                $date = Carbon::parse($dayPrice['date'])->format('M j');
                if ($dayPrice['base_price'] != $dayPrice['final_price']) {
                    $this->line("    {$date}: ₹" . number_format($dayPrice['base_price'], 2) . " → ₹" . number_format($dayPrice['final_price'], 2));
                    if (!empty($dayPrice['price_explanation'])) {
                        $this->line("      Reason: {$dayPrice['price_explanation']}");
                    }
                } else {
                    $this->line("    {$date}: ₹" . number_format($dayPrice['final_price'], 2));
                }
            }
        }
    }

    protected function displayTransparencyInfo(array $transparency): void
    {
        if (!empty($transparency['explanations'])) {
            $this->line("  Price Explanations:");
            foreach ($transparency['explanations'] as $explanation) {
                $date = Carbon::parse($explanation['date'])->format('M j');
                $this->line("    {$date}: {$explanation['explanation']}");
            }
        }

        if (!empty($transparency['seasonal_info'])) {
            $this->line("  Seasonal Information:");
            foreach ($transparency['seasonal_info'] as $season) {
                $this->line("    {$season['season_name']}: ₹" . number_format($season['price_impact'], 2) . " impact");
            }
        }
    }

    protected function testSeasonalPricingStats(): void
    {
        $this->info('📊 Seasonal Pricing Statistics');
        $this->line('==============================');

        $stats = $this->seasonalPricingService->getSeasonalPricingStats();
        
        foreach ($stats as $seasonType => $stat) {
            $this->line("{$stat['name']}: {$stat['total_count']} total, {$stat['future_count']} future");
        }
    }

    protected function testBulkSeasonalCreation(): void
    {
        $this->info('🔧 Testing Bulk Seasonal Creation');
        $this->line('==================================');

        // Test creating a sample seasonal pricing
        $testData = [
            'season_name' => 'Test Season',
            'season_type' => RoomPrice::SEASON_SPECIAL_EVENT,
            'start_date' => Carbon::now()->addMonths(6)->format('Y-m-d'),
            'end_date' => Carbon::now()->addMonths(6)->addDays(10)->format('Y-m-d'),
            'pricing_scope' => 'all_rooms',
            'adjustment_type' => 'percentage',
            'adjustment_value' => 15,
            'price_reason' => 'Test seasonal pricing',
            'customer_message' => 'Test season with special amenities',
            'priority' => 12,
        ];

        // Preview the pricing first
        $preview = $this->seasonalPricingService->previewSeasonalPricing($testData);
        
        $this->line("Preview for Test Season (+15%):");
        foreach ($preview as $item) {
            $this->line("  {$item['room_name']}: ₹{$item['base_price']} → ₹{$item['seasonal_price']} (+₹{$item['price_increase']})");
        }

        $this->line("\nNote: This was just a preview. No actual pricing rules were created.");
    }
}
