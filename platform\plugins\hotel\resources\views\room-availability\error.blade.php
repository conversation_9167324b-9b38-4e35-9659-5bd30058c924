@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="widget meta-boxes">
                <div class="widget-title">
                    <h4>
                        <span>{{ trans('plugins/hotel::room.room_availability') }}</span>
                    </h4>
                </div>
                <div class="widget-body">
                    <div class="alert alert-danger">
                        <h4>{{ $message }}</h4>
                        @if(config('app.debug'))
                            <p>{{ $error }}</p>
                        @endif
                    </div>
                    <div class="mt-3">
                        <p>Please make sure you have run the database migrations for the room inventory system.</p>
                        <p>You can run the SQL migration file manually to create the necessary tables and columns.</p>
                        <a href="{{ route('dashboard.index') }}" class="btn btn-primary">Return to Dashboard</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
