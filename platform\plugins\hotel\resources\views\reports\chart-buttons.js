/**
 * Chart Button Functions
 * This file contains the functions for the chart buttons in the booking analytics dashboard
 */

// Function to refresh chart data
function refreshChartData() {
    console.log('Refresh button clicked');
    
    // Show loading indicators
    $('.card').addClass('loading');
    
    // Add loading spinners to charts
    $('.chart-container').each(function() {
        if (!$(this).find('.chart-loading').length) {
            $(this).append('<div class="chart-loading"><div class="chart-loading-spinner"></div></div>');
        } else {
            $(this).find('.chart-loading').show();
        }
    });
    
    // Get filter values
    const startDate = $('input[name="start_date"]').val();
    const endDate = $('input[name="end_date"]').val();
    const roomCategoryId = $('#room-category').val();
    const groupBy = $('#group-by').val();
    
    // Make AJAX request
    $.ajax({
        url: bookingAnalyticsRoute,
        type: 'GET',
        dataType: 'json',
        data: {
            start_date: startDate,
            end_date: endDate,
            room_category_id: roomCategoryId,
            group_by: groupBy
        },
        success: function(response) {
            if (response.error) {
                Botble.showError(response.message);
            } else {
                // Update charts with new data
                updateBookingTrendsChart(response.data.chartData);
                updateBookingStatusChart(response.data.bookingStatusData);
                updateTopRoomCategoriesChart(response.data.topRoomCategories);
                updateRoomAvailabilityChart(response.data.roomAvailability);
                
                // Update metrics
                updateMetrics(response.data.metrics);
            }
        },
        error: function(error) {
            console.error('Error refreshing data:', error);
            Botble.handleError(error);
        },
        complete: function() {
            // Hide loading indicators
            $('.card').removeClass('loading');
            $('.chart-loading').hide();
            
            // Log completion
            console.log('Data refresh completed');
        }
    });
}

// Function to debug chart
function debugChart() {
    console.log('Debug button clicked');
    
    // Add spinner animation
    $('#debug-chart i').addClass('fa-spin');
    
    // Create sample data for testing
    const sampleData = {
        labels: ['Jan 1', 'Jan 2', 'Jan 3', 'Jan 4', 'Jan 5'],
        bookings: [5, 10, 7, 12, 8],
        revenue: [5000, 10000, 7000, 12000, 8000]
    };
    
    // Log the current chart state
    console.group('Chart Debug');
    console.log('Chart object:', window.bookingTrendsChart);
    console.log('Current data:', window.bookingTrendsChart ? window.bookingTrendsChart.data : 'Chart not initialized');
    console.log('Sample data:', sampleData);
    console.groupEnd();
    
    if (!window.bookingTrendsChart) {
        console.error('Chart not initialized yet');
        alert('Chart not initialized yet. Check console for details.');
        $('#debug-chart i').removeClass('fa-spin');
        return;
    }
    
    // Try updating with sample data
    try {
        window.bookingTrendsChart.data.labels = sampleData.labels;
        window.bookingTrendsChart.data.datasets[0].data = sampleData.bookings;
        window.bookingTrendsChart.data.datasets[1].data = sampleData.revenue;
        window.bookingTrendsChart.update();
        
        console.log('Debug data applied successfully');
        alert('Debug data applied to chart. Check console for details.');
    } catch (e) {
        console.error('Error applying debug data:', e);
        alert('Error applying debug data: ' + e.message);
    }
    
    // Remove spinner after a delay
    setTimeout(() => {
        $('#debug-chart i').removeClass('fa-spin');
    }, 1000);
}

// Function to change chart type
function changeChartType(type) {
    console.log('Changing chart type to:', type);
    
    if (!window.bookingTrendsChart) {
        console.error('Chart not initialized yet');
        alert('Chart not initialized yet');
        return;
    }
    
    try {
        // Update active button state
        $('.chart-type').removeClass('active');
        $(`.chart-type[data-type="${type}"]`).addClass('active');
        
        // Update chart type
        if (type === 'area') {
            window.bookingTrendsChart.config.type = 'line';
            
            // Update fill options for area chart
            window.bookingTrendsChart.data.datasets.forEach((dataset) => {
                dataset.fill = true;
            });
        } else {
            window.bookingTrendsChart.config.type = type;
            
            // Update fill options
            window.bookingTrendsChart.data.datasets.forEach((dataset) => {
                dataset.fill = false;
            });
        }
        
        // Update chart
        window.bookingTrendsChart.update();
        
        console.log('Chart type updated successfully');
    } catch (e) {
        console.error('Error changing chart type:', e);
        alert('Error changing chart type: ' + e.message);
    }
}

// Initialize button event listeners
function initializeChartButtons() {
    console.log('Initializing chart buttons');
    
    // Refresh button
    $('#refresh-charts').off('click').on('click', function(e) {
        e.preventDefault();
        refreshChartData();
    });
    
    // Debug button
    $('#debug-chart').off('click').on('click', function(e) {
        e.preventDefault();
        debugChart();
    });
    
    // Chart type buttons
    $('.chart-type').off('click').on('click', function(e) {
        e.preventDefault();
        const type = $(this).data('type');
        changeChartType(type);
    });
    
    console.log('Chart buttons initialized');
}

// Export functions
window.refreshChartData = refreshChartData;
window.debugChart = debugChart;
window.changeChartType = changeChartType;
window.initializeChartButtons = initializeChartButtons;
