<?php

namespace Botble\Hotel\Http\Controllers\Front\Customers;

use Botble\Base\Http\Controllers\BaseController;
use Botble\Hotel\Facades\InvoiceHelper;
use Botble\Hotel\Models\Booking;
use Botble\Hotel\Models\Invoice;
use Bo<PERSON>ble\SeoHelper\Facades\SeoHelper;
use Botble\Theme\Facades\Theme;
use Illuminate\Http\Request;

class BookingController extends BaseController
{
    public function __construct()
    {
        Theme::asset()
            ->add('customer-style', 'vendor/core/plugins/hotel/css/customer.css');

        Theme::asset()
            ->container('footer')
            ->add('utilities-js', 'vendor/core/plugins/hotel/js/utilities.js', ['jquery'])
            ->add('cropper-js', 'vendor/core/core/base/libraries/cropper.min.js', ['jquery'])
            ->add('avatar-js', 'vendor/core/plugins/hotel/js/avatar.js', ['jquery']);
    }

    public function index()
    {
        SeoHelper::setTitle(__('Bookings'));

        $bookings = Booking::query()
            ->where([
                'customer_id' => auth('customer')->id(),
            ])
            ->with(['room', 'invoice.items'])
            ->orderByDesc('created_at')
            ->paginate(5);

        Theme::breadcrumb()
            ->add(__('Home'), route('public.index'))
            ->add(__('Bookings'), route('customer.bookings'));

        return Theme::scope(
            'hotel.customers.bookings.list',
            compact('bookings'),
            'plugins/hotel::themes.customers.bookings.list'
        )->render();
    }

    public function show(int|string $id)
    {
        $booking = Booking::query()
            ->with(['invoice.items', 'room'])
            ->where([
                'transaction_id' => $id,
                'customer_id' => auth('customer')->id(),
            ])
            ->firstOrFail();

        SeoHelper::setTitle(__('Booking Information'));

        Theme::breadcrumb()->add(__('Home'), route('public.index'))
            ->add(
                __('Booking Information'),
                route('customer.bookings.show', $id)
            );

        return Theme::scope(
            'hotel.customers.bookings.detail',
            compact('booking'),
            'plugins/hotel::themes.customers.bookings.detail'
        )->render();
    }

    public function getGenerateInvoice(int|string $invoiceId, Request $request)
    {
        $invoice = Invoice::query()->findOrFail($invoiceId);

        abort_unless($this->canViewInvoice($invoice), 404);

        if ($request->input('type') === 'print' || $request->input('type') === 'view') {
            // Generate PDF content
            $pdf = InvoiceHelper::makeInvoice($invoice);
            $output = $pdf->output();

            // Force the browser to display the PDF inline
            return new \Illuminate\Http\Response($output, 200, [
                'Content-Type' => 'application/pdf',
                'Content-Length' => strlen($output),
                'Content-Disposition' => 'inline; filename="invoice-' . $invoice->code . '.pdf"',
                'Cache-Control' => 'public, must-revalidate, max-age=0',
                'Pragma' => 'public',
                'X-Content-Type-Options' => 'nosniff',
            ]);
        }

        return InvoiceHelper::downloadInvoice($invoice);
    }

    public function viewInvoice(int|string $invoiceId)
    {
        $invoice = Invoice::query()->findOrFail($invoiceId);

        abort_unless($this->canViewInvoice($invoice), 404);

        // Generate PDF content
        $pdf = InvoiceHelper::makeInvoice($invoice);
        $output = $pdf->output();

        // Force the browser to display the PDF inline with all necessary headers
        return new \Illuminate\Http\Response($output, 200, [
            'Content-Type' => 'application/pdf',
            'Content-Length' => strlen($output),
            'Content-Disposition' => 'inline; filename="invoice-' . $invoice->code . '.pdf"',
            'Cache-Control' => 'public, must-revalidate, max-age=0',
            'Pragma' => 'public',
            'X-Content-Type-Options' => 'nosniff',
            'Accept-Ranges' => 'bytes',
        ]);
    }

    protected function canViewInvoice(Invoice $invoice): bool
    {
        return auth('customer')->id() == $invoice->payment->customer_id;
    }
}
