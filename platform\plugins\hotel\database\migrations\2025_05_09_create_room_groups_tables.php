<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasTable('ht_room_groups')) {
            Schema::create('ht_room_groups', function (Blueprint $table) {
                $table->id();
                $table->string('name');
                $table->string('code')->unique();
                $table->text('description')->nullable();
                $table->timestamps();
            });
        }

        if (!Schema::hasTable('ht_room_group_items')) {
            Schema::create('ht_room_group_items', function (Blueprint $table) {
                $table->id();
                $table->foreignId('room_group_id')->constrained('ht_room_groups')->onDelete('cascade');
                $table->string('room_id_code');
                $table->timestamps();
                
                $table->unique(['room_group_id', 'room_id_code']);
            });
        }
        
        // Insert the 2BHK room group
        DB::table('ht_room_groups')->insert([
            'name' => '2BHK Package',
            'code' => '2BHK',
            'description' => 'A 2BHK package with two rooms (MRR-777 and MRR-999)',
            'created_at' => now(),
            'updated_at' => now(),
        ]);
        
        // Get the ID of the inserted room group
        $roomGroupId = DB::table('ht_room_groups')->where('code', '2BHK')->value('id');
        
        // Insert the room group items
        DB::table('ht_room_group_items')->insert([
            [
                'room_group_id' => $roomGroupId,
                'room_id_code' => 'MRR-777',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'room_group_id' => $roomGroupId,
                'room_id_code' => 'MRR-999',
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('ht_room_group_items');
        Schema::dropIfExists('ht_room_groups');
    }
};
