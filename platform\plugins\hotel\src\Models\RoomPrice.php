<?php

namespace Botble\Hotel\Models;

use Botble\Base\Models\BaseModel;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Carbon\Carbon;

class RoomPrice extends BaseModel
{
    use SoftDeletes;

    protected $table = 'ht_room_prices';

    // Pricing category constants
    const CATEGORY_INDIVIDUAL_ROOM = 'individual_room';
    const CATEGORY_ROOM_CATEGORY = 'room_category';
    const CATEGORY_ALL_ROOMS = 'all_rooms';

    // Recurrence pattern constants
    const RECURRENCE_NONE = 'none';
    const RECURRENCE_DAILY = 'daily';
    const RECURRENCE_WEEKLY = 'weekly';
    const RECURRENCE_MONTHLY = 'monthly';
    const RECURRENCE_YEARLY = 'yearly';

    // Approval status constants
    const APPROVAL_PENDING = 'pending';
    const APPROVAL_APPROVED = 'approved';
    const APPROVAL_REJECTED = 'rejected';

    protected $fillable = [
        'room_id',
        'pricing_category',
        'room_category_id',
        'name',
        'price',
        'start_date',
        'end_date',
        'priority',
        'override_priority',
        'is_active',
        'is_promotional',
        'promotional_expires_at',
        'description',
        'pricing_type',
        'adjustment_value',
        'days_of_week',
        'min_nights',
        'max_nights',
        'recurrence_pattern',
        'recurrence_end_date',
        'recurrence_config',
        'approval_status',
        'approved_by',
        'approved_at',
        'approval_notes',
        'metadata',
        'created_by_type',
        'created_by_id',
    ];

    protected $casts = [
        'start_date' => 'date',
        'end_date' => 'date',
        'recurrence_end_date' => 'date',
        'promotional_expires_at' => 'datetime',
        'approved_at' => 'datetime',
        'price' => 'decimal:2',
        'adjustment_value' => 'decimal:2',
        'is_active' => 'boolean',
        'is_promotional' => 'boolean',
        'priority' => 'integer',
        'override_priority' => 'integer',
        'days_of_week' => 'array',
        'recurrence_config' => 'array',
        'metadata' => 'array',
        'min_nights' => 'integer',
        'max_nights' => 'integer',
    ];

    public function room(): BelongsTo
    {
        return $this->belongsTo(Room::class, 'room_id');
    }

    public function roomCategory(): BelongsTo
    {
        return $this->belongsTo(RoomCategory::class, 'room_category_id');
    }

    /**
     * Scope to get active prices
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get prices for a specific date range
     */
    public function scopeForDateRange($query, $startDate, $endDate)
    {
        return $query->where(function ($q) use ($startDate, $endDate) {
            $q->where('start_date', '<=', $endDate)
              ->where('end_date', '>=', $startDate);
        });
    }

    /**
     * Scope to get prices by priority (highest first)
     */
    public function scopeByPriority($query)
    {
        return $query->orderBy('priority', 'desc');
    }

    /**
     * Check if this price applies to a specific date
     */
    public function appliesTo($date): bool
    {
        $date = Carbon::parse($date);

        // Check date range
        if ($date->lt($this->start_date) || $date->gt($this->end_date)) {
            return false;
        }

        // Check day of week if specified
        if ($this->days_of_week && !in_array($date->dayOfWeek, $this->days_of_week)) {
            return false;
        }

        return $this->is_active;
    }

    /**
     * Calculate the final price based on pricing type
     */
    public function calculatePrice($basePrice): float
    {
        switch ($this->pricing_type) {
            case 'fixed':
                return $this->price;

            case 'percentage':
                return $basePrice + ($basePrice * $this->adjustment_value / 100);

            case 'amount_adjust':
                return $basePrice + $this->adjustment_value;

            default:
                return $this->price;
        }
    }

    /**
     * Check if the stay duration meets requirements
     */
    public function meetsStayRequirements($nights): bool
    {
        if ($this->min_nights && $nights < $this->min_nights) {
            return false;
        }

        if ($this->max_nights && $nights > $this->max_nights) {
            return false;
        }

        return true;
    }

    /**
     * Get a human-readable description of the pricing rule
     */
    public function getDescriptionAttribute($value): string
    {
        if ($value) {
            return $value;
        }

        $description = $this->name ?: 'Custom Pricing';

        if ($this->days_of_week) {
            $days = collect($this->days_of_week)->map(function ($day) {
                return Carbon::create()->startOfWeek()->addDays($day)->format('D');
            })->join(', ');
            $description .= " (applies on: $days)";
        }

        if ($this->min_nights || $this->max_nights) {
            $nights = [];
            if ($this->min_nights) $nights[] = "min {$this->min_nights} nights";
            if ($this->max_nights) $nights[] = "max {$this->max_nights} nights";
            $description .= " (" . implode(', ', $nights) . ")";
        }

        return $description;
    }

    /**
     * Scope to get approved prices only
     */
    public function scopeApproved($query)
    {
        return $query->where('approval_status', self::APPROVAL_APPROVED);
    }

    /**
     * Scope to get future prices (start date is in the future)
     */
    public function scopeFuture($query)
    {
        return $query->where('start_date', '>', Carbon::now()->toDateString());
    }

    /**
     * Scope to get promotional prices
     */
    public function scopePromotional($query)
    {
        return $query->where('is_promotional', true)
                    ->where(function ($q) {
                        $q->whereNull('promotional_expires_at')
                          ->orWhere('promotional_expires_at', '>', Carbon::now());
                    });
    }

    /**
     * Scope to get prices by category
     */
    public function scopeByCategory($query, $category)
    {
        return $query->where('pricing_category', $category);
    }

    /**
     * Scope to get prices for specific room category
     */
    public function scopeForRoomCategory($query, $roomCategoryId)
    {
        return $query->where(function ($q) use ($roomCategoryId) {
            $q->where('pricing_category', self::CATEGORY_ROOM_CATEGORY)
              ->where('room_category_id', $roomCategoryId);
        })->orWhere('pricing_category', self::CATEGORY_ALL_ROOMS);
    }

    /**
     * Scope to get future pricing (is_future_pricing = true)
     */
    public function scopeFuturePricing($query)
    {
        if (\Schema::hasColumn('ht_room_prices', 'is_future_pricing')) {
            return $query->where('is_future_pricing', true);
        }
        return $query;
    }

    /**
     * Scope to get seasonal pricing by type
     */
    public function scopeSeasonal($query, $seasonType = null)
    {
        if (\Schema::hasColumn('ht_room_prices', 'season_type')) {
            if ($seasonType) {
                return $query->where('season_type', $seasonType);
            }
            return $query->whereNotNull('season_type');
        }
        return $query;
    }

    /**
     * Check if this pricing rule requires approval
     */
    public function requiresApproval(): bool
    {
        // Require approval for significant price increases (>50%)
        if ($this->room && $this->room->price > 0) {
            $increase = (($this->price - $this->room->price) / $this->room->price) * 100;
            if ($increase > 50) {
                return true;
            }
        }

        // Require approval for promotional pricing
        if ($this->is_promotional) {
            return true;
        }

        return false;
    }

    /**
     * Check if this price is currently valid (not expired)
     */
    public function isCurrentlyValid(): bool
    {
        $now = Carbon::now();

        // Check if promotional pricing has expired
        if ($this->is_promotional && $this->promotional_expires_at && $now->gt($this->promotional_expires_at)) {
            return false;
        }

        // Check approval status
        if ($this->approval_status !== self::APPROVAL_APPROVED) {
            return false;
        }

        return $this->is_active;
    }

    /**
     * Generate recurring dates based on recurrence pattern
     */
    public function generateRecurringDates(): array
    {
        if ($this->recurrence_pattern === self::RECURRENCE_NONE) {
            return [['start_date' => $this->start_date, 'end_date' => $this->end_date]];
        }

        $dates = [];
        $current = Carbon::parse($this->start_date);
        $end = Carbon::parse($this->end_date);
        $recurrenceEnd = $this->recurrence_end_date ? Carbon::parse($this->recurrence_end_date) : Carbon::now()->addYear();
        $duration = $current->diffInDays($end);

        while ($current->lte($recurrenceEnd)) {
            $periodEnd = $current->copy()->addDays($duration);

            if ($periodEnd->lte($recurrenceEnd)) {
                $dates[] = [
                    'start_date' => $current->toDateString(),
                    'end_date' => $periodEnd->toDateString()
                ];
            }

            // Move to next recurrence
            switch ($this->recurrence_pattern) {
                case self::RECURRENCE_DAILY:
                    $current->addDay();
                    break;
                case self::RECURRENCE_WEEKLY:
                    $current->addWeek();
                    break;
                case self::RECURRENCE_MONTHLY:
                    $current->addMonth();
                    break;
                case self::RECURRENCE_YEARLY:
                    $current->addYear();
                    break;
            }
        }

        return $dates;
    }

    /**
     * Check for conflicts with other pricing rules
     */
    public function hasConflicts(): array
    {
        $conflicts = [];

        $query = self::where('id', '!=', $this->id)
                    ->active()
                    ->approved();

        // Check for date overlaps
        if ($this->pricing_category === self::CATEGORY_INDIVIDUAL_ROOM && $this->room_id) {
            $query->where('room_id', $this->room_id);
        } elseif ($this->pricing_category === self::CATEGORY_ROOM_CATEGORY && $this->room_category_id) {
            $query->where('room_category_id', $this->room_category_id);
        }

        $overlapping = $query->forDateRange($this->start_date, $this->end_date)->get();

        foreach ($overlapping as $price) {
            $conflicts[] = [
                'type' => 'date_overlap',
                'conflicting_price' => $price,
                'message' => "Overlaps with '{$price->name}' from {$price->start_date} to {$price->end_date}"
            ];
        }

        return $conflicts;
    }

    /**
     * Get effective priority (considering override priority)
     */
    public function getEffectivePriority(): int
    {
        return $this->override_priority ?: $this->priority;
    }
}
