<?php

return [
    [
        'name' => 'Rooms',
        'flag' => 'room.index',
    ],
    [
        'name' => 'Create',
        'flag' => 'room.create',
        'parent_flag' => 'room.index',
    ],
    [
        'name' => 'Edit',
        'flag' => 'room.edit',
        'parent_flag' => 'room.index',
    ],
    [
        'name' => 'Delete',
        'flag' => 'room.destroy',
        'parent_flag' => 'room.index',
    ],
    [
        'name' => 'Room Availability',
        'flag' => 'room.availability',
        'parent_flag' => 'room.index',
    ],

    [
        'name' => 'Amenities',
        'flag' => 'amenity.index',
    ],
    [
        'name' => 'Create',
        'flag' => 'amenity.create',
        'parent_flag' => 'amenity.index',
    ],
    [
        'name' => 'Edit',
        'flag' => 'amenity.edit',
        'parent_flag' => 'amenity.index',
    ],
    [
        'name' => 'Delete',
        'flag' => 'amenity.destroy',
        'parent_flag' => 'amenity.index',
    ],

    [
        'name' => 'Food',
        'flag' => 'food.index',
    ],
    [
        'name' => 'Create',
        'flag' => 'food.create',
        'parent_flag' => 'food.index',
    ],
    [
        'name' => 'Edit',
        'flag' => 'food.edit',
        'parent_flag' => 'food.index',
    ],
    [
        'name' => 'Delete',
        'flag' => 'food.destroy',
        'parent_flag' => 'food.index',
    ],

    [
        'name' => 'Food types',
        'flag' => 'food-type.index',
    ],
    [
        'name' => 'Create',
        'flag' => 'food-type.create',
        'parent_flag' => 'food-type.index',
    ],
    [
        'name' => 'Edit',
        'flag' => 'food-type.edit',
        'parent_flag' => 'food-type.index',
    ],
    [
        'name' => 'Delete',
        'flag' => 'food-type.destroy',
        'parent_flag' => 'food-type.index',
    ],

    [
        'name' => 'Booking',
        'flag' => 'booking.index',
    ],
    [
        'name' => 'Advanced Booking Reports',
        'flag' => 'booking.advance-reports.index',
        'parent_flag' => 'booking.index',
    ],
    [
        'name' => 'Edit',
        'flag' => 'booking.edit',
        'parent_flag' => 'booking.index',
    ],
    [
        'name' => 'Delete',
        'flag' => 'booking.destroy',
        'parent_flag' => 'booking.index',
    ],
    [
        'name' => 'Add Additional Services',
        'flag' => 'booking.additional-services',
        'parent_flag' => 'booking.index',
    ],
    [
        'name' => 'View Invoice',
        'flag' => 'booking.view-invoice',
        'parent_flag' => 'booking.index',
    ],
    [
        'name' => 'Download Invoice',
        'flag' => 'booking.download-invoice',
        'parent_flag' => 'booking.index',
    ],

    [
        'name' => 'Booking Reports',
        'flag' => 'booking.reports.index',
        'parent_flag' => 'booking.index',
    ],

    [
        'name' => 'Bookings Calendar',
        'flag' => 'booking.calendar.index',
    ],

    [
        'name' => 'Booking addresses',
        'flag' => 'booking-address.index',
    ],
    [
        'name' => 'Create',
        'flag' => 'booking-address.create',
        'parent_flag' => 'booking-address.index',
    ],
    [
        'name' => 'Edit',
        'flag' => 'booking-address.edit',
        'parent_flag' => 'booking-address.index',
    ],
    [
        'name' => 'Delete',
        'flag' => 'booking-address.destroy',
        'parent_flag' => 'booking-address.index',
    ],

    [
        'name' => 'Booking rooms',
        'flag' => 'booking-room.index',
    ],
    [
        'name' => 'Create',
        'flag' => 'booking-room.create',
        'parent_flag' => 'booking-room.index',
    ],
    [
        'name' => 'Edit',
        'flag' => 'booking-room.edit',
        'parent_flag' => 'booking-room.index',
    ],
    [
        'name' => 'Delete',
        'flag' => 'booking-room.destroy',
        'parent_flag' => 'booking-room.index',
    ],

    [
        'name' => 'Customers',
        'flag' => 'customer.index',
    ],
    [
        'name' => 'Create',
        'flag' => 'customer.create',
        'parent_flag' => 'customer.index',
    ],
    [
        'name' => 'Edit',
        'flag' => 'customer.edit',
        'parent_flag' => 'customer.index',
    ],
    [
        'name' => 'Delete',
        'flag' => 'customer.destroy',
        'parent_flag' => 'customer.index',
    ],

    [
        'name' => 'Room categories',
        'flag' => 'room-category.index',
    ],
    [
        'name' => 'Create',
        'flag' => 'room-category.create',
        'parent_flag' => 'room-category.index',
    ],
    [
        'name' => 'Edit',
        'flag' => 'room-category.edit',
        'parent_flag' => 'room-category.index',
    ],
    [
        'name' => 'Delete',
        'flag' => 'room-category.destroy',
        'parent_flag' => 'room-category.index',
    ],

    [
        'name' => 'Features',
        'flag' => 'feature.index',
    ],
    [
        'name' => 'Create',
        'flag' => 'feature.create',
        'parent_flag' => 'feature.index',
    ],
    [
        'name' => 'Edit',
        'flag' => 'feature.edit',
        'parent_flag' => 'feature.index',
    ],
    [
        'name' => 'Delete',
        'flag' => 'feature.destroy',
        'parent_flag' => 'feature.index',
    ],

    [
        'name' => 'Services',
        'flag' => 'service.index',
    ],
    [
        'name' => 'Create',
        'flag' => 'service.create',
        'parent_flag' => 'service.index',
    ],
    [
        'name' => 'Edit',
        'flag' => 'service.edit',
        'parent_flag' => 'service.index',
    ],
    [
        'name' => 'Delete',
        'flag' => 'service.destroy',
        'parent_flag' => 'service.index',
    ],

    [
        'name' => 'Places',
        'flag' => 'place.index',
    ],
    [
        'name' => 'Create',
        'flag' => 'place.create',
        'parent_flag' => 'place.index',
    ],
    [
        'name' => 'Edit',
        'flag' => 'place.edit',
        'parent_flag' => 'place.index',
    ],
    [
        'name' => 'Delete',
        'flag' => 'place.destroy',
        'parent_flag' => 'place.index',
    ],

    [
        'name' => 'Taxes',
        'flag' => 'tax.index',
    ],
    [
        'name' => 'Create',
        'flag' => 'tax.create',
        'parent_flag' => 'tax.index',
    ],
    [
        'name' => 'Edit',
        'flag' => 'tax.edit',
        'parent_flag' => 'tax.index',
    ],
    [
        'name' => 'Delete',
        'flag' => 'tax.destroy',
        'parent_flag' => 'tax.index',
    ],

    [
        'name' => 'Invoice Template',
        'flag' => 'invoice.template',
        'parent_flag' => 'hotel.settings',
    ],

    [
        'name' => 'Coupons',
        'flag' => 'coupons.index',
    ],
    [
        'name' => 'Create',
        'flag' => 'coupons.create',
        'parent_flag' => 'coupons.index',
    ],
    [
        'name' => 'Edit',
        'flag' => 'coupons.edit',
        'parent_flag' => 'coupons.index',
    ],
    [
        'name' => 'Delete',
        'flag' => 'coupons.destroy',
        'parent_flag' => 'coupons.index',
    ],
    [
        'name' => 'Hotel Settings',
        'flag' => 'hotel.settings',
    ],

    [
        'name' => 'Offline Booking',
        'flag' => 'booking.offline.index',
        'parent_flag' => 'booking.index',
    ],
    [
        'name' => 'Create Offline Booking',
        'flag' => 'booking.offline.create',
        'parent_flag' => 'booking.offline.index',
    ],
    [
        'name' => 'Manage Offline Bookings',
        'flag' => 'booking.offline.manage',
        'parent_flag' => 'booking.offline.index',
    ],
];






