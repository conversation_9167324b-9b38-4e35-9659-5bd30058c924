<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Only create tables if they don't exist
        if (!Schema::hasTable('ht_room_groups')) {
            Schema::create('ht_room_groups', function (Blueprint $table) {
                $table->id();
                $table->string('name');
                $table->string('code')->unique();
                $table->text('description')->nullable();
                $table->timestamps();
            });
        }

        if (!Schema::hasTable('ht_room_group_items')) {
            Schema::create('ht_room_group_items', function (Blueprint $table) {
                $table->id();
                $table->foreignId('room_group_id')->references('id')->on('ht_room_groups')->onDelete('cascade');
                $table->string('room_id_code');
                $table->timestamps();

                $table->unique(['room_group_id', 'room_id_code']);
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ht_room_group_items');
        Schema::dropIfExists('ht_room_groups');
    }
};
